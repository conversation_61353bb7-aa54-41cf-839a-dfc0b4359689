{% extends "admin/change_form.html" %}
{% load static %}
{% block extrahead %}{{ block.super }}

<style>

    #gm-custom-mapregiondropdown::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            background-color: #F5F5F5;
        }

        #gm-custom-mapregiondropdown::-webkit-scrollbar {
            width: 6px;
            background-color: #F5F5F5;
        }

        #gm-custom-mapregiondropdown::-webkit-scrollbar-thumb {
            border-radius: 10px;
            -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
            background-color: #555;
        }





    #gm-custom-mapfilter, #gm-custom-maplayers, #gm-custom-maplegend {
        -moz-box-shadow: 0 0 2px rgba(0,0,0,.1);
        -webkit-box-shadow: 0 0 2px rgba(0,0,0,.1);
        box-shadow: 0 0 0 2px rgba(0,0,0,.1);
    }

    #peak-search-3d {
        cursor: pointer;
        font-size: 16px;
        color: #333;
        font-weight: 700;
        line-height: 30px;
        text-align: center;
        margin-left: 4px;
    }

    @media screen and (min-width: 1024px) {
        #gm-custom-maplegend {
            bottom: 233px;
        }
        #gm-custom-map3d {
            bottom: 278px;
        }
    }

    @media screen and (min-width: 1px) and (max-width: 1023px) {
        #gm-custom-maplegend {
            top: 100px;
        }
        #gm-custom-map3d {
            top: 145px;
        }
    }

    .mapboxgl-ctrl {
        margin-bottom: 15px !important;
    }

    #gm-custom-mapdropdown, #gm-custom-mapbutton {
        opacity: 1;
        webkit-backdrop-filter: blur(5px);
        backdrop-filter: blur(5px);
    }

    #gm-custom-mapunits:hover {
        background-color: transparent !important;
    }

    #gm-custom-mapbutton, #gm-custom-mapdropdown {
        border: 2px solid rgba(0,0,0,0.15);
    }

    #gm-custom-mapbutton {
        width: 180px;
        margin-left: 90px;
        border-top-right-radius: 8px;
        border-top-left-radius: 8px;
        border-bottom-right-radius: 8px;
        border-bottom-left-radius: 8px;
    }

    #gm-custom-mapbutton:hover {
        border-bottom-right-radius: 0px;
        border-bottom-left-radius: 0px;
    }

    #gm-custom-mapdropdown {
        width: 190px;
        margin-left: 91px;
        border-bottom-right-radius: 8px;
        border-bottom-left-radius: 8px;
    }

    #gm-custom-mapoption-terrain, #gm-custom-mapoption-natatl, #gm-custom-mapoption-outdoors, #gm-custom-mapoption-streets, #gm-custom-mapoption-topo, #gm-custom-mapoption-satstreets, #gm-custom-mapoption-sat {
        width: 178px;
        margin-left: -2px;
        border-bottom: solid 1px #f2f2f2;
    }

    #gm-custom-mapoption-3d {
        width: 178px;
        margin-left: -2px;
        border-bottom: solid 1px #f2f2f2;
    }

    .gm-custom-mapoption-region {
            width: 260px;
            margin-left: -1px;
            border-bottom: solid 1px #f2f2f2;
        }

    .gm-custom-mapoption-region:hover {
            background-color: #ebebeb !important;
            color: #000 !important;
        }

    #gm-custom-mapoption-3d {
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
    }

    #gm-custom-mapoption-streets:hover {
        background-color: rgb(235, 235, 235) !important;
        color: rgb(0, 0, 0) !important;
    }

    #search-peaks-btn[disabled] {
        -webkit-text-fill-color: #fff;
        color: #fff;
    }



   @media screen and (max-width: 767px) and (min-width: 1px) {
       #gm-custom-mapregiondropdown {
                right: -315px;
                top: 30px;
                max-height: 480px;
            }


       #content-body {
           margin-top: 3px;
       }
       #peaks-map {
           top: 0px;
       }
       #gm-custom-maptype {
           right: 46% !important;
       }
       .mapboxgl-ctrl-bottom-right {
            bottom: 20px;
        }
       .mapboxgl-ctrl-geolocate, .mapboxgl-ctrl-fullscreen {
           width: 36px !important;
           height: 36px !important;
       }
       #gm-custom-maplegend, #gm-custom-maplayers, #gm-custom-mapfilter, #gm-custom-map3d {
           width: 36px !important;
           height: 36px !important;
           padding-top: 3px;
           padding-left: 3px;
       }
   }
    @media screen and (min-width: 768px) {
       #gm-custom-mapregiondropdown {
                right: -531px;
                top: 52px;
                max-height: 640px;
            }

       #content-body {
           margin-top: 3px;
       }
        #peak-search {
            top: 0px;
        }
        #gm-custom-maplegend-dropdown {
            width: 320px !important;
            left: 20px !important;
        }
    }
    @media screen and (min-width: 768px) and (max-width: 1023px) {
        #peaks-map {
            top: 0px;
        }
    }
    @media screen and (min-width: 1024px) {
        #peaks-map {
            top: 0px;
        }
        #gm-custom-mapunits {
            right: 172px;
        }
    }
    #content-holder {
        background-image: none;
    }

    #explore {
        padding-top: 0px;
    }

    .map-tooltip-info {
        background-color: rgba(0,0,0,.6);
        -webkit-backdrop-filter: blur(0px) !important;
    }

    .mapboxgl-ctrl-attrib {display: none !important;}

</style>

<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.1/jquery.min.js"></script>;
<script type="text/javascript">
$(document).ready(function(){
    $('.field-elevation_in_meters').find('label').html('Elevation (m):');
    $('.field-location_lat_lng').find('label').html('Location:');

    var change = 0;

    $('input, textarea').keypress(function(){
        change++;
    });
    $('select').change(function(){
        change++;
    });
    $('#approve_btn').bind("click", function(){
        if (change>0){
            if (confirm("You might have unsaved changes, are you sure you want to approve?")){
                location.href = "{% url "approve_temp_item" object_id %}";
            }
        }
        else {
            location.href = "{% url "approve_temp_item" object_id %}";
        }
    });
    $('#save_btn').bind("click", function(){
        $('input[name*="_continue"]').trigger('click');
    });

    initialize();

    //Custom Google Map type stuff

    $('#gm-custom-mapbutton').on('mouseenter', function(){
       $('#gm-custom-mapdropdown').show();
       $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
       $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapbutton').on('mouseleave', function(){
       $('#gm-custom-mapdropdown').hide();
       $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    $('#map-loading').on('mouseenter', function(){
       $('#gm-custom-mapdropdown').show();
       $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
       $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#map-loading').on('mouseleave', function(){
       $('#gm-custom-mapdropdown').hide();
       $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    $('#gm-custom-mapbutton').on('touchstart', function() {
        $('#gm-custom-mapdropdown').toggle();
    });

    //new MapBox 2.0 3D stuff
    var flag = false;
    $('#gm-custom-mapoption-3d').on('touchstart click', function(e) {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#0ae");
            $('#gm-custom-mapoption-3d').css("color", "#fff");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");

            toggleMapType('satellite_3d');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">3D Map</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-3d').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-3d').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-satstreets').on('touchstart click', function(e) {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#0ae");
            $('#gm-custom-mapoption-satstreets').css("color", "#fff");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");

            toggleMapType('satellite');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-satstreets').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-satstreets').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-topo').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
            $('#gm-custom-mapoption-topo').css("color", "#fff");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");

            toggleMapType('caltopo');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
        }
        return false
    });

    $('#gm-custom-mapoption-topo').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-topo').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-sat').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#0ae");
            $('#gm-custom-mapoption-sat').css("color", "#fff");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");

            toggleMapType('sat_topo');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite Topo</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-sat').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-sat').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-terrain').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#0ae");
            $('#gm-custom-mapoption-terrain').css("color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");

            toggleMapType('terrain');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-terrain').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-terrain').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-outdoors').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#0ae");
            $('#gm-custom-mapoption-outdoors').css("color", "#fff");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");

            toggleMapType('outdoors');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">OpenTopoMap</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-outdoors').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-outdoors').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-streets').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#0ae");
            $('#gm-custom-mapoption-streets').css("color", "#fff");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");

            toggleMapType('streets');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-streets').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-streets').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    //Natural atlas stuff
    var flag = false;
    $('#gm-custom-mapoption-natatl').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#0ae");
            $('#gm-custom-mapoption-natatl').css("color", "#fff");

            toggleMapType('natural_atlas');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural Atlas <span style="font-size: 10px;">(US)</span></div>');
        }
        return false
    });

    $('#gm-custom-mapoption-natatl').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-natatl').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

});
</script>

{% include "mapbox/mapbox.html" %}
{% include "mapbox/map_layers.html" %}
<script type="text/javascript">

    function createCookie(name, value, days) {
        var expires;

        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toGMTString();
        } else {
            expires = "";
        }
        document.cookie = encodeURIComponent(name) + "=" + encodeURIComponent(value) + expires + "; path=/";
    }

    function readCookie(name) {
        var nameEQ = encodeURIComponent(name) + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
        }
        return null;
    }

    function eraseCookie(name) {
        createCookie(name, "", -1);
    }

    function initialize() {

        var mapDiv = document.getElementById('map-canvas');
        var latLng = new mapboxgl.LngLat({{ original.long }}, {{ original.lat }});

        var LatLngList = [];

        {% if original.is_usa_but_not_alaska %}
        var mapType = 'natural_atlas';
        {% else %}
        var mapType = 'outdoors';
        {% endif %}
        initMapType(mapType);

        var initZoom = 14;

        map = new mapboxgl.Map({
            container: mapDiv, // HTML container id
            style: 'mapbox://styles/peakery/cjjkkx6qa63mn2rthodesuu8m', // style URL
            center: latLng, // starting position as [lng, lat]
            zoom: initZoom,
            interactive: true
        });
        scale = new mapboxgl.ScaleControl({maxWidth: 120, unit: 'imperial'});
        map.addControl(scale, 'bottom-right');
        var nav = new mapboxgl.NavigationControl({showCompass: false});
        map.addControl(nav, 'bottom-right');

        map.on('load', function () {

            var latLng = new mapboxgl.LngLat({{ original.long }}, {{ original.lat }});
            var marker = new mapboxgl.Marker({
                    draggable: false,
                    color: '#ff0000'
                })
                .setLngLat([{{ original.long }}, {{ original.lat }}])
                .addTo(map);
            LatLngList.push(latLng);

            {% if original.related_item_1 %}
            var latLng = new mapboxgl.LngLat({{ original.related_item_1.long }}, {{ original.related_item_1.lat }});
            var marker = new mapboxgl.Marker({
                    draggable: false,
                    color: '#0000ff'
                })
                .setLngLat([{{ original.related_item_1.long }}, {{ original.related_item_1.lat }}])
                .addTo(map);
            LatLngList.push(latLng);
            {% endif %}

            {% if original.related_item_2 %}
            var latLng = new mapboxgl.LngLat({{ original.related_item_2.long }}, {{ original.related_item_2.lat }});
            var marker = new mapboxgl.Marker({
                    draggable: false,
                    color: '#0000ff'
                })
                .setLngLat([{{ original.related_item_2.long }}, {{ original.related_item_2.lat }}])
                .addTo(map);
            LatLngList.push(latLng);
            {% endif %}

            {% if original.related_item_3 %}
            var latLng = new mapboxgl.LngLat({{ original.related_item_3.long }}, {{ original.related_item_3.lat }});
            var marker = new mapboxgl.Marker({
                    draggable: false,
                    color: '#0000ff'
                })
                .setLngLat([{{ original.related_item_3.long }}, {{ original.related_item_3.lat }}])
                .addTo(map);
            LatLngList.push(latLng);
            {% endif %}

            var bounds = new mapboxgl.LngLatBounds();
            for (var i = 0, LtLgLen = LatLngList.length; i < LtLgLen; i++) {
                bounds.extend(LatLngList[i]);
            }
            //map.fitBounds(bounds, {padding: 150, duration: 0});
            map.setCenter([{{ original.long }}, {{ original.lat }}]);
            toggleMapUnits('feet');
            toggleMapType('terrain');
        });

        $('#map-canvas').insertAfter($('#id_location_lat_lng'));
        $('#id_location_lat_lng').hide();

    }

    function setMapControls() {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                setMapControls();
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        var mapUnits = readCookie('map_units');
        if (mapUnits == 'meters') {
            toggleMapUnits(mapUnits);
        }
    }

    function addExtraMapLayers() {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                addExtraMapLayers();
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        // No extra map layers necessary
    }

</script>

<div id="map-canvas" style="width: 800px; height: 500px;">
    <div id="gm-custom-maptype" class="gmnoprint"
                             style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 180px; right: 50%; top: 0px;">
                            <div id="gm-custom-mapbutton" class="hidden-xs hidden-sm" draggable="false"
                                 style="cursor: pointer; direction: ltr; overflow: hidden; text-align: left; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; -webkit-background-clip: padding-box; background-clip: padding-box; font-weight: 500;">
                                <div style="float: left;"><img
                                        style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                        src="{% static '' %}img/<EMAIL>">
                                </div>
                                <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>
                            </div>
                        </div>
                        <div id="gm-custom-mapdropdown-container" class="gmnoprint"
                             style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; right: 50%; margin-right: 191px; top: 0px;">
                            <div id="gm-custom-mapdropdown"
                                 style="z-index: 10; padding-left: 2px; padding-right: 2px; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; position: absolute; top: 50px; left: 0px; right: 0px; text-align: left; display: none;">
                                <div id="gm-custom-mapoption-terrain" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>
                                </div>
                                <div id="gm-custom-mapoption-natatl" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural Atlas
                                        <span style="font-size: 10px;">(US)</span></div>
                                </div>
                                <div id="gm-custom-mapoption-outdoors" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">OpenTopoMap
                                    </div>
                                </div>
                                <div id="gm-custom-mapoption-streets" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets</div>
                                </div>
                                <div id="gm-custom-mapoption-topo" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500; width: 177px; border-right: 1px solid #aaa;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Government Topo
                                        <span style="font-size: 10px;">&nbsp;<i class="fa fa-caret-right"
                                                                                aria-hidden="true"></i></span></div>
                                </div>
                                <div id="gm-custom-mapoption-satstreets" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite</div>
                                </div>
                                <div id="gm-custom-mapoption-sat" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite Topo
                                    </div>
                                </div>
                                <div class="" id="gm-custom-mapoption-3d" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>"></div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">3D Map</div>
                                </div>
                            </div>
                            <div id="gm-custom-mapregiondropdown"
                                 style="height: 410px; z-index: 10; padding-left: 0px; padding-right: 2px; position: absolute; text-align: left; overflow-y: auto; display: none;">
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-argentina-ign-50k"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/ar.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Argentina (IGN -
                                        50K)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-argentina-ign-100k"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/ar.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Argentina (IGN -
                                        100K)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-nsw"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/au.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - NSW
                                        (SIX)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-qld"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/au.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - QLD
                                        (QTopo)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-sa"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/au.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - SA
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-ts"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/au.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - TAS
                                        (LIST)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-vic"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/au.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - VIC
                                        (VicMap)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-austria-bergfex"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/at.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Austria
                                        (BergFex)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-austria-bev"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/at.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Austria (BEV)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-belgium-ngi"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/be.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Belgium (NGI)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-brazil"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/br.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Brazil (IBGE
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-canada-caltopo"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/ca.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada (NRCAN)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-canada-on"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/ca.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada - ON
                                        (OBM)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-canada-qc"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/ca.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada - QC
                                        (MERN)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-croatia-dgu"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/hr.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Croatia (DGU)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-czechia-cuzk"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/cz.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Czechia (ČÚZK)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-finland-nls"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/fi.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Finland (NLS)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-france-ign"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/fr.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">France (IGN)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-germany-oa"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/de.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Germany
                                        (OutdoorActive)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-hongkong-landsd"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/hk.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Hong Kong
                                        (LandsD)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-iceland-caltopo"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/is.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Iceland
                                        (CalTopo)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-iceland-new"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/is.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Iceland
                                        (Landmælingar)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-israel-hikingosm"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/il.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Israel (Hiking
                                        OSM)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-japan-gsi"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/jp.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Japan (GSI)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-luxembourg"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/lu.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Luxembourg
                                        (ACT)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-mexico-inegi"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/mx.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Mexico (INEGI)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-newzealand-linz"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/nz.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">New Zealand
                                        (LINZ)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-new"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/no.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway
                                        (Kartvertek)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-kartverket"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/no.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway
                                        (Old Kartvertek)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-janmayen"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/no.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway - Jan
                                        Mayen (NPI)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-svalbard"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/no.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway -
                                        Svalbard (NPI)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-philippines-namria"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/ph.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Philippines
                                        (NAMRIA)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-poland-geoportal"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/pl.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Poland
                                        (Geoportal)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-slovakia-dgu"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/sk.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Slovakia (DGU)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-slovenia-prostor"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/si.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Slovenia
                                        (ProStor)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-spain-ign"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/es.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Spain (IGN)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-spain-cataluna"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/es.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Spain - Cataluña
                                        (ICGC)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-sweden-sgu"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/se.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Sweden (SGU)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-switzerland-swisstopo"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/ch.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Switzerland
                                        (swisstopo)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-taiwan-nlsc"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/tw.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Taiwan (NLSC)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-uk-os" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/gb.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">United Kingdom
                                        (OS)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-us-caltopo"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/us.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">United States
                                        (USGS)
                                    </div>
                                </div>
                            </div>
                        </div>
                   </div>
    <div id="message_map_div" class="gmnoprint gm-style-mtc" style="opacity: 1; margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 400px; height: 100px; min-height: 100px; margin-left: -200px; left: 50%; bottom: 0px; display: none;">
        <div id="message_map" draggable="false" style="height: 100px; direction: ltr; overflow: hidden; text-align: left; position: relative; color: rgb(86, 86, 86); font-family: Roboto, Arial, sans-serif; font-size: 11px; background-color: transparent; padding: 11px; border-radius: 8px; font-weight: 500;">

        </div>
    </div>
</div>
<div id="marker-tooltip" data-url="" data-index="" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; cursor: pointer; opacity: 0; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>

{% endblock %}

{% block object-tools %}
    {% load tempitems_tags %}
    {% if change %}{% if not is_popup %}
        <ul class="object-tools">
{#            <li><a id='approve_btn' href="{% url "approve_temp_item" object_id %}">Approve</a></li>#}
            <li><a id='save_btn' href="#">Save changes</a></li>
            <li><a id='approve_btn' href="#">Approve</a></li>
            <li><a href="{% url "reject_temp_item" object_id %}">Reject</a></li>
            <li style="background: none;"><span style="margin: 0px 15px 0px 10px;color:#ccc">|</span></li>
            <li><a href="{% get_googlemap_url object_id %}" target="_blank">View in Maps</a></li>
            <li><a href="history/" class="historylink">History</a></li>
        </ul>
     {% endif %}{% endif %}

{% endblock %}


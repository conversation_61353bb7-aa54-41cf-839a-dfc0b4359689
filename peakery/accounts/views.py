import copy
import traceback
import math
import collections
from concurrent.futures import ThreadPoolExecutor
from django.core.exceptions import ValidationError
from django.views.decorators.cache import cache_page
from django.core.files.storage import default_storage
from django.contrib.auth.forms import PasswordChangeForm
from django.template.defaultfilters import slugify
from peakery.items.models import Item, ItemGroup, SummitLog
from peakery.cities.models import City
from django.http import Http404, JsonResponse
from peakery.accounts.forms import PersonForm, UserForm, PersonUserNameForm
from peakery.accounts.models import Person, UserRelation, PEAKERY_SOURCE
from peakery.accounts.forms import ExtendedUserCreationForm
from django.contrib.auth.decorators import login_required
from django.contrib.auth.forms import AuthenticationForm
from django.http import HttpResponseRedirect
from django.shortcuts import redirect
from django.shortcuts import render
from django.shortcuts import get_object_or_404
from django.contrib.auth import login as django_login
from django.contrib.auth import authenticate
import json
from django.contrib import messages
from follow.models import Follow
from peakery.notification.models import *
from peakery.notification.models import Notice
from django.http import HttpResponse
from django.db import connection
from django.template.defaultfilters import floatformat
from peakery.items.utils import commify
from django.contrib.auth import logout as django_logout
from peakery.cache import cache_manager
from peakery.main.redis_queue_singleton import redis_queue


def dictfetchall(cursor):
    "Return all rows from a cursor as a dict"
    columns = [col[0] for col in cursor.description]
    return [
        dict(zip(columns, row))
        for row in cursor.fetchall()
    ]

def detect_mobile(request):
    """
    Useful function to detect if the user is
    on a mobile device.
    """
    import re
    is_mobile = False;

    if 'HTTP_USER_AGENT' in request.META:
        user_agent = request.META['HTTP_USER_AGENT']

        # Test common mobile values.
        pattern = "(up.browser|up.link|mmp|symbian|smartphone|midp|wap|phone|windows ce|pda|mobile|mini|palm|netfront)"
        prog = re.compile(pattern, re.IGNORECASE)
        match = prog.search(user_agent)

        if match:
            is_mobile = True;
        else:
            # Nokia like test for WAP browsers.
            # http://www.developershome.com/wap/xhtmlmp/xhtml_mp_tutorial.asp?page=mimeTypesFileExtension

            if 'HTTP_ACCEPT' in request.META:
                http_accept = request.META['HTTP_ACCEPT']

                pattern = "application/vnd\.wap\.xhtml\+xml"
                prog = re.compile(pattern, re.IGNORECASE)

                match = prog.search(http_accept)

                if match:
                    is_mobile = True

        if not is_mobile:
            # Now we test the user_agent from a big list.
            user_agents_test = ("w3c ", "acs-", "alav", "alca", "amoi", "audi",
                                "avan", "benq", "bird", "blac", "blaz", "brew",
                                "cell", "cldc", "cmd-", "dang", "doco", "eric",
                                "hipt", "inno", "ipaq", "java", "jigs", "kddi",
                                "keji", "leno", "lg-c", "lg-d", "lg-g", "lge-",
                                "maui", "maxo", "midp", "mits", "mmef", "mobi",
                                "mot-", "moto", "mwbp", "nec-", "newt", "noki",
                                "xda",  "palm", "pana", "pant", "phil", "play",
                                "port", "prox", "qwap", "sage", "sams", "sany",
                                "sch-", "sec-", "send", "seri", "sgh-", "shar",
                                "sie-", "siem", "smal", "smar", "sony", "sph-",
                                "symb", "t-mo", "teli", "tim-", "tosh", "tsm-",
                                "upg1", "upsi", "vk-v", "voda", "wap-", "wapa",
                                "wapi", "wapp", "wapr", "webc", "winw", "winw",
                                "xda-",)

            test = user_agent[0:4].lower()
            if test in user_agents_test:
                is_mobile = True

    return is_mobile


def password_reset_done(request):
    return render(request, 'accounts/reset_password_done.html', {})


def login(request):
    redirect_to = request.REQUEST.get('next')

    if request.method == 'POST':
        form = AuthenticationForm(data=request.POST)
        try:
            if form.is_valid():
                redirect_summit = None
                if request.session.get('redirect_summit', None):
                    redirect_summit = request.session.get('redirect_summit', None)

                django_login(request, form.get_user())


                if request.session.test_cookie_worked():
                    request.session.delete_test_cookie()

                #success
                if not redirect_to or '//' in redirect_to or ' ' in redirect_to:
                    from settings import LOGIN_REDIRECT_URL
                    redirect_to = LOGIN_REDIRECT_URL

                if redirect_summit:
                    request.session['redirect_summit'] = redirect_summit

                return HttpResponseRedirect(redirect_to)
        except Exception:
            return None
    else:
        form = AuthenticationForm(request)
        request.session.set_test_cookie()

    return render(request, "accounts/login.html", {'form':form, 'next':redirect_to})


def logout(request):
    django_logout(request)
    return HttpResponseRedirect('/')


def register(request):
    action = request.GET.get('action')
    window = request.GET.get('window', '')
    next = request.GET.get('next', False)
    if request.method == 'POST':
        cp = copy.deepcopy(request.POST)
        cp.update({'password2':cp['password1']})
        #form = ExtendedUserCreationForm(request.POST)
        form = ExtendedUserCreationForm(cp)
        print(request.POST)
        print(form.is_valid())
        print(form.errors)

        if form.is_valid():
            new_user = form.save(commit=False)
            new_user.email = form.cleaned_data.get("email")
            new_user.username = generate_username(new_user)
            new_user.save()
            #source
            if request.POST.get('is_android', None) == 'true':
                signup_source = 'android-native'
            elif detect_mobile(request) == True:
                signup_source = 'web-mobile'
            else:
                signup_source = 'web-desktop'

            person = Person(user=new_user, signup_source=signup_source)
            person.save()
            redirect_summit = None

            if request.session.get('redirect_summit', None):
                redirect_summit = request.session.get('redirect_summit', None)

            userx = authenticate(username=new_user.username, password=form.cleaned_data.get("password1"))
            django_login(request, userx)

            if redirect_summit:
                request.session['redirect_summit'] = redirect_summit

            request.session['register_type'] = 'email'
            return_value = {'sucess':True, 'action':action, 'value':new_user.username, 'next':next}
            return_json = json.dumps(return_value)
            return HttpResponse(return_json, content_type='application/json')
    else:
        form = ExtendedUserCreationForm()

    return render(request, "accounts/sign_up_with_email.html", {'form':form, 'action':action, 'window':window, 'next':next})


def users_redirect(request, username=None):
    return redirect('/members/%s' % username)


def check_username_exists(request, username=None):
    if username:
        if User.objects.filter(username=username).exists():
            return JsonResponse({"exists": "true"})

    return JsonResponse({"exists": "false"})

@login_required
def edit_user_profile(request, username=None):

    from django.contrib.gis.geos.point import Point

    if username == request.user.username:
        if request.user.is_authenticated:
            bagger = request.user
        else:
            return HttpResponse("User doesn't exist")

    if request.method == 'POST':
        #process edit user form
        profile = bagger.person
        about_me = request.POST.get('about-user',None)
        favorite_item = request.POST.get('favorite_item',None)
        next_item_goal = request.POST.get('next_item_goal',None)
        new_email = request.POST.get('email',None)
        facebook_page = request.POST.get('facebook_page',None)
        twitter_username = request.POST.get('twitter_username',None)
        instagram_username = request.POST.get('instagram_username',None)
        flicker_page = request.POST.get('flicker_page',None)
        website = request.POST.get('website',None)
        location_name = request.POST.get('location_name',None)
        location_area_1 = request.POST.get('location_area_1',None)
        location_area_2 = request.POST.get('location_area_2',None)
        new_lat = request.POST.get('basecamp-lat',None)
        new_lng = request.POST.get('basecamp-lng',None)
        new_username = request.POST.get('new_username', None)

        if new_username:
            if User.objects.filter(username=new_username).exists():
                raise ValidationError("This username is already taken. Please choose a different one.")

        if new_lat and new_lng:
            new_location = Point(float(new_lng), float(new_lat))
            profile.location = new_location

        bagger.email = new_email
        bagger.save()

        profile.about_me = about_me
        profile.favorite_item = favorite_item
        profile.next_item_goal = next_item_goal
        profile.facebook_page = facebook_page
        profile.twitter_username = twitter_username
        profile.instagram_username = instagram_username
        profile.flicker_page = flicker_page
        profile.website = website
        profile.location_name = location_name
        profile.location_area_1 = location_area_1
        profile.location_area_2 = location_area_2
        profile.save()

        #remove existing avatar
        from avatar.models import Avatar
        avatar = Avatar.objects.filter(user=request.user)
        for a in avatar:
            a.delete()

        #have a new avatar?
        avatar_image = request.POST.get('avatar_image',None)
        if avatar_image:
            new_avatar = Avatar(user=request.user, primary=True)
            new_avatar.avatar = avatar_image
            new_avatar.save()

        # Refresh member profile
        cache_path = '/members/%s/' % request.user.username
        cache_manager.expire_view_cache(cache_path)
        from urllib.parse import urlparse
        from peakery.accounts.utils import update_member_profile_cache
        redis_queue.enqueue(update_member_profile_cache, request.user)
        cache_manager.delete_user_profile_stats(bagger.id)
        return HttpResponseRedirect('/members/%s/' % bagger)
    else:
        profile = bagger.person

        profile_stats = profile.get_profile_stats()

        #get avatar
        sql = "select " + \
            "a.id, " + \
            "replace(replace(b.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/') as avatar_url, " + \
            "replace(replace(b.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/') as big_avatar_url " + \
            "from auth_user a " + \
            "left join avatar_avatar b on b.user_id = a.id " + \
            "where a.id = %s "

        with connection.cursor() as cursor:
            cursor.execute(sql, [bagger.person.user.id])
            avatar = dictfetchall(cursor)[0]

        nav_page_name = bagger

    profile_response = {
        'bagger':bagger,
        'profile':profile,
        'profile_stats': profile_stats,
        'avatar': avatar,
        'nav_page_name': nav_page_name
    }

    return render(request, 'accounts/profile_edit.html', profile_response)

@login_required(login_url='/')
def edit_settings(request):

    if request.method == 'POST':

        notice_types = NoticeType.objects.all()
        for exclude_notification in NOTIFICATIONS_CONFIG['exclude']:
            notice_types = notice_types.exclude(label = exclude_notification)
        for notice_type in notice_types:
            for medium_id, medium_display in NOTICE_MEDIA:
                form_label = "%s_%s" % (notice_type.label, medium_id)
                setting = get_notification_setting(request.user, notice_type, medium_id)
                if request.method == "POST":
                    if request.POST.get(form_label) == "on":
                        if not setting.send:
                            setting.send = True
                            setting.save()
                    else:
                        if setting.send:
                            setting.send = False
                            setting.save()

        return_url = request.POST.get('previous_page',None)
        if return_url:
            return HttpResponseRedirect(return_url)
        else:
            return HttpResponseRedirect('/')

    else:

        notice_types = NoticeType.objects.all()
        for exclude_notification in NOTIFICATIONS_CONFIG['exclude']:
            notice_types = notice_types.exclude(label = exclude_notification)
        settings_table = []
        for notice_type in notice_types:
            settings_row = []
            for medium_id, medium_display in NOTICE_MEDIA:
                form_label = "%s_%s" % (notice_type.label, medium_id)
                setting = get_notification_setting(request.user, notice_type, medium_id)
                if request.method == "POST":
                    if request.POST.get(form_label) == "on":
                        if not setting.send:
                            setting.send = True
                            setting.save()

                    else:
                        if setting.send:
                            setting.send = False
                            setting.save()

                settings_row.append((form_label, setting.send))
            settings_table.append({"notice_type": notice_type, "cells": settings_row})

        notice_settings = {
            "column_headers": [medium_display for medium_id, medium_display in NOTICE_MEDIA],
            "rows": settings_table,
            }

        return render(request, "accounts/edit_settings.html", {
            'notice_types': notice_types,
            'notice_settings': notice_settings
        })


def get_user_profile_latest_challenges(user_id) -> list:
    # latest challenges
    sql = """
            select 
                a.id,
                a.name,
                a.slug,
                concat(replace(a.thumbnail, 'items/lists/', 'items/lists/cache/'), '.350x245_q95_crop.jpg') as thumbnail,
                d.total_peaks
            from items_itemgroup a
                     join items_itemgroupitem b on b.group_id = a.id
                     join items_summitlog c on c.item_id = b.item_id and c.status = 1 and c.attempt = false and c.user_id = %s
                     join (select x.group_id, count(x.id) as total_peaks from items_itemgroupitem x group by x.group_id) d
                          on d.group_id = a.id
            group by a.id, a.name, a.slug,
                     concat(replace(a.thumbnail, 'items/lists/', 'items/lists/cache/'), '.350x245_q95_crop.jpg'), d.total_peaks
            order by max(c.date) desc
            limit 5;
        """

    with connection.cursor() as cursor:
        cursor.execute(sql, [user_id])
        challenges = dictfetchall(cursor)

    if challenges:
        # summit counts per challenge
        sql = """
                select a.group_id, count(b.id) as summit_count
                from items_itemgroupitem a
                         left join items_summitlog b
                                   on b.item_id = a.item_id and b.status = 1 and b.attempt = false and b.user_id = %s
                where a.group_id IN %s
                group by a.item_id, a.group_id
                order by count(b.id) asc;
            """

        challenge_ids = [c['id'] for c in challenges]
        with connection.cursor() as cursor:
            cursor.execute(sql, [user_id, tuple(challenge_ids)])
            challenge_to_summit_count = collections.defaultdict(list)
            for row in cursor.fetchall():
                challenge_to_summit_count[row[0]].append(row[1])
    else:
        challenge_to_summit_count = {}

    latest_challenges = []
    for c in challenges:

        rounds_completed = 0
        status = []

        if c['id'] in challenge_to_summit_count:
            status = challenge_to_summit_count[c['id']]
            if status:
                rounds_completed = status[0]

        current_round_count = 0
        for round in status:
            if round > rounds_completed:
                current_round_count = current_round_count + 1

        current_round_pct = int(float(current_round_count) / float(c['total_peaks']) * 100)
        current_round_number = int(rounds_completed) + 1

        challenge = {'challenge': c,
                     'current_round_count': current_round_count,
                     'current_round_pct': current_round_pct,
                     'current_round_number': current_round_number}
        latest_challenges.append(challenge)

    return latest_challenges


def get_user_profile_top_companions(user_id) -> list:
    sql = """
            SELECT username, peak_count, avatar_url  
            FROM accounts_usertopfiveclimbedcompanions
            WHERE user_id = %s 
            ORDER BY peak_count DESC
        """

    with connection.cursor() as cursor:
        cursor.execute(sql, [user_id])
        top_companions = dictfetchall(cursor)
    return top_companions


def get_user_profile_top_ranges(user_id) -> list:
    sql = """
            SELECT range, peak_count  
            FROM accounts_usertopfiveclimbedranges
            WHERE user_id = %s 
            ORDER BY peak_count DESC
        """

    with connection.cursor() as cursor:
        cursor.execute(sql, [user_id])
        top_ranges = dictfetchall(cursor)
    return top_ranges


def get_user_profile_top_regions(user_id) -> list:
    sql = """
            SELECT region, country_slug, region_slug, peak_count  
            FROM accounts_usertopfiveclimbedregions
            WHERE user_id = %s 
            ORDER BY peak_count DESC
        """

    with connection.cursor() as cursor:
        cursor.execute(sql, [user_id])
        top_regions = dictfetchall(cursor)
    return top_regions


def get_user_profile_top_countries(user_id) -> list:
    sql = """
        SELECT country, slug, peak_count 
        FROM accounts_usertopfiveclimbedcountries 
        WHERE user_id = %s 
        ORDER BY peak_count DESC
    """

    with connection.cursor() as cursor:
        cursor.execute(sql, [user_id])
        top_countries = dictfetchall(cursor)
    return top_countries


def get_user_profile_favorite_item(profile: Person) -> dict:
    profile_favorite_item = profile.get_favorite_item()
    if profile_favorite_item:
        r = profile_favorite_item
        f = r.elevation
        m = floatformat(f * 0.3048, 0)
        elevation = '%s ft / %s m' % (commify(int(f)), commify(m))
        if r.thumbnail:
            hover_class = 'hover-photos'
            info_class = 'user-photo-info'
        else:
            hover_class = ''
            info_class = 'empty-photo-info'
        favorite_item = {'id': r.id, 'name': r.name, 'slug': r.slug_new_text, 'hover_class': hover_class,
                         'info_class': info_class, 'elevation': elevation, 'thumbnail_url': r.get_thumbnail_910}
    else:
        favorite_item = {'id': 0, 'name': '', 'slug': '', 'hover_class': '', 'info_class': '', 'elevation': '',
                         'thumbnail_url': settings.S3_URL + 'img/default.png'}

    return favorite_item


def get_user_profile_latest_summits(user_id) -> list:
    # Latest summits
    sql = "select " + \
          "aa.id, " + \
          "aa.date as summitlog_date, " + \
          "aa.attempt, " + \
          "a.id as peak_id, " + \
          "a.name as peak_name, " + \
          "a.slug_new_text as peak_slug, " + \
          "get_thumb(a.thumbnail, 480) as peak_thumbnail_url, " + \
          "a.elevation " + \
          "from items_summitlog aa " + \
          "join items_item a on a.id = aa.item_id " + \
          "where aa.user_id = %s and aa.status = 1 " + \
          "group by aa.id, aa.date, a.id, a.name, a.slug_new_text, a.thumbnail, a.elevation " + \
          "order by aa.date desc, aa.id desc limit 5 "

    with connection.cursor() as cursor:
        cursor.execute(sql, [user_id])
        summits = dictfetchall(cursor)

    if summits:
        # Get the last photo per summit_log_id
        sql = """
                 SELECT DISTINCT ON (a.summit_log_id)
                    a.summit_log_id,
                    get_thumb(a.image, 70) AS thumbnail_url
                FROM
                    items_itemphoto a
                WHERE
                    a.summit_log_id IN %s
                ORDER BY
                    a.summit_log_id, a.id;        
            """

        summit_log_ids = [s['id'] for s in summits]
        with connection.cursor() as cursor:
            cursor.execute(sql, [tuple(summit_log_ids)])
            photos = dictfetchall(cursor)
    else:
        photos = []

    if summits:
        # Get region_name for each peak
        sql = """
                SELECT DISTINCT ON (c.item_id) 
                    c.item_id,
                    case when b.code in ('US', 'CA', 'GB') then a.name else b.name end as region_name
                FROM cities_region a
                         join cities_country b on b.id = a.country_id
                         join items_item_region c on c.region_id = a.id
                WHERE c.item_id IN %s
                order by c.item_id, a.name
            """
        peak_ids = [s['peak_id'] for s in summits]
        with connection.cursor() as cursor:
            cursor.execute(sql, [tuple(peak_ids)])
            regions = dictfetchall(cursor)
    else:
        regions = []

    latest_summits = []

    for s in summits:
        summit_photo = s['peak_thumbnail_url']

        for photo in photos:
            if photo['summit_log_id'] == s['id']:
                summit_photo = photo['thumbnail_url']
                break

        summit_region = None

        for region in regions:
            if region['item_id'] == s['peak_id']:
                summit_region = region['region_name']
                break

        summit = {'summit': s,
                  'summit_photo': summit_photo,
                  'summit_elevation': Item.get_elevation_formatted(s['elevation']),
                  'summit_region': summit_region}
        latest_summits.append(summit)

    return latest_summits


def get_user_profile_completion_radius_data(bagger: User, profile: Person):
    # Nearest peak not summited by user
    completion_radius = cache_manager.get_user_completion_radius(bagger.id)
    if not completion_radius:
        completion_radius = profile.get_completion_radius_for_user()
        if completion_radius:
            completion_radius_km = int(math.floor(completion_radius / 1000))
            completion_radius_miles = int(math.floor(completion_radius * 0.000621371))
        else:
            completion_radius = 0
            completion_radius_km = 0
            completion_radius_miles = 0
        cache_manager.set_user_completion_radius(bagger.id, completion_radius)
    else:
        completion_radius_km = int(math.floor(completion_radius / 1000))
        completion_radius_miles = int(math.floor(completion_radius * 0.000621371))

    return completion_radius, completion_radius_km, completion_radius_miles


def get_user_profile_nearest_peak_not_submitted(bagger: User, profile: Person):
    # Nearest peak not summited
    nearest_peak_not_summited = cache_manager.get_user_nearest_peak_not_summited(bagger.id)
    if not nearest_peak_not_summited:
        if profile.location:
            sql = "select a.id, c.id as item_id, c.name, c.elevation, c.slug_new_text as slug, case when length(c.thumbnail) > 0 then 'hover-photos' else '' end as hover_class, case when length(c.thumbnail) > 0 then 'user-photo-info' else 'empty-photo-info' end as info_class, " + \
                  "get_thumb(c.thumbnail, 745) as thumbnail_url, " + \
                  "ST_DistanceSphere(st_makepoint(%s, %s),st_makepoint(c.long, c.lat)) as distance_in_meters " + \
                  "from accounts_person a, items_item c " + \
                  "where a.user_id = %s and a.location is not null and c.active = true " + \
                  "and c.summitlog_count = 0 " + \
                  "order by ST_SetSRID(ST_MakePoint(%s, %s), 4326) <#> c.location limit 1 "
            result = User.objects.raw(sql, [profile.location.x, profile.location.y, profile.user_id, profile.location.x,
                                            profile.location.y])
            for r in result:
                f = r.elevation
                m = floatformat(f * 0.3048, 0)
                elevation = '%s ft / %s m' % (commify(int(f)), commify(m))
                m = r.distance_in_meters
                km = float(m) / 1000
                mi = floatformat(km * 0.621371, 0)
                km = floatformat(km, 0)
                distance = '%s mi / %s km' % (commify(int(mi)), commify(km))
                nearest_peak_not_summited = {'id': r.id, 'name': r.name, 'slug': r.slug, 'hover_class': r.hover_class,
                                             'info_class': r.info_class, 'elevation': elevation, 'distance': distance,
                                             'thumbnail_url': r.thumbnail_url}
                cache_manager.set_user_nearest_peak_not_summited(bagger.id, nearest_peak_not_summited)

    return nearest_peak_not_summited


def get_user_profile_stats(bagger: User, profile: Person) -> dict:
    profile_stats = cache_manager.get_user_profile_stats(bagger.id)
    if not profile_stats:
        profile_stats = profile.get_profile_stats()
        cache_manager.set_user_profile_stats(bagger.id, profile_stats)
    return profile_stats


def get_user_profile_avatar(bagger: User) -> dict:
    sql = "select " + \
          "a.id, " + \
          "coalesce(replace(replace(b.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'),'img/default-user.png') as avatar_url, " + \
          "coalesce(replace(replace(b.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'),'img/default-user.png') as big_avatar_url " + \
          "from auth_user a " + \
          "left join avatar_avatar b on b.user_id = a.id " + \
          "where a.id = %s "

    with connection.cursor() as cursor:
        cursor.execute(sql, [bagger.person.user.id])
        avatar = dictfetchall(cursor)[0]
    return avatar


def get_user_profile_photos_added(bagger: User) -> list[dict]:
    sql = """
            WITH SummitPhotos AS (SELECT a.created,
                                  get_thumb(a.image, 910)                                         as thumbnail_url,
                                  concat(c.slug_new_text, '/summits/', cast(aa.id as varchar))    as slug,
                                  c.name                                                          as peak_name,
                                  ROW_NUMBER() OVER (PARTITION BY c.name ORDER BY a.id ASC, a.created DESC) AS RowNum
                           FROM items_summitlog aa,
                                items_itemphoto a,
                                items_item c
                           WHERE aa.id = a.summit_log_id
                             and a.user_id = %s
                             and a.summit_log_id is not null
                             and a.item_id = c.id
                           order by a.created desc)
            SELECT 
                created,
                thumbnail_url,
                slug,
                peak_name
            FROM
                SummitPhotos
            WHERE
                RowNum = 1
            limit 4
        """

    with connection.cursor() as cursor:
        cursor.execute(sql, [bagger.person.user.id])
        photos_added = dictfetchall(cursor)

    return photos_added


def get_user_profile_summit_stats(bagger: User) -> dict:
    # Vertical gain, distance
    summit_stats = cache_manager.get_user_summit_stats(bagger.id)
    if not summit_stats:
        sql = "select a.id, b.elevation_gain_all_time, b.total_distance_all_time, c.elevation_gain_90_days, c.total_distance_90_days, d.elevation_gain_30_days, d.total_distance_30_days " + \
              "from auth_user a " + \
              "left join (select x.id, coalesce(sum(y.elevation_gain), 0) as elevation_gain_all_time, coalesce(sum(y.total_distance), 0) as total_distance_all_time " + \
              "from auth_user x, items_summitlog y " + \
              "where x.id = y.user_id and y.status = 1 and y.attempt = false " + \
              "group by x.id) b on b.id = a.id " + \
              "left join (select x.id, coalesce(sum(y.elevation_gain), 0) as elevation_gain_90_days, coalesce(sum(y.total_distance), 0) as total_distance_90_days " + \
              "from auth_user x, items_summitlog y " + \
              "where x.id = y.user_id and y.status = 1 and y.attempt = false and date_entered = true and date >= (CURRENT_DATE - INTERVAL '90 day') " + \
              "group by x.id) c on c.id = a.id " + \
              "left join (select x.id, coalesce(sum(y.elevation_gain), 0) as elevation_gain_30_days, coalesce(sum(y.total_distance), 0) as total_distance_30_days " + \
              "from auth_user x, items_summitlog y " + \
              "where x.id = y.user_id and y.status = 1 and y.attempt = false and date_entered = true and date >= (CURRENT_DATE - INTERVAL '30 day') " + \
              "group by x.id) d on d.id = a.id " + \
              "where a.id = %s "

        result = User.objects.raw(sql, [bagger.person.user.id])
        for r in result:
            summit_stats = {'elevation_gain_all_time': r.elevation_gain_all_time,
                            'total_distance_all_time': r.total_distance_all_time,
                            'elevation_gain_90_days': r.elevation_gain_90_days,
                            'total_distance_90_days': r.total_distance_90_days,
                            'elevation_gain_30_days': r.elevation_gain_30_days,
                            'total_distance_30_days': r.total_distance_30_days}
            cache_manager.set_user_summit_stats(bagger.id, summit_stats)
    return summit_stats


def get_user_profile_summit_difficulty(bagger: User) -> dict:
    # Route difficulty
    summit_difficulty = cache_manager.get_user_summit_difficulty(bagger.id)
    if not summit_difficulty:
        sql = "select a.id, b.class_one_two_count, c.class_three_four_count, d.class_five_count " + \
              "from auth_user a " + \
              "left join (select x.id, count(distinct y.item_id) as class_one_two_count " + \
              "from auth_user x, items_summitlog y, items_peakroute z " + \
              "where x.id = y.user_id and y.status = 1 and y.attempt = false and y.peak_route_id = z.id and z.difficulty in ('Class 1','Class 2') " + \
              "group by x.id ) b on b.id = a.id " + \
              "left join (select x.id, count(distinct y.item_id) as class_three_four_count " + \
              "from auth_user x, items_summitlog y, items_peakroute z " + \
              "where x.id = y.user_id and y.status = 1 and y.attempt = false and y.peak_route_id = z.id and z.difficulty in ('Class 3','Class 4') " + \
              "group by x.id ) c on c.id = a.id " + \
              "left join (select x.id, count(distinct y.item_id) as class_five_count " + \
              "from auth_user x, items_summitlog y, items_peakroute z " + \
              "where x.id = y.user_id and y.status = 1 and y.attempt = false and y.peak_route_id = z.id and z.difficulty = 'Class 5' " + \
              "group by x.id ) d on d.id = a.id " + \
              "where a.id = %s "

        result = User.objects.raw(sql, [bagger.person.user.id])
        for r in result:
            summit_difficulty = {'class_one_two_count': r.class_one_two_count,
                                 'class_three_four_count': r.class_three_four_count,
                                 'class_five_count': r.class_five_count}
            cache_manager.set_user_summit_difficulty(bagger.id, summit_difficulty)

    return summit_difficulty


def get_user_profile_highest_peak(bagger: User, profile: Person) -> dict:
    highest_peak_bagged = cache_manager.get_user_highest_peak_bagged(bagger.id)
    if not highest_peak_bagged:
        sql = "select a.id, a.name, a.slug_new_text as slug, case when length(a.thumbnail) > 0 then 'hover-photos' else '' end as hover_class, case when length(a.thumbnail) > 0 then 'user-photo-info' else 'empty-photo-info' end as info_class, " + \
              "get_thumb(a.thumbnail, 745) as thumbnail_url " + \
              "from items_item a, items_summitlog b " + \
              "where b.user_id = %s and b.attempt = false and b.status = 1 " + \
              "and b.item_id = a.id " + \
              "order by a.elevation desc limit 1 "
        result = Item.objects.raw(sql, [profile.user_id])
        for r in result:
            f = r.elevation
            m = floatformat(f * 0.3048, 0)
            elevation = '%s ft / %s m' % (commify(int(f)), commify(m))

            feels_like = (-.0000000000001192870492 * (f * f * f)) + (.00000001416622325 * (f * f)) - (
                        .0007920654416 * f) + 20.8876173
            feels_like = floatformat((feels_like / 20.9) * 100, 0)

            highest_peak_bagged = {'id': r.id, 'name': r.name, 'slug': r.slug, 'hover_class': r.hover_class,
                                   'info_class': r.info_class, 'elevation': elevation, 'thumbnail_url': r.thumbnail_url,
                                   'feels_like': feels_like}
            cache_manager.set_user_highest_peak_bagged(bagger.id, highest_peak_bagged)
    return highest_peak_bagged


def get_user_profile_most_prominent_peak(bagger: User, profile: Person) -> dict:
    most_prominent_peak_bagged = cache_manager.get_user_most_prominent_peak_bagged(bagger.id)
    if not most_prominent_peak_bagged:
        sql = "select a.id, a.name, a.slug_new_text as slug, case when length(a.thumbnail) > 0 then 'hover-photos' else '' end as hover_class, case when length(a.thumbnail) > 0 then 'user-photo-info' else 'empty-photo-info' end as info_class, " + \
              "get_thumb(a.thumbnail, 745) as thumbnail_url " + \
              "from items_item a, items_summitlog b " + \
              "where b.user_id = %s and b.attempt = false and b.status = 1 " + \
              "and b.item_id = a.id " + \
              "and a.prominence is not null " + \
              "order by a.prominence desc limit 1 "
        result = Item.objects.raw(sql, [profile.user_id])
        for r in result:
            f = r.prominence
            m = floatformat(f * 0.3048, 0)
            elevation = '%s ft / %s m' % (commify(int(f)), commify(m))
            most_prominent_peak_bagged = {'id': r.id, 'name': r.name, 'slug': r.slug, 'hover_class': r.hover_class,
                                          'info_class': r.info_class, 'elevation': elevation,
                                          'thumbnail_url': r.thumbnail_url}
            cache_manager.set_user_most_prominent_peak_bagged(bagger.id, most_prominent_peak_bagged)
    return most_prominent_peak_bagged


def get_user_profile_most_submitted_peak(bagger: User, profile: Person) -> dict:
    most_summited_peak = cache_manager.get_user_most_summited_peak(bagger.id)
    if not most_summited_peak:
        sql = "select x.id, x.name, x.slug_new_text as slug, case when length(x.thumbnail) > 0 then 'hover-photos' else '' end as hover_class, case when length(x.thumbnail) > 0 then 'user-photo-info' else 'empty-photo-info' end as info_class, y.summit_count, " + \
              "get_thumb(x.thumbnail, 745) as thumbnail_url " + \
              "from items_item x, ( " + \
              "select a.item_id, count(a.id) as summit_count from items_summitlog a where a.user_id = %s and a.attempt = false and a.status = 1 group by a.item_id order by count(a.id) desc limit 1) y " + \
              "where y.item_id = x.id "
        result = Item.objects.raw(sql, [profile.user_id])
        for r in result:
            f = r.elevation
            m = floatformat(f * 0.3048, 0)
            elevation = '%s ft / %s m' % (commify(int(f)), commify(m))
            most_summited_peak = {'id': r.id, 'name': r.name, 'slug': r.slug, 'hover_class': r.hover_class,
                                  'info_class': r.info_class, 'elevation': elevation, 'summit_count': r.summit_count,
                                  'thumbnail_url': r.thumbnail_url}
            cache_manager.set_user_most_summited_peak(bagger.id, most_summited_peak)
    return most_summited_peak


def get_user_profile_nearest_peak(bagger: User, profile: Person) -> dict:
    # Nearest peak
    nearest_peak = cache_manager.get_user_nearest_peak(bagger.id)
    if not nearest_peak:
        if profile.location:
            sql = "select a.id, c.id as item_id, c.name, c.elevation, c.slug_new_text as slug, case when length(c.thumbnail) > 0 then 'hover-photos' else '' end as hover_class, case when length(c.thumbnail) > 0 then 'user-photo-info' else 'empty-photo-info' end as info_class, " + \
                  "get_thumb(c.thumbnail, 745) as thumbnail_url, " + \
                  "ST_DistanceSphere(st_makepoint(%s, %s),st_makepoint(c.long, c.lat)) as distance_in_meters " + \
                  "from accounts_person a, items_item c " + \
                  "where a.user_id = %s and a.location is not null and c.active = true " + \
                  "and not exists (select 1 from items_summitlog x where x.item_id = c.id and x.user_id = %s and x.attempt = false and x.status = 1) " + \
                  "order by ST_SetSRID(ST_MakePoint(%s, %s), 4326) <#> c.location limit 1 "
            result = User.objects.raw(sql, [profile.location.x, profile.location.y, profile.user_id, profile.user_id,
                                            profile.location.x, profile.location.y])
            for r in result:
                f = r.elevation
                m = floatformat(f * 0.3048, 0)
                elevation = '%s ft / %s m' % (commify(int(f)), commify(m))
                m = r.distance_in_meters
                km = float(m) / 1000
                mi = floatformat(km * 0.621371, 0)
                km = floatformat(km, 0)
                distance = '%s mi / %s km' % (commify(int(mi)), commify(km))
                nearest_peak = {'id': r.id, 'name': r.name, 'slug': r.slug, 'hover_class': r.hover_class,
                                'info_class': r.info_class, 'elevation': elevation, 'distance': distance,
                                'thumbnail_url': r.thumbnail_url}
                cache_manager.set_user_nearest_peak(bagger.id, nearest_peak)
    return nearest_peak


@cache_page(60 * 5)
def user_profile(request, username=None):

    if username is None:
        username = request.user.username

    if username == request.user.username:
        if request.user.is_authenticated:
            bagger = request.user
        else:
            return HttpResponse("User doesn't exist")
    else:
        bagger = get_object_or_404(User, username__iexact=username)

    try:
        profile = bagger.person
    except:
        raise Http404

    if not bagger.is_active:
        return HttpResponse("User doesn't exist")

    # Getting some data from the DB concurrently
    with ThreadPoolExecutor(max_workers=3) as executor:
        # Schedule tasks
        profile_stats_future = executor.submit(get_user_profile_stats, bagger, profile)
        avatar_future = executor.submit(get_user_profile_avatar, bagger)
        photos_added_future = executor.submit(get_user_profile_photos_added, bagger)
        summit_stats_future = executor.submit(get_user_profile_summit_stats, bagger)
        summit_difficulty_future = executor.submit(get_user_profile_summit_difficulty, bagger)
        highest_peak_bagged_future = executor.submit(get_user_profile_highest_peak, bagger, profile)
        most_prominent_peak_bagged_future = executor.submit(get_user_profile_most_prominent_peak, bagger, profile)
        most_summited_peak_future = executor.submit(get_user_profile_most_submitted_peak, bagger, profile)
        nearest_peak_future = executor.submit(get_user_profile_nearest_peak, bagger, profile)
        nearest_peak_not_summited_future = executor.submit(get_user_profile_nearest_peak_not_submitted, bagger, profile)
        completion_future = executor.submit(get_user_profile_completion_radius_data, bagger, profile)
        favorite_item_future = executor.submit(get_user_profile_favorite_item, profile)
        next_item_goal_future = executor.submit(profile.get_next_item_goal)
        latest_summits_future = executor.submit(get_user_profile_latest_summits, profile.user_id)
        latest_challenges_future = executor.submit(get_user_profile_latest_challenges, profile.user_id)
        top_countries_future = executor.submit(get_user_profile_top_countries, profile.user_id)
        top_regions_future = executor.submit(get_user_profile_top_regions, profile.user_id)
        top_ranges_future = executor.submit(get_user_profile_top_ranges, profile.user_id)
        top_companions_future = executor.submit(get_user_profile_top_companions, profile.user_id)

        # Wait for them to finish
        profile_stats = profile_stats_future.result()
        avatar = avatar_future.result()
        photos_added = photos_added_future.result()
        summit_stats = summit_stats_future.result()
        summit_difficulty = summit_difficulty_future.result()
        highest_peak_bagged = highest_peak_bagged_future.result()
        most_prominent_peak_bagged = most_prominent_peak_bagged_future.result()
        most_summited_peak = most_summited_peak_future.result()
        nearest_peak = nearest_peak_future.result()
        nearest_peak_not_summited = nearest_peak_not_summited_future.result()
        completion_radius, completion_radius_km, completion_radius_miles = completion_future.result()
        favorite_item = favorite_item_future.result()
        next_item_goal = next_item_goal_future.result()
        latest_summits = latest_summits_future.result()
        latest_challenges = latest_challenges_future.result()
        top_countries = top_countries_future.result()
        top_regions = top_regions_future.result()
        top_ranges = top_ranges_future.result()
        top_companions = top_companions_future.result()

    if profile_stats['peaks_bagged'] >= 200:
        peaks_bagged_color = '#CFB53B'
    elif profile_stats['peaks_bagged'] >= 100:
        peaks_bagged_color = '#C0C0C0'
    elif profile_stats['peaks_bagged'] >= 50:
        peaks_bagged_color = '#CD7F32'
    else:
        peaks_bagged_color = '#00B1F2'

    profile_response = {
        'bagger':bagger,
        'profile':profile,
        'top_companions':top_companions,
        'avatar': avatar,
        'photos_added': photos_added,
        'profile_stats': profile_stats,
        'peaks_bagged_color': peaks_bagged_color,
        'summit_stats': summit_stats,
        'summit_difficulty': summit_difficulty,
        'top_countries': top_countries,
        'top_regions': top_regions,
        'top_ranges': top_ranges,
        'highest_peak_bagged': highest_peak_bagged,
        'most_prominent_peak_bagged': most_prominent_peak_bagged,
        'most_summited_peak': most_summited_peak,
        'favorite_item': favorite_item,
        'next_item_goal': next_item_goal,
        'nearest_peak': nearest_peak,
        'nearest_peak_not_summited': nearest_peak_not_summited,
        'completion_radius': completion_radius,
        'completion_radius_km': completion_radius_km,
        'completion_radius_miles': completion_radius_miles,
        'subnav_info_style': 'font-weight: 500; color: #f24100;',
        'fixed_subnav_class': "",
        'nav_page_name': bagger,
        'latest_summits':latest_summits,
        'latest_challenges':latest_challenges
    }

    return render(request, 'accounts/profile.html', profile_response)


def user_summits(request, username=None):

    if username is None:
        username = request.user.username

    if username == request.user.username:
        if request.user.is_authenticated:
            bagger = request.user
        else:
            return HttpResponse("User doesn't exist")
    else:
        bagger = get_object_or_404(User, username__iexact=username)

    profile = bagger.person

    #get avatar
    sql = "select " + \
        "a.id, " + \
        "coalesce(replace(replace(b.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'),'img/default-user.png') as avatar_url " + \
        "from auth_user a " + \
        "left join avatar_avatar b on b.user_id = a.id " + \
        "where a.id = %s "

    with connection.cursor() as cursor:
        cursor.execute(sql, [bagger.person.user.id])
        avatar = dictfetchall(cursor)[0]

    sql = "select b.id, case when length(c.name) > 0 then concat(b.name,', ',c.name) else b.name end as region_name, c.name as country_name, count(d.*) as summit_count " + \
        "from items_summitlog d " + \
        "join items_item_region a on a.item_id = d.item_id " + \
        "join cities_region b on b.id = a.region_id " + \
        "left join cities_country c on c.id = b.country_id " + \
        "where d.user_id = %s " + \
        "and d.status = 1 " + \
        "group by b.id, b.name, c.name " + \
        "order by b.name, c.name "
    with connection.cursor() as cursor:
        cursor.execute(sql, [bagger.person.user.id])
        regions = dictfetchall(cursor)

    count_sql = "select 0 as sort_order, to_char(aa.date, 'YYYY') as year, to_char(aa.date, 'YYYY') as year_name, count(aa.id) as summits_count " + \
                "from items_summitlog aa " + \
                "join auth_user ab on ab.id = aa.user_id " + \
                "join items_item a on a.id = aa.item_id " + \
                "where aa.user_id = %s and aa.status = 1 and date_entered = true " + \
                "group by to_char(aa.date, 'YYYY') " + \
        "union " + \
            "select 1 as sort_order, 'Unknown' as year, 'Unknown' as year_name, count(aa.id) as summits_count " + \
                "from items_summitlog aa " + \
                "join auth_user ab on ab.id = aa.user_id " + \
                "join items_item a on a.id = aa.item_id " + \
                "where aa.user_id = %s and aa.status = 1 and date_entered = false " + \
                "group by sort_order, year, year_name " + \
        "order by sort_order, year_name "

    with connection.cursor() as cursor:
        cursor.execute(count_sql, [bagger.person.user.id, bagger.person.user.id])
        years = dictfetchall(cursor)

    following = False
    if request.user.is_authenticated:
        following = profile.is_user_following(request.user)

    subnav_summits_style = 'color: #f24100;'

    nav_page_name = bagger

    return render(request, 'accounts/user_summits.html', {
        'bagger':bagger,
        'profile':profile,
        'avatar': avatar,
        'regions': regions,
        'years': years,
        'following': following,
        'subnav_summits_style': subnav_summits_style,
        'nav_page_name': nav_page_name,
        'NO_PEAK_ID': getattr(settings, "NO_PEAK_ID")
    })

def user_badges(request, username=None):

    if username is None:
        username = request.user.username

    if username == request.user.username:
        if request.user.is_authenticated:
            bagger = request.user
        else:
            return HttpResponse("User doesn't exist")
    else:
        bagger = get_object_or_404(User, username__iexact=username)

    profile = bagger.person

    #get avatar
    sql = "select " + \
        "a.id, " + \
        "coalesce(replace(replace(b.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'),'img/default-user.png') as avatar_url " + \
        "from auth_user a " + \
        "left join avatar_avatar b on b.user_id = a.id " + \
        "where a.id = %s "

    with connection.cursor() as cursor:
        cursor.execute(sql, [bagger.person.user.id])
        avatar = dictfetchall(cursor)[0]

    sql = "select aa.* from (select distinct x.id, y.peak_id, y.max_summit_date, yy.name, yy.slug_new_text as slug, yy.elevation, coalesce(yy.prominence,0) as prominence, " + \
        "get_thumb(yy.thumbnail, 480) as thumbnail_url, " + \
        "y.summitlog_count, case when y.summitlog_count >= 5 then 1 else 0 end as summit_steward, case when z.peak_id > 0 then 1 else 0 end as kom, case when zz.peak_id > 0 then 1 else 0 end as first_ascent " + \
        "from auth_user x, accounts_usersummits_view y left join ( " + \
            "select distinct x.id, y.id as peak_id, z.summitlog_count, z.min_summit_date " + \
            "from auth_user x, items_item y, accounts_usersummits_view z " + \
            "where x.id = z.user_id " + \
            "and y.id = z.peak_id " + \
            "and x.id = %s " + \
            "and not exists (select 1 from accounts_usersummits_view zz where zz.user_id <> z.user_id and zz.summitlog_count = z.summitlog_count and zz.max_summit_date < z.max_summit_date and zz.peak_id = z.peak_id) " + \
            "and not exists (select 1 from accounts_usersummits_view zz where zz.user_id <> z.user_id and zz.summitlog_count = z.summitlog_count and zz.max_summit_date = z.max_summit_date and zz.max_summit_id < z.max_summit_id and zz.peak_id = z.peak_id) " + \
            "and not exists (select 1 from accounts_usersummits_view zz where zz.user_id <> z.user_id and zz.summitlog_count > z.summitlog_count and zz.peak_id = z.peak_id) " + \
        ") z on z.peak_id = y.peak_id " + \
        "left join ( " + \
        "select x.id, y.item_id as peak_id " + \
        "from auth_user x, items_summitlog y, items_item z " + \
        "where x.id = %s " + \
        "and x.id = y.user_id and y.attempt = false and y.status = 1 " + \
        "and y.item_id = z.id " + \
        "and not exists ( " + \
            "select 1 from items_summitlog zz " + \
            "where z.id = zz.item_id and zz.attempt = false and zz.status = 1 " + \
            "and x.id <> zz.user_id " + \
            "and zz.created < y.created)) zz on zz.peak_id = y.peak_id " + \
        "join items_item yy on yy.id = y.peak_id and yy.deleted = false " + \
        "where x.id = %s and x.id = y.user_id " + \
        ") aa "

    sql_params = []
    sql_params.append(bagger.person.user.id)
    sql_params.append(bagger.person.user.id)
    sql_params.append(bagger.person.user.id)

    with connection.cursor() as cursor:
        cursor.execute(sql, sql_params)
        badges_count = dictfetchall(cursor)
    kom_count = 0
    summit_steward_count = 0
    first_ascent_count = 0
    all_badges_count = 0
    for b in badges_count:
        all_badges_count = all_badges_count + 1
        if b['kom'] != 0:
            kom_count = kom_count + 1
        if b['summit_steward'] != 0:
            summit_steward_count = summit_steward_count + 1
        if b['first_ascent'] != 0:
            first_ascent_count = first_ascent_count + 1

    following = False
    if request.user.is_authenticated:
        following = profile.is_user_following(request.user)

    subnav_badges_style = 'color: #f24100;'

    nav_page_name = bagger

    return render(request, 'accounts/user_badges.html', {
        'bagger':bagger,
        'profile':profile,
        'avatar': avatar,
        'following': following,
        'subnav_badges_style': subnav_badges_style,
        'nav_page_name': nav_page_name,
        'all_badges_count': all_badges_count,
        'kom_count': kom_count,
        'summit_steward_count': summit_steward_count,
        'first_ascent_count': first_ascent_count
    })

def user_summit_badges(request, username=None):

    import time
    start_time = time.time()

    if username is None:
        username = request.user.username

    if username == request.user.username:
        if request.user.is_authenticated:
            bagger = request.user
        else:
            return HttpResponse("User doesn't exist")
    else:
        bagger = get_object_or_404(User, username__iexact=username)

    if not request.session.get('show_badges', False):
        return HttpResponseRedirect(reverse('user_summits', kwargs={'username':username}))

    badges_summit_id = request.session.get('badges_summit_id', 0)
    if badges_summit_id == 0:
        return HttpResponseRedirect(reverse('user_summits', kwargs={'username':username}))
    else:
        badges_summit = SummitLog.objects.get(id=badges_summit_id)

    profile = bagger.person

    #get followers
    #followers = Follow.objects.get_followers_for_object(bagger)
    followers = bagger.person.get_followers_for_user()
    #followers = None

    #get avatar
    sql = "select " + \
        "a.id, " + \
        "coalesce(replace(replace(b.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'),'img/default-user.png') as avatar_url " + \
        "from auth_user a " + \
        "left join avatar_avatar b on b.user_id = a.id " + \
        "where a.id = %s "

    with connection.cursor() as cursor:
        cursor.execute(sql, [bagger.person.user.id])
        avatar = dictfetchall(cursor)[0]

    sql = "select b.id, case when length(c.name) > 0 then concat(b.name,', ',c.name) else b.name end as region_name, c.name as country_name, count(d.*) as summit_count " + \
        "from items_summitlog d " + \
        "join items_item_region a on a.item_id = d.item_id " + \
        "join cities_region b on b.id = a.region_id " + \
        "left join cities_country c on c.id = b.country_id " + \
        "where d.user_id = %s " + \
        "and d.status = 1 " + \
        "group by b.id, b.name, c.name " + \
        "order by b.name, c.name "
    with connection.cursor() as cursor:
        cursor.execute(sql, [bagger.person.user.id])
        regions = dictfetchall(cursor)

    count_sql = "select 0 as sort_order, to_char(aa.date, 'YYYY') as year, to_char(aa.date, 'YYYY') as year_name, count(aa.id) as summits_count " + \
                "from items_summitlog aa " + \
                "join auth_user ab on ab.id = aa.user_id " + \
                "join items_item a on a.id = aa.item_id " + \
                "where aa.user_id = %s and aa.status = 1 and date_entered = true " + \
                "group by to_char(aa.date, 'YYYY') " + \
        "union " + \
            "select 1 as sort_order, 'Unknown' as year, 'Unknown' as year_name, count(aa.id) as summits_count " + \
                "from items_summitlog aa " + \
                "join auth_user ab on ab.id = aa.user_id " + \
                "join items_item a on a.id = aa.item_id " + \
                "where aa.user_id = %s and aa.status = 1 and date_entered = false " + \
                "group by sort_order, year, year_name " + \
        "order by sort_order, year_name "

    with connection.cursor() as cursor:
        cursor.execute(count_sql, [bagger.person.user.id, bagger.person.user.id])
        years = dictfetchall(cursor)

    following = False
    if request.user.is_authenticated:
        following = profile.is_user_following(request.user)

    subnav_summits_style = 'color: #f24100;'

    nav_page_name = bagger

    #Need to get badges for other peaks?
    if badges_summit.summitlog_group_id:
        summits = SummitLog.objects.filter(summitlog_group_id=badges_summit.summitlog_group_id).order_by('id')
    else:
        summits = SummitLog.objects.filter(id=badges_summit.id).order_by('id')

    badge_challenges = None
    last_badge_index = 0
    summit_badges = []
    if bagger.id == request.user.id:
        from urllib.parse import urlparse

        import locale
        locale.setlocale(locale.LC_ALL, settings.LOCALE)

        for s in summits:

            #user summit count
            sql = "select a.* from items_summitlog a where a.user_id = %s and a.status = 1 and a.attempt = false and a.item_id = %s "
            with connection.cursor() as cursor:
                cursor.execute(sql, [s.user.id, s.item.id])

                user_summits_all = dictfetchall(cursor)
                user_summit_count_all = len(user_summits_all)

            #user summit count all peaks
            sql = "select distinct b.id, b.name, b.slug_new_text, b.elevation, floor(b.elevation*.3048) as elevation_in_meters from items_summitlog a, items_item b where a.user_id = %s and a.status = 1 and a.attempt = false and a.item_id = b.id order by b.elevation desc "
            with connection.cursor() as cursor:
                cursor.execute(sql, [s.user.id])

                user_summits_all_peaks = dictfetchall(cursor)
                user_summit_count_all_peaks = len(user_summits_all_peaks)

            #summits this year count
            sql = "select distinct(user_id) from items_summitlog where item_id = %s and date_entered = true and date_part('year', date) = date_part('year', CURRENT_DATE) "
            with connection.cursor() as cursor:
                cursor.execute(sql, [s.item.id])

                summits_this_year = dictfetchall(cursor)
                summits_this_year_count = len(summits_this_year)

            #summit stewards
            sql = "select " + \
                "ab.id, " + \
                "ab.username, " + \
                "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url, " + \
                "count(aa.id) as summitlog_count " + \
                "from items_summitlog aa " + \
                "join auth_user ab on ab.id = aa.user_id " + \
                "left join avatar_avatar f on f.user_id = aa.user_id " + \
                "where aa.item_id = %s and aa.status = 1 and aa.attempt = false " + \
                "group by ab.id, ab.username, replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/') having count(aa.id) >= 5 " + \
                "order by count(aa.id) desc "

            with connection.cursor() as cursor:
                cursor.execute(sql, [s.item.id])
                summit_stewards = dictfetchall(cursor)

            #first ascents
            sql = "select " + \
                "ab.id, " + \
                "ab.username, " + \
                "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url, " + \
                "aa.created as log_date " + \
                "from items_summitlog aa " + \
                "join auth_user ab on ab.id = aa.user_id " + \
                "left join avatar_avatar f on f.user_id = aa.user_id " + \
                "where aa.item_id = %s and aa.status = 1 and aa.attempt = false " + \
                "order by aa.created asc limit 1 "

            with connection.cursor() as cursor:
                cursor.execute(sql, [s.item.id])
                if cursor.rowcount > 0:
                    first_ascent = dictfetchall(cursor)[0]
                else:
                    first_ascent = {'id': 0, 'username': '', 'avatar_url': '', 'log_date': ''}

            #king of the mountain
            sql = "select " + \
                "ab.id, " + \
                "ab.username, " + \
                "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url, " + \
                "x.oldest_log_date, " + \
                "count(aa.id) as summitlog_count " + \
                "from items_summitlog aa " + \
                "join auth_user ab on ab.id = aa.user_id " + \
                "join (select x.user_id, x.item_id, min(x.created) as oldest_log_date from items_summitlog x group by x.user_id, x.item_id) x on x.user_id = aa.user_id and x.item_id = aa.item_id " + \
                "left join avatar_avatar f on f.user_id = aa.user_id " + \
                "where aa.item_id = %s and aa.status = 1 and aa.attempt = false " + \
                "group by ab.id, ab.username, replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), x.oldest_log_date " + \
                "order by count(aa.id) desc, x.oldest_log_date asc limit 2 "

            with connection.cursor() as cursor:
                cursor.execute(sql, [s.item.id])
                result = dictfetchall(cursor)
            if cursor.rowcount > 0:
                king_of_the_mountain = result[0]
            else:
                king_of_the_mountain = {'id': 0, 'username': '', 'avatar_url': '', 'oldest_log_date': '', 'summitlog_count': 0}
            if cursor.rowcount > 1:
                second_place = result[1]
                second_place_summits = second_place.get('summitlog_count')
            else:
                second_place_summits = 0

            #if first time summited, get challenges for badges
            if user_summit_count_all == 1:
                sql = "select a.id, a.name, a.thumbnail, count(distinct c.item_id) as summited_peak_count, d.total_peak_count, ceil(cast(count(distinct c.item_id) as float) / cast(d.total_peak_count as float) * 100) as completion_pct " + \
                    "from items_itemgroup a " + \
                    "join items_itemgroupitem b on b.group_id = a.id " + \
                    "join items_summitlog c on c.item_id = b.item_id and c.user_id = %s and c.attempt = false and c.status = 1 and c.id <= %s " + \
                    "join (select x.group_id, count(x.item_id) as total_peak_count from items_itemgroupitem x group by x.group_id) d on d.group_id = a.id " + \
                    "where exists (select 1 from items_itemgroupitem b where b.item_id = %s and b.group_id = a.id) " + \
                    "group by a.id, a.name, a.thumbnail, d.total_peak_count "

                badge_challenges = ItemGroup.objects.raw(sql, [s.user.id, s.id, s.item.id])

            #get any badges
            is_first_ascent = False
            is_kom = False
            is_steward = False
            if s.item.summitlog_count == 1 and first_ascent.get('username') == s.user.username:
                badge = {}
                badge['type'] = 'first_ascent'
                badge['peak_name'] = s.item.name
                summit_badges.append(badge)
                is_first_ascent = True
                sql = "update items_item set first_ascent_user = %s where id = %s "
                with connection.cursor() as cursor:
                    cursor.execute(sql, [s.user.id, s.item.id])
                #send notifications
                try:
                    redis_queue.enqueue(send(followers,'following_new_first_ascent',{'user':s.user, 'summit':s , 'item':s.item, 'summitlogId':s.id}, on_site=True, sender=s.user, summitlog=s, summitlog_comment=None, receiver=None))
                except:
                    pass

            if s.attempt == False and not is_first_ascent:
                badge = {}
                badge['type'] = 'summary'
                badge['peak_name'] = s.item.name
                badge['peak_elevation'] = s.item.get_elevation
                badge['peak_thumbnail'] = s.item.get_thumbnail_745
                badge['summit_count'] = user_summit_count_all
                badge['summit_date'] = s.date
                if user_summit_count_all == 1:
                    badge['summits_this_year_count'] = summits_this_year_count
                else:
                    badge['summits_this_year_count'] = 0
                summit_badges.append(badge)

            if user_summit_count_all == 5:
                badge = {}
                badge['type'] = 'summit_steward'
                badge['peak_name'] = s.item.name
                badge['steward_count'] = len(summit_stewards)
                summit_badges.append(badge)
                is_steward = True
                sql = "update items_item set summit_stewards = (case when summit_stewards = '{}' then '{\"summit_stewards\": [\"%s\"]}' else concat(substr(summit_stewards,1,length(summit_stewards)-2),',\"%s\"]}') end) where id = %s "
                with connection.cursor() as cursor:
                    cursor.execute(sql, [s.user.id, s.user.id, s.item.id])
                #send notifications
                try:
                    redis_queue.enqueue(send(followers,'following_new_steward',{'user':s.user, 'summit':s , 'item':s.item, 'summitlogId':s.id}, on_site=True, sender=s.user, summitlog=s, summitlog_comment=None, receiver=None))
                except:
                    pass

            if king_of_the_mountain.get('username') == s.user.username and (int(king_of_the_mountain.get('summitlog_count')) - int(second_place_summits) == 1):
                badge = {}
                badge['type'] = 'king_of_the_mountain'
                badge['peak_name'] = s.item.name
                summit_badges.append(badge)
                is_kom = True
                sql = "update items_item set kom_user = %s where id = %s "
                with connection.cursor() as cursor:
                    cursor.execute(sql, [s.user.id, s.item.id])
                #send notifications
                if not is_first_ascent:
                    try:
                        redis_queue.enqueue(send(followers,'following_new_kom',{'user':s.user, 'summit':s , 'item':s.item, 'summitlogId':s.id}, on_site=True, sender=s.user, summitlog=s, summitlog_comment=None, receiver=None))
                    except:
                        pass

            if user_summit_count_all_peaks == 50:
                badge = {}
                badge['type'] = '50_peaks'
                summit_badges.append(badge)
            if user_summit_count_all_peaks == 100:
                badge = {}
                badge['type'] = '100_peaks'
                summit_badges.append(badge)
            if user_summit_count_all_peaks == 200:
                badge = {}
                badge['type'] = '200_peaks'
                summit_badges.append(badge)
            if user_summits_all_peaks:
                if s.item.summitlog_count == 1 and user_summits_all_peaks[0]['id'] == s.item.id:
                    badge = {}
                    if len(user_summits_all_peaks) > 1:
                        new_record_delta = float(user_summits_all_peaks[0]['elevation']) - float(user_summits_all_peaks[1]['elevation'])
                        new_record_delta_meters = float(user_summits_all_peaks[0]['elevation_in_meters']) - float(user_summits_all_peaks[1]['elevation_in_meters'])
                    else:
                        new_record_delta = float(user_summits_all_peaks[0]['elevation'])
                        new_record_delta_meters = float(user_summits_all_peaks[0]['elevation_in_meters'])
                    badge['type'] = 'elevation_pr'
                    badge['peak_name'] = s.item.name
                    badge['peak_elevation'] = s.item.get_elevation
                    badge['peak_thumbnail'] = s.item.get_thumbnail_745
                    if len(user_summits_all_peaks) > 1:
                        badge['previous_record_peak'] = user_summits_all_peaks[1]['name']
                        badge['previous_record_elevation'] = user_summits_all_peaks[1]['elevation']
                    else:
                        badge['previous_record_peak'] = 'None'
                        badge['previous_record_elevation'] = 0
                    badge['new_record_delta'] = locale.format_string('%d', new_record_delta, grouping=True)
                    badge['new_record_delta_meters'] = locale.format_string('%d', new_record_delta_meters, grouping=True)
                    summit_badges.append(badge)

            if badge_challenges:
                for b in badge_challenges:
                    finisher_count = 0
                    for _ in b.get_finishers(b.total_peak_count):
                        finisher_count = finisher_count + 1
                    if b.completion_pct == 0:
                        completion_pct = 1
                    elif b.completion_pct == 100 and b.summited_peak_count != b.total_peak_count:
                        completion_pct = 99
                    else:
                        completion_pct = b.completion_pct
                    badge = {}
                    badge['type'] = 'challenge'
                    badge['name'] = b.name
                    badge['thumbnail'] = b.thumbnail
                    badge['summited_peaks'] = b.summited_peak_count
                    badge['total_peaks'] = b.total_peak_count
                    badge['completion_pct'] = completion_pct
                    badge['finisher_count'] = finisher_count
                    summit_badges.append(badge)

            last_badge_index = len(summit_badges)-1

            #if need to send the summit log
            if not is_first_ascent and not is_kom and not is_steward:
                try:
                    redis_queue.enqueue(send(followers,'new_following_summitlog',{'user':request.user, 'summit':s , 'item':s.item, 'summitlogId':s.id}, on_site=True, sender=request.user, summitlog=s, summitlog_comment=None, receiver=None))
                except:
                    pass

    request.session['show_badges'] = False
    request.session['badges_summit_id'] = None

    end_time = time.time() - start_time
    print('perfmon - user_summit_badges (%s) - %s' % (bagger.username, end_time))

    return render(request, 'accounts/user_summits.html', {
        'bagger':bagger,
        'profile':profile,
        'avatar': avatar,
        'regions': regions,
        'years': years,
        'following': following,
        'subnav_summits_style': subnav_summits_style,
        'nav_page_name': nav_page_name,
        'summit_badges':summit_badges,
        'last_badge_index':last_badge_index
    })

def mobile_user_badges(request, username=None, summitlog_id=0):

    import time
    start_time = time.time()

    if username is None:
        username = request.user.username

    if username == request.user.username:
        if request.user.is_authenticated:
            bagger = request.user
        else:
            return HttpResponse("User doesn't exist")
    else:
        bagger = get_object_or_404(User, username__iexact=username)

    #if not request.session.get('show_badges', False):
        #return HttpResponseRedirect(reverse('user_summits', kwargs={'username':username}))

    #badges_summit_id = request.session.get('badges_summit_id', 0)
    badges_summit_id = summitlog_id
    badges_summit = SummitLog.objects.get(id=badges_summit_id)
    #if badges_summit_id == 0 or str(badges_summit_id) == str(badges_summit.id):
    if badges_summit_id == 0:
        return HttpResponseRedirect(reverse('user_summits', kwargs={'username':username}))

    profile = bagger.person

    #get followers
    try:
        #followers = Follow.objects.get_followers_for_object(bagger)
        followers = bagger.person.get_followers_for_user()
    except:
        followers = None

    #get avatar
    sql = "select " + \
        "a.id, " + \
        "coalesce(replace(replace(b.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'),'img/default-user.png') as avatar_url " + \
        "from auth_user a " + \
        "left join avatar_avatar b on b.user_id = a.id " + \
        "where a.id = %s "

    with connection.cursor() as cursor:
        cursor.execute(sql, [bagger.person.user.id])
        avatar = dictfetchall(cursor)[0]

    sql = "select b.id, case when length(c.name) > 0 then concat(b.name,', ',c.name) else b.name end as region_name, c.name as country_name, count(d.*) as summit_count " + \
        "from items_summitlog d " + \
        "join items_item_region a on a.item_id = d.item_id " + \
        "join cities_region b on b.id = a.region_id " + \
        "left join cities_country c on c.id = b.country_id " + \
        "where d.user_id = %s " + \
        "and d.status = 1 " + \
        "group by b.id, b.name, c.name " + \
        "order by b.name, c.name "
    with connection.cursor() as cursor:
        cursor.execute(sql, [bagger.person.user.id])
        regions = dictfetchall(cursor)

    count_sql = "select 0 as sort_order, to_char(aa.date, 'YYYY') as year, to_char(aa.date, 'YYYY') as year_name, count(aa.id) as summits_count " + \
                "from items_summitlog aa " + \
                "join auth_user ab on ab.id = aa.user_id " + \
                "join items_item a on a.id = aa.item_id " + \
                "where aa.user_id = %s and aa.status = 1 and date_entered = true " + \
                "group by to_char(aa.date, 'YYYY') " + \
        "union " + \
            "select 1 as sort_order, 'Unknown' as year, 'Unknown' as year_name, count(aa.id) as summits_count " + \
                "from items_summitlog aa " + \
                "join auth_user ab on ab.id = aa.user_id " + \
                "join items_item a on a.id = aa.item_id " + \
                "where aa.user_id = %s and aa.status = 1 and date_entered = false " + \
                "group by sort_order, year, year_name " + \
        "order by sort_order, year_name "

    with connection.cursor() as cursor:
        cursor.execute(count_sql, [bagger.person.user.id, bagger.person.user.id])
        years = dictfetchall(cursor)

    following = False
    if request.user.is_authenticated:
        following = profile.is_user_following(request.user)

    subnav_summits_style = 'color: #f24100;'

    nav_page_name = bagger

    #Need to get badges for other peaks?
    if badges_summit.summitlog_group_id:
        summits = SummitLog.objects.filter(summitlog_group_id=badges_summit.summitlog_group_id).order_by('id')
    else:
        summits = SummitLog.objects.filter(id=badges_summit.id).order_by('id')

    badge_challenges = None
    last_badge_index = 0
    summit_badges = []
    #if bagger.id == request.user.id:
    if 1 == 1:
        from urllib.parse import urlparse

        import locale
        locale.setlocale(locale.LC_ALL, settings.LOCALE)

        for s in summits:

            #user summit count
            sql = "select a.* from items_summitlog a where a.user_id = %s and a.status = 1 and a.attempt = false and a.item_id = %s "
            with connection.cursor() as cursor:
                cursor.execute(sql, [s.user.id, s.item.id])

                user_summits_all = dictfetchall(cursor)
                user_summit_count_all = len(user_summits_all)

            #user summit count all peaks
            sql = "select distinct b.id, b.name, b.slug_new_text, b.elevation, floor(b.elevation*.3048) as elevation_in_meters from items_summitlog a, items_item b where a.user_id = %s and a.status = 1 and a.attempt = false and a.item_id = b.id order by b.elevation desc "
            with connection.cursor() as cursor:
                cursor.execute(sql, [s.user.id])

                user_summits_all_peaks = dictfetchall(cursor)
                user_summit_count_all_peaks = len(user_summits_all_peaks)

            #summits this year count
            sql = "select distinct(user_id) from items_summitlog where item_id = %s and date_entered = true and date_part('year', date) = date_part('year', CURRENT_DATE) "
            with connection.cursor() as cursor:
                cursor.execute(sql, [s.item.id])

                summits_this_year = dictfetchall(cursor)
                summits_this_year_count = len(summits_this_year)

            #summit stewards
            sql = "select " + \
                "ab.id, " + \
                "ab.username, " + \
                "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url, " + \
                "count(aa.id) as summitlog_count " + \
                "from items_summitlog aa " + \
                "join auth_user ab on ab.id = aa.user_id " + \
                "left join avatar_avatar f on f.user_id = aa.user_id " + \
                "where aa.item_id = %s and aa.status = 1 and aa.attempt = false " + \
                "group by ab.id, ab.username, replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/') having count(aa.id) >= 5 " + \
                "order by count(aa.id) desc "

            with connection.cursor() as cursor:
                cursor.execute(sql, [s.item.id])
                summit_stewards = dictfetchall(cursor)

            #first ascents
            sql = "select " + \
                "ab.id, " + \
                "ab.username, " + \
                "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url, " + \
                "aa.created as log_date " + \
                "from items_summitlog aa " + \
                "join auth_user ab on ab.id = aa.user_id " + \
                "left join avatar_avatar f on f.user_id = aa.user_id " + \
                "where aa.item_id = %s and aa.status = 1 and aa.attempt = false " + \
                "order by aa.created asc limit 1 "

            with connection.cursor() as cursor:
                cursor.execute(sql, [s.item.id])
                if cursor.rowcount > 0:
                    first_ascent = dictfetchall(cursor)[0]
                else:
                    first_ascent = {'id': 0, 'username': '', 'avatar_url': '', 'log_date': ''}

            #king of the mountain
            sql = "select " + \
                "ab.id, " + \
                "ab.username, " + \
                "coalesce(replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), 'img/default-user.png') as avatar_url, " + \
                "x.oldest_log_date, " + \
                "count(aa.id) as summitlog_count " + \
                "from items_summitlog aa " + \
                "join auth_user ab on ab.id = aa.user_id " + \
                "join (select x.user_id, x.item_id, min(x.created) as oldest_log_date from items_summitlog x group by x.user_id, x.item_id) x on x.user_id = aa.user_id and x.item_id = aa.item_id " + \
                "left join avatar_avatar f on f.user_id = aa.user_id " + \
                "where aa.item_id = %s and aa.status = 1 and aa.attempt = false " + \
                "group by ab.id, ab.username, replace(replace(f.avatar, '/', '/resized/400/'), 'avatars/resized/400/', 'avatars/'), x.oldest_log_date " + \
                "order by count(aa.id) desc, x.oldest_log_date asc limit 2 "

            with connection.cursor() as cursor:
                cursor.execute(sql, [s.item.id])
                result = dictfetchall(cursor)
                if cursor.rowcount > 0:
                    king_of_the_mountain = result[0]
                else:
                    king_of_the_mountain = {'id': 0, 'username': '', 'avatar_url': '', 'oldest_log_date': '', 'summitlog_count': 0}
                if cursor.rowcount > 1:
                    second_place = result[1]
                    second_place_summits = second_place.get('summitlog_count')
                else:
                    second_place_summits = 0

            #if first time summited, get challenges for badges
            if user_summit_count_all == 1:
                sql = "select a.id, a.name, a.thumbnail, count(distinct c.item_id) as summited_peak_count, d.total_peak_count, ceil(cast(count(distinct c.item_id) as float) / cast(d.total_peak_count as float) * 100) as completion_pct " + \
                    "from items_itemgroup a " + \
                    "join items_itemgroupitem b on b.group_id = a.id " + \
                    "join items_summitlog c on c.item_id = b.item_id and c.user_id = %s and c.attempt = false and c.status = 1 and c.id <= %s " + \
                    "join (select x.group_id, count(x.item_id) as total_peak_count from items_itemgroupitem x group by x.group_id) d on d.group_id = a.id " + \
                    "where exists (select 1 from items_itemgroupitem b where b.item_id = %s and b.group_id = a.id) " + \
                    "group by a.id, a.name, a.thumbnail, d.total_peak_count "

                badge_challenges = ItemGroup.objects.raw(sql, [s.user.id, s.id, s.item.id])

            #get any badges
            is_first_ascent = False
            if s.item.summitlog_count == 1 and first_ascent.get('username') == s.user.username:
                badge = {}
                badge['type'] = 'first_ascent'
                badge['peak_name'] = s.item.name
                summit_badges.append(badge)
                is_first_ascent = True
                #send notifications
                try:
                    redis_queue.enqueue(send(followers,'following_new_first_ascent',{'user':s.user, 'summit':s , 'item':s.item, 'summitlogId':s.id}, on_site=True, sender=s.user, summitlog=s, summitlog_comment=None, receiver=None))
                except:
                    pass

            if s.attempt == False and not is_first_ascent:
                badge = {}
                badge['type'] = 'summary'
                badge['peak_name'] = s.item.name
                badge['peak_elevation'] = s.item.get_elevation
                badge['peak_thumbnail'] = s.item.get_thumbnail_745
                badge['summit_count'] = user_summit_count_all
                badge['summit_date'] = s.date
                if user_summit_count_all == 1:
                    badge['summits_this_year_count'] = summits_this_year_count
                else:
                    badge['summits_this_year_count'] = 0
                summit_badges.append(badge)

            if user_summit_count_all == 5:
                badge = {}
                badge['type'] = 'summit_steward'
                badge['peak_name'] = s.item.name
                badge['steward_count'] = len(summit_stewards)
                summit_badges.append(badge)
                #send notifications
                try:
                    redis_queue.enqueue(send(followers,'following_new_steward',{'user':s.user, 'summit':s , 'item':s.item, 'summitlogId':s.id}, on_site=True, sender=s.user, summitlog=s, summitlog_comment=None, receiver=None))
                except:
                    pass

            if king_of_the_mountain.get('username') == s.user.username and (int(king_of_the_mountain.get('summitlog_count')) - int(second_place_summits) == 1):
                badge = {}
                badge['type'] = 'king_of_the_mountain'
                badge['peak_name'] = s.item.name
                summit_badges.append(badge)
                #send notifications
                if not is_first_ascent:
                    try:
                        redis_queue.enqueue(send(followers,'following_new_kom',{'user':s.user, 'summit':s , 'item':s.item, 'summitlogId':s.id}, on_site=True, sender=s.user, summitlog=s, summitlog_comment=None, receiver=None))
                    except:
                        pass

            if user_summit_count_all_peaks == 50:
                badge = {}
                badge['type'] = '50_peaks'
                summit_badges.append(badge)
            if user_summit_count_all_peaks == 100:
                badge = {}
                badge['type'] = '100_peaks'
                summit_badges.append(badge)
            if user_summit_count_all_peaks == 200:
                badge = {}
                badge['type'] = '200_peaks'
                summit_badges.append(badge)
            if user_summits_all_peaks:
                if s.item.summitlog_count == 1 and user_summits_all_peaks[0]['id'] == s.item.id:
                    badge = {}
                    if len(user_summits_all_peaks) > 1:
                        new_record_delta = float(user_summits_all_peaks[0]['elevation']) - float(user_summits_all_peaks[1]['elevation'])
                        new_record_delta_meters = float(user_summits_all_peaks[0]['elevation_in_meters']) - float(user_summits_all_peaks[1]['elevation_in_meters'])
                    else:
                        new_record_delta = float(user_summits_all_peaks[0]['elevation'])
                        new_record_delta_meters = float(user_summits_all_peaks[0]['elevation_in_meters'])
                    badge['type'] = 'elevation_pr'
                    badge['peak_name'] = s.item.name
                    badge['peak_elevation'] = s.item.get_elevation
                    badge['peak_thumbnail'] = s.item.get_thumbnail_745
                    if len(user_summits_all_peaks) > 1:
                        badge['previous_record_peak'] = user_summits_all_peaks[1]['name']
                        badge['previous_record_elevation'] = user_summits_all_peaks[1]['elevation']
                    else:
                        badge['previous_record_peak'] = 'None'
                        badge['previous_record_elevation'] = 0
                    badge['new_record_delta'] = locale.format_string('%d', new_record_delta, grouping=True)
                    badge['new_record_delta_meters'] = locale.format_string('%d', new_record_delta_meters, grouping=True)
                    summit_badges.append(badge)

            if badge_challenges:
                for b in badge_challenges:
                    finisher_count = 0
                    for f in b.get_finishers(b.total_peak_count):
                        finisher_count = finisher_count + 1
                    if b.completion_pct == 0:
                        completion_pct = 1
                    elif b.completion_pct == 100 and b.summited_peak_count != b.total_peak_count:
                        completion_pct = 99
                    else:
                        completion_pct = b.completion_pct
                    badge = {}
                    badge['type'] = 'challenge'
                    badge['name'] = b.name
                    badge['thumbnail'] = b.thumbnail
                    badge['summited_peaks'] = b.summited_peak_count
                    badge['total_peaks'] = b.total_peak_count
                    badge['completion_pct'] = completion_pct
                    badge['finisher_count'] = finisher_count
                    summit_badges.append(badge)

            last_badge_index = len(summit_badges)-1

            #if need to send the summit log
            #if not is_first_ascent and not is_kom and not is_steward:
                #try:
                    #redis_queue.enqueue(send(followers,'new_following_summitlog',{'user':request.user, 'summit':s , 'item':s.item, 'summitlogId':s.id}, on_site=True, sender=request.user, summitlog=s, summitlog_comment=None, receiver=None))
                #except:
                    #pass

    request.session['show_badges'] = False
    request.session['badges_summit_id'] = None

    end_time = time.time() - start_time
    print('perfmon - user_summit_badges (%s) - %s' % (bagger.username, end_time))

    return render(request, 'accounts/user_summits.html', {
        'bagger':bagger,
        'profile':profile,
        'avatar': avatar,
        'regions': regions,
        'years': years,
        'following': following,
        'subnav_summits_style': subnav_summits_style,
        'nav_page_name': nav_page_name,
        'summit_badges':summit_badges,
        'last_badge_index':last_badge_index
    })

def user_map(request, username=None):

    if username is None:
        username = request.user.username

    if username == request.user.username:
        if request.user.is_authenticated:
            bagger = request.user
        else:
            return HttpResponse("User doesn't exist")
    else:
        bagger = get_object_or_404(User, username__iexact=username)

    profile = bagger.person

    #get avatar
    sql = "select " + \
        "a.id, " + \
        "coalesce(replace(replace(b.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'),'img/default-user.png') as avatar_url " + \
        "from auth_user a " + \
        "left join avatar_avatar b on b.user_id = a.id " + \
        "where a.id = %s "

    map_user_id = bagger.person.user.id

    with connection.cursor() as cursor:
        cursor.execute(sql, [map_user_id])
        avatar = dictfetchall(cursor)[0]

    following = False
    if request.user.is_authenticated:
        following = profile.is_user_following(request.user)

    is_authenticated_user_map = map_user_id == request.user.id

    return render(request, 'accounts/user_map.html', {
        'bagger':bagger,
        'profile':profile,
        'avatar': avatar,
        'following': following,
        'subnav_map_style': 'color: #f24100;',
        'nav_page_name': bagger,
        'is_authenticated_user_map': is_authenticated_user_map
    })

def user_challenges(request, username=None):

    if username is None:
        username = request.user.username

    if username == request.user.username:
        if request.user.is_authenticated:
            bagger = request.user
        else:
            return HttpResponse("User doesn't exist")
    else:
        bagger = get_object_or_404(User, username__iexact=username)

    profile = bagger.person

    sql = "select a.id, a.name, a.description, a.slug, concat(replace(a.thumbnail,'items/lists/','items/lists/cache/'),'.350x245_q95_crop.jpg') as thumbnail, b.total_peaks, " + \
        "count(aa.id) as peak_count " + \
        "from items_itemgroup a " + \
        "join (select x.group_id, count(x.item_id) as total_peaks from items_itemgroupitem x group by x.group_id) b on b.group_id = a.id " + \
        "join items_itemgroupitem aa on aa.group_id = a.id  " + \
        "where exists (select 1 from items_summitlog b where b.user_id = %s and b.status = 1 and b.item_id = aa.item_id) " + \
        "group by a.id, a.name, a.description, a.slug, concat(replace(a.thumbnail,'items/lists/','items/lists/cache/'),'.350x245_q95_crop.jpg'), b.total_peaks order by a.order asc "

    challenges = ItemGroup.objects.raw(sql, [bagger.id])

    sql = "select 'region' as type, b.id, case when length(c.name) > 0 then concat(b.name,', ',c.name) else b.name end as region_name, c.name as country_name, count(distinct d.id) as challenge_count " + \
                "from items_itemgroup d " + \
                "join items_itemgroupitem e on e.group_id = d.id " + \
                "join items_item_region a on a.item_id = e.item_id " + \
                "join cities_region b on b.id = a.region_id " + \
                "left join cities_country c on c.id = b.country_id " + \
                "where c.code in ('US','CA','GB') and exists (select 1 from items_summitlog y where y.user_id = %s and y.status = 1 and y.item_id = e.item_id) " + \
                "group by b.id, b.name, c.name " + \
        "union select 'country' as type, c.id, c.name as region_name, c.name as country_name, count(distinct d.id) as challenge_count " + \
                "from items_itemgroup d " + \
                "join items_itemgroupitem e on e.group_id = d.id " + \
                "join items_item_country a on a.item_id = e.item_id " + \
                "join cities_country c on c.id = a.country_id " + \
                "where exists (select 1 from items_summitlog y where y.user_id = %s and y.status = 1 and y.item_id = e.item_id) " + \
                "group by c.id, c.name " + \
                "order by country_name, type, region_name "
    with connection.cursor() as cursor:
        cursor.execute(sql, [bagger.person.user.id, bagger.person.user.id])
        regions = dictfetchall(cursor)

    total_count = 0
    completed_count = 0
    for c in challenges:
        total_count = total_count + 1
        if c.total_peaks == c.peak_count:
            completed_count = completed_count + 1
    inprogress_count = total_count - completed_count

    #get avatar
    sql = "select " + \
        "a.id, " + \
        "coalesce(replace(replace(b.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'),'img/default-user.png') as avatar_url " + \
        "from auth_user a " + \
        "left join avatar_avatar b on b.user_id = a.id " + \
        "where a.id = %s "

    with connection.cursor() as cursor:
        cursor.execute(sql, [bagger.person.user.id])
        avatar = dictfetchall(cursor)[0]

    following = False
    if request.user.is_authenticated:
        following = profile.is_user_following(request.user)

    subnav_challenges_style = 'color: #f24100;'

    nav_page_name = bagger

    return render(request, 'accounts/user_challenges.html', {
        'bagger':bagger,
        'profile':profile,
        'regions':regions,
        'total_count': total_count,
        'completed_count': completed_count,
        'inprogress_count': inprogress_count,
        'avatar': avatar,
        'following': following,
        'subnav_challenges_style': subnav_challenges_style,
        'nav_page_name': nav_page_name
    })

@login_required
def login_and_reload(request,user):

    #return HttpResponse('')
    return render(request, 'accounts/reload.html', {'s':'s'})
    #return HttpResponseRedirect(reverse('accounts.views.user_profile',args=[user]))


@login_required
def edit(request):

    tab = request.GET.get('tab', 0)

    profile = request.user.person
    has_avatar = profile.has_avatar()
    if request.method == 'POST':
        person_form = PersonForm(request.POST, instance=profile)
        user_form = UserForm(request.POST, instance=request.user)

        valid = True
        #print(">>> Saving the Form")
        #print(dir(person_form))

        if person_form.is_valid():
            person = person_form.save(commit=False)
            person.was_edited_info = True
            person.save()
        else:
            #print(">>> Form is not Valid")
            valid = False
        if user_form.is_valid():
            user_form.save()
        else:
            valid = False
        location_city = request.POST.get('location_city')
        if location_city:
            try:
                parts = location_city.replace(', ', ',').split(',')
                city = City.objects.get(name=parts[0], region__name=parts[1])
                profile.location = city
                profile.save()
            except:
                pass

        if valid:
            messages.success(request, 'Profile details updated')

    else:

        person_form = PersonForm(instance=profile)
        user_form = UserForm(instance=request.user)
        if profile.location_city:
            location_city = profile.location_city.__unicode__()
        else:
            location_city = ''

    notice_types = NoticeType.objects.all()
    for exclude_notification in NOTIFICATIONS_CONFIG['exclude']:
        notice_types = notice_types.exclude(label = exclude_notification)
    settings_table = []
    for notice_type in notice_types:
        settings_row = []
        for medium_id, medium_display in NOTICE_MEDIA:
            form_label = "%s_%s" % (notice_type.label, medium_id)
            setting = get_notification_setting(request.user, notice_type, medium_id)
            if request.method == "POST":
                if request.POST.get(form_label) == "on":
                    if not setting.send:
                        setting.send = True
                        setting.save()

                else:
                    if setting.send:
                        setting.send = False
                        setting.save()

            settings_row.append((form_label, setting.send))
        settings_table.append({"notice_type": notice_type, "cells": settings_row})
    if request.method == "POST" and valid:
        return HttpResponse('True')
    show_javascript = True
    if request.method == "POST":
        show_javascript = False
    notice_settings = {
        "column_headers": [medium_display for medium_id, medium_display in NOTICE_MEDIA],
        "rows": settings_table,
        }

    return render(request, "accounts/edit.html", {'person_form':person_form,
                                                     'user_form':user_form,
                                                     'location_city':location_city,
                                                     'has_avatar':has_avatar,
                                                     'notice_types': notice_types,
                                                     'notice_settings': notice_settings,
                                                     'show_javascript': show_javascript,
                                                     'tab':tab
    })


@login_required
def change_password(request):
    PasswordChangeForm
    if request.method == 'POST':
        password_form = PasswordChangeForm(request.user, request.POST)
        if password_form.is_valid():
            password_form.save()
            messages.success(request, 'password changed successfully')
            return HttpResponse('True')
    else:
        password_form = PasswordChangeForm(request.user)
    return render(request, "accounts/change_password.html", {'password_form':password_form})


@login_required
def user_news(request,filter="all"):
    try:
        ini = int(request.GET.get('from',0))
        end = int(request.GET.get('to',10))
        each = 10
    except:
        raise Http404
    notices = Notice.objects.notices_for(request.user).order_by('-added')
    Notice.objects.notices_for(request.user).update(unseen=False)
    if filter == "all":
        notice_types = None
    elif filter == "you":
        notice_types = NoticeType.objects.exclude(label__contains = "following")
    elif filter == "following":
        notice_types = NoticeType.objects.filter(label__contains = "following")

    if notice_types:
        notices = notices.filter(notice_type__in = notice_types)


    total = notices.count()
    notices = notices[ini:end]
    if ini !=0 :
        return render(request, "accounts/ajax/more_news.html",{'notices':notices,'filter':filter,'first_time':False})
    else:
        return render(request, "accounts/user_news.html",{'notices':notices,'filter':filter,'from':end + 1,'each':each,'total':total,'first_time':True})

@login_required
def new_user_news(request):

    nav_news_style = 'color: #00B1F2;'
    nav_page_name = 'News'

    notices = Notice.objects.notices_for(request.user).order_by('-added')
    Notice.objects.notices_for(request.user).update(unseen=False)
    total = notices.count()

    return render(request, "accounts/new_user_news.html",{'total_news':total, 'nav_news_style': nav_news_style, 'nav_page_name': nav_page_name})


def login_user_lite(request, template='accounts/login_form.html'):
    if request.method == 'POST':
        form = AuthenticationForm(data=request.POST)
        username = None
        try:
            if form.is_valid():
                django_login(request, form.get_user())
                if request.session.test_cookie_worked():
                    request.session.delete_test_cookie()
                if request.is_ajax():
                    return HttpResponse('True')
            else:
                form_username = request.POST.get('username', None).lower()
                form_password = request.POST.get('password', None)

                #attempt login
                if '@' in form_username:
                    # get the username from email
                    users = User.objects.filter(email__iexact=form_username)
                    for user in users:
                        username = user.username
                else:
                    # get the username from username
                    users = User.objects.filter(username__iexact=form_username)
                    for user in users:
                        username = user.username

                user = authenticate(username=username, password=form_password)
                if not user:
                    if form_username:
                        if form_password:
                            username = User.objects.filter(username=request.POST.get('username', None))
                            email = User.objects.filter(email=request.POST.get('username', None))
                            if not username and not email:
                                if form_username.find("@") >= 0:
                                    return HttpResponse('ErrorEmail')
                                else:
                                    return HttpResponse('ErrorUsername')
                            else:
                                return HttpResponse('ErrorPassword')
                        else:
                            return HttpResponse('NoPassword')
                    else:
                        if form_password:
                            return HttpResponse('NoUsername')
                        else:
                            return HttpResponse('NoUsernameNoPassword')
                else:
                    from django.contrib.auth import login
                    login(request, user)
                    return HttpResponse('True')
        except ValueError:
            return HttpResponse('ErrorPassword')
        except Exception as e:
            print("Exception occurred in login_user_lite")
            print(traceback.format_exc())
            raise e
    else:
        form = AuthenticationForm(request)
        request.session.set_test_cookie()

    return render(request, template, {'form':form})

def upload_avatar(request):
    param = request.GET.get('param')
    import os
    dir = '%s/avatars/%s/' % (settings.MEDIA_ROOT, request.user.username)
    if not os.path.exists(dir):
        os.makedirs(dir)

    if request.GET.get('qqfile'):
        upload_path = 'avatars/%s/%s' % (request.user.username, request.GET.get('qqfile'))
        destination = open('%s/%s' % (settings.MEDIA_ROOT, upload_path), 'wb+')
        destination.write(request.raw_post_data)
        destination.close()
    else:
        f = request.FILES['qqfile']
        upload_path = 'avatars/%s/%s' % (request.user.username, f.name)
        destination = open('%s/%s' % (settings.MEDIA_ROOT, upload_path), 'wb+')
        for chunk in f.chunks():
            destination.write(chunk)
        destination.close()

    from avatar.models import Avatar
    avatar = Avatar.objects.filter(user=request.user)
    for a in avatar:
        a.delete()
    new_avatar = Avatar(user=request.user, primary=True)
    new_avatar.avatar = upload_path
    new_avatar.save()

    profile = request.user.person
    profile.was_edited_info = True
    profile.save()

    return render(request, 'accounts/ajax/show_uploaded_avatar.html', {'user':request.user})

@login_required
def upload_user_avatar(request, username=None):
    from PIL import Image, ImageFilter, ExifTags
    import cStringIO
    import time
    import os.path
    import re

    timestr = time.strftime("%Y%m%d-%H%M%S")
    user_id = request.GET.get('user_id', False)
    if user_id:
        user = User.objects.get(id=user_id)
        if request.GET.get('qqfile'):
            filename = request.GET.get('qqfile')
            filename = re.sub('[^a-zA-Z0-9-_*.]', '', filename)
            base = os.path.splitext(filename)[0]
            extension = os.path.splitext(filename)[1][1:].lower()
            filename = slugify(base) + '.' + extension
            if (extension == 'png'):
                extension = 'PNG'
            else:
                extension = 'JPEG'
            upload_path = 'avatars/%s/%s-%s-%s' % (user.username, user_id, timestr, filename)
            thumb_file_path_100 = 'avatars/%s/resized/100/%s-%s-%s' % (user.username, user_id, timestr, filename)
            thumb_file_path_400 = 'avatars/%s/resized/400/%s-%s-%s' % (user.username, user_id, timestr, filename)
            file = default_storage.open(upload_path, 'w')
            file.write(request.body)
            file.close()

    try:
        #width, height = get_image_dimensions(settings.MEDIA_ROOT + '/' +  upload_path)
        f = default_storage.open(upload_path, 'r')
        image = Image.open(f)

        try:
            for orientation in ExifTags.TAGS.keys():
                if ExifTags.TAGS[orientation]=='Orientation':
                    break
            exif=dict(image._getexif().items())

            if exif[orientation] == 3:
                image=image.rotate(180, expand=True)
            elif exif[orientation] == 6:
                image=image.rotate(270, expand=True)
            elif exif[orientation] == 8:
                image=image.rotate(90, expand=True)

        except (AttributeError, KeyError, IndexError):
            # cases: image don't have getexif
            pass

        width, height = image.size

        #square thumbnail
        if width > height:
            delta = width - height
            left = int(delta/2)
            upper = 0
            right = width - left
            lower = height
        else:
            delta = height - width
            left = 0
            upper = int(delta/2)
            right = width
            lower = height - upper

        sq_image = image.crop((left, upper, right, lower))
        sq_image = sq_image.resize((400, 400), Image.LANCZOS)
        f_thumb = default_storage.open(thumb_file_path_400, "w")
        #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
        out_img = cStringIO.StringIO()
        #You MUST specify the file type because there is no file name to discern it from
        sq_image.save(out_img, extension)
        f_thumb.write(out_img.getvalue())
        f_thumb.close()

        sq_image_2 = sq_image.resize((100, 100), Image.LANCZOS)
        f_thumb = default_storage.open(thumb_file_path_100, "w")
        #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
        out_img = cStringIO.StringIO()
        #You MUST specify the file type because there is no file name to discern it from
        sq_image_2.save(out_img, extension)
        f_thumb.write(out_img.getvalue())
        f_thumb.close()

    except Exception as e:
        width,height = [-1,-1]

    if width >= 400 and height >= 400:

        from avatar.models import Avatar
        avatar = Avatar.objects.filter(user=request.user)
        for a in avatar:
            a.delete()
        new_avatar = Avatar(user=request.user, primary=True)
        new_avatar.avatar = upload_path
        new_avatar.save()

        profile = request.user.person
        profile.was_edited_info = True
        profile.save()

        success = 'true'
        return render(request, 'accounts/ajax/show_uploaded_avatar.html', {'success':success, 'avatar_url':thumb_file_path_400})
    else:
        #import os
        #os.remove(settings.MEDIA_ROOT + '/' + upload_path)
        default_storage.delete(upload_path)
        success = 'false'
        return render(request, 'accounts/ajax/show_uploaded_avatar.html', {'success':success, 'avatar_url':''})

@login_required
def s3_profile_photo_upload(request):
    from PIL import Image, ImageFilter, ExifTags
    from io import BytesIO
    import os.path
    user_id = request.POST.get('user_id', False)
    if user_id:
        user = User.objects.get(id=user_id)
        if request.POST.get('name'):
            filename = request.POST.get('name')
            key = request.POST.get('key')
            uuid = request.POST.get('uuid')
            basefile = key.split("/")[-1]
            extension = os.path.splitext(filename)[1][1:].lower()
            if extension == 'png':
                extension = 'PNG'
            else:
                extension = 'JPEG'
            upload_path = key
            thumb_file_path_100 = 'avatars/%s/resized/100/%s' % (user.username, basefile)
            thumb_file_path_400 = 'avatars/%s/resized/400/%s' % (user.username, basefile)

    try:
        #width, height = get_image_dimensions(settings.MEDIA_ROOT + '/' +  upload_path)
        f = default_storage.open(upload_path, 'rb')

        with Image.open(f) as image:

            try:
                for orientation in ExifTags.TAGS.keys():
                    if ExifTags.TAGS[orientation]=='Orientation':
                        break
                exif=dict(image._getexif().items())

                if exif[orientation] == 3:
                    image=image.rotate(180, expand=True)
                elif exif[orientation] == 6:
                    image=image.rotate(270, expand=True)
                elif exif[orientation] == 8:
                    image=image.rotate(90, expand=True)

            except (AttributeError, KeyError, IndexError):
                # cases: image don't have getexif
                pass

            width, height = image.size

            #400x400 thumbnail
            if width >= height:
                #landscape orientation photo
                if float(width)/float(height) > 1.0:
                    delta = width - (1.0 * height)
                    left = int(delta/2)
                    upper = 0
                    right = width - left
                    lower = height
                else:
                    delta = height - (width / 1.0)
                    left = 0
                    upper = int(delta/2)
                    right = width
                    lower = height - upper
            else:
                #portrait orientation photo
                if float(height)/float(width) > 1.0:
                    delta = height - (1.0 * width)
                    left = 0
                    upper = int(delta/2)
                    right = width
                    lower = height - upper
                else:
                    delta = width - (height / 1.0)
                    left = int(delta/2)
                    upper = 0
                    right = width - left
                    lower = height

            tmp_image = image.crop((left, upper, right, lower))
            if width >= height:
                new_width = 400
                new_height = 400
            else:
                new_width = 400
                new_height = 400
            tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
            f_thumb = default_storage.open(thumb_file_path_400, "w")
            #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
            with BytesIO() as out_img:
                #You MUST specify the file type because there is no file name to discern it from
                tmp_image.save(out_img, format='JPEG', subsampling=0, quality=95)
                f_thumb.write(out_img.getvalue())
                f_thumb.close()
                tmp_image.close()
            #100x100 thumbnail
            if width >= height:
                #landscape orientation photo
                if float(width)/float(height) > 1.0:
                    delta = width - (1.0 * height)
                    left = int(delta/2)
                    upper = 0
                    right = width - left
                    lower = height
                else:
                    delta = height - (width / 1.0)
                    left = 0
                    upper = int(delta/2)
                    right = width
                    lower = height - upper
            else:
                #portrait orientation photo
                if float(height)/float(width) > 1.0:
                    delta = height - (1.0 * width)
                    left = 0
                    upper = int(delta/2)
                    right = width
                    lower = height - upper
                else:
                    delta = width - (height / 1.0)
                    left = int(delta/2)
                    upper = 0
                    right = width - left
                    lower = height

            tmp_image = image.crop((left, upper, right, lower))
            if width >= height:
                new_width = 100
                new_height = 100
            else:
                new_width = 100
                new_height = 100
            tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
            f_thumb = default_storage.open(thumb_file_path_100, "w")
            #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
            with BytesIO() as out_img:
                #You MUST specify the file type because there is no file name to discern it from
                tmp_image.save(out_img, format='JPEG', subsampling=0, quality=95)
                f_thumb.write(out_img.getvalue())
                f_thumb.close()
                tmp_image.close()

    except Exception as e:
        width,height = [-1,-1]

    if width >= 400 and height >= 400:
        success = 'true'
        return render(request, 'accounts/ajax/show_uploaded_avatar.html', {'success':success, 'avatar_image':upload_path, 'avatar_url':thumb_file_path_400})
    else:
        success = 'false'
        return render(request, 'accounts/ajax/show_uploaded_avatar.html', {'success':success, 'avatar_url':''})

@login_required
def s3_profile_photo_init(request):
    user_id = request.GET.get('user_id', False)
    if user_id:
        user = User.objects.get(id=user_id)
        from avatar.models import Avatar
        avatar = Avatar.objects.filter(user=user)

    return render(request, 'accounts/ajax/show_profile_avatar.html', {
        'avatar': avatar,
        'user': request.user.person
    })

def user_followers(request, user_id):
    max = settings.MAX_FOLLOWS_PER_PAGE

    profile = get_object_or_404(User, id=user_id)
    #followers = Follow.objects.get_followers_for_object(profile)
    followers = profile.person.get_followers_for_user()

    total_followers = followers.count()
    followers = followers[:max]

    return render(request, 'accounts/user_followers.html', {'profile':profile, 'followers':followers, 'total_followers':total_followers, 'max_per_page':max, })

def user_followings(request, user_id):
    max = settings.MAX_FOLLOWS_PER_PAGE

    profile = get_object_or_404(User, id=user_id)
    followings = Follow.objects.get_objects_user_follows(profile, [User, ])

    total_followings = followings.count()
    followings = followings[:max]

    return render(request, 'accounts/user_followings.html', {'profile':profile, 'followings':followings, 'total_followings':total_followings, 'max_per_page':max, })

def get_more_user_followers(request, user_id):
    ini = int(request.GET.get('page', 0))
    max = settings.MAX_FOLLOWS_PER_PAGE

    profile = get_object_or_404(User, id=user_id)
    #followers = Follow.objects.get_followers_for_object(profile)[ini:ini + max]
    followers = profile.person.get_followers_for_user()[ini:ini + max]

    return render(request, 'accounts/ajax/more_user_followers.html', {'profile':profile, 'followers':followers, })

def get_profile_follow(request,user_id):

    bagger = User.objects.get(pk = user_id)
    profile = bagger.person
    following = False
    if request.user.is_authenticated:
        following = profile.does_follow_user(request.user)
    return render(request, 'accounts/ajax/profile_follow.html',{'bagger':bagger,'user':request.user,'following':following})

def get_more_user_followings(request, user_id):
    ini = int(request.GET.get('page', 0))
    max = settings.MAX_FOLLOWS_PER_PAGE

    profile = get_object_or_404(User, id=user_id)
    followings = Follow.objects.get_objects_user_follows(profile, [User, ])[ini:ini + max]

    return render(request, 'accounts/ajax/more_user_followings.html', {'profile':profile, 'followings':followings, })

@login_required
def follow_user(request, user_id):
    from datetime import datetime
    user = User.objects.filter(id=user_id)
    for u in user:
        user_to_follow = u
    profile = user_to_follow.person
    if profile:
        if not request.user.person.is_following_user(user_to_follow):

            #follow_object = Follow.objects.create(request.user, user)
            sql = "insert into follow_follow (follower_id, datetime, user_id) values (%s, now(), %s) "
            with connection.cursor() as cursor:
                cursor.execute(sql, [request.user.id, user_to_follow.id])

            UserRelation(from_user=request.user, to_user=user_to_follow, first_name=user_to_follow.first_name, last_name=user_to_follow.last_name, source=2, active=True).save()

            #send([user],'new_follower',{'user':user,'following_date':datetime.now(),'owner':request.user}, on_site=True, sender=request.user, summitlog=None, summitlog_comment=None, receiver=user)
            #followers = Follow.objects.get_followers_for_object(request.user).exclude(id = user.id)
            #followers = request.user.person.get_followers_for_user().exclude(id = user.id)
            #send(followers,'new_following_follows',{'user':request.user,'following_date':follow_object.datetime,'user_following':user}, on_site=True, sender=request.user, summitlog=None, summitlog_comment=None, receiver=user)
            #send([request.user],'new_follower_me',{'user':request.user,'following_date':follow_object.datetime,'following':user[0]},user[0])
            return HttpResponse('True')
        else:
            return HttpResponse('False')
    else:
        return HttpResponse('False')

def unfollow_user(request, user_id):
    user = User.objects.filter(id=user_id)

    if user.count() > 0:
        for u in user:
            followed_user = u
        if not followed_user.person.is_user_following(request.user):
            return HttpResponse('False')
        else:
            #Follow.objects.get_object(request.user, user[0]).delete()
            sql = "delete from follow_follow where user_id = %s and follower_id = %s "
            with connection.cursor() as cursor:
                cursor.execute(sql, [followed_user.id, request.user.id])
            ur = UserRelation.objects.filter(from_user=request.user, to_user=user[0])
            if ur:
                ur = ur[0]
                ur.active = False
                ur.save()
            return HttpResponse('True')
    else:
        return HttpResponse('False')

#this is the window post registration.
@login_required
def sign_up_follow_other_users(request):
    user = request.user

    if request.method == 'POST':
        selected_users = request.POST.getlist('selected_users')
        source = PEAKERY_SOURCE

        for selected_id in selected_users:
            u = User.objects.get(id=selected_id)
            f = Follow.objects.get_or_create(request.user, u)

            #send([u],'new_follower',{'user':u,'following_date':datetime.now(),'owner':request.user}, request.user)
            #followers = Follow.objects.get_followers_for_object(request.user).exclude(id = u.id)
            followers = request.user.person.get_followers_for_user().exclude(id = u.id)
            #send(followers,'new_following_follows',{'user':request.user,'following_date':datetime.now(),'user_following':u},request.user)
            #send([request.user],'new_follower_me',{'user':request.user,'following_date':datetime.now(),'following':u}, u)

            UserRelation(from_user=request.user, to_user=u, first_name=u.first_name, last_name=u.last_name, source=source, active=True).save()

        if request.session.get('redirect_summit', None):
            request.session['trigger_redirect'] = True
            url = request.session.get('redirect_summit', None)
            del request.session['redirect_summit']
            return HttpResponseRedirect(url)
        else:
            return HttpResponseRedirect(reverse('user_profile'))

    return render(request, 'accounts/sign_up_follow_other_users.html', {'user':user, 'users':[], 'friends_count':0,'show_message':True })


@login_required
def sign_up_welcome(request):
    return render(request, 'accounts/sign_up_welcome.html', {})

@login_required
def sign_up_choose_name(request,step=1):
    step = int(step)
    if request.method == 'POST':
        form = PersonUserNameForm(data = request.POST)
        valid = False
        if form.is_valid():
            valid = True
            username = form.cleaned_data['username']
            request.user.username = username
            request.user.save()
            request.method = "GET"
        else:
            if (request.user.username == form.data['username']):
                valid = True

        if (valid):
            request.user.person.send_welcome_email()
            request.method = "GET"
            return sign_up_welcome(request)
    else:
        form = PersonUserNameForm(initial = {'username':request.user.username})
    return render(request, 'accounts/sign_up_choose_name.html',{'form':form,'step':step})


@login_required
def sharing_options(request):
    option_key = "sharing_%s" %( request.GET['option'])

    if option_key == 'sharing_email':
        request.session['sharing_email'] = False
        return HttpResponse(False)

    option = request.session.get(option_key,False)
    option = not option
    request.session[option_key] = option

    return HttpResponse(option)

def generate_username(user):
    if user:
        names = '%s%s' % (user.first_name, user.last_name)
        username = slugify(names)

    if User.objects.filter(username__iexact=username).count() > 0:
        users = User.objects.filter(username__icontains=username).order_by('username').values('username')
        if len(users) > 0:
            username = '%s%s' % (username, len(users))
        else:
            username = '%s%s' % (username, 1)

    return username

def user_photos(request, username=None):

    if username is None:
        username = request.user.username

    if username == request.user.username:
        if request.user.is_authenticated:
            bagger = request.user
        else:
            return HttpResponse("User doesn't exist")
    else:
        bagger = get_object_or_404(User, username__iexact=username)

    profile = bagger.person

    #get avatar
    sql = "select " + \
        "a.id, " + \
        "coalesce(replace(replace(b.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'),'img/default-user.png') as avatar_url " + \
        "from auth_user a " + \
        "left join avatar_avatar b on b.user_id = a.id " + \
        "where a.id = %s "

    with connection.cursor() as cursor:
        cursor.execute(sql, [bagger.person.user.id])
        avatar = dictfetchall(cursor)[0]

    count_sql = "select case when d.id > 0 and date_entered = true then to_char(d.date, 'YYYY') else to_char(aa.created, 'YYYY') end as year, case when d.id > 0 and date_entered = true then to_char(d.date, 'YYYY') else to_char(aa.created, 'YYYY') end as year_name, count(distinct aa.id) as photos_count " + \
        "from items_itemphoto aa " + \
        "join auth_user ab on ab.id = aa.user_id " + \
        "join items_item c on c.id = aa.item_id " + \
        "left join items_summitlog d on d.id = aa.summit_log_id " + \
        "left join (select user_id, image, min(summit_log_id) as min_summit_log_id from items_itemphoto x group by user_id, image) x on x.user_id = aa.user_id and x.min_summit_log_id = aa.summit_log_id " + \
        "where aa.user_id = %s " + \
        "group by year, year_name " + \
        "order by year_name "

    with connection.cursor() as cursor:
        cursor.execute(count_sql, [bagger.person.user.id])
        years = dictfetchall(cursor)

    following = False
    if request.user.is_authenticated:
        following = profile.is_user_following(request.user)

    subnav_photos_style = 'color: #f24100;'

    nav_page_name = bagger

    peak_photo_count = 0
    for y in years:
        peak_photo_count = peak_photo_count + y['photos_count']

    return render(request, 'accounts/user_photos.html',{
        'peak_photo_count':peak_photo_count,
        'years': years,
        'username': username,
        'bagger': bagger,
        'profile': profile,
        'avatar': avatar,
        'following': following,
        'subnav_photos_style': subnav_photos_style,
        'nav_page_name': nav_page_name
    })


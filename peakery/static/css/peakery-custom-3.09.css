/*****************/
/*    MODALS     */
/*****************/

nav {
  font-family: -apple-system, system-ui, BlinkMacSystemFont, Roboto, "Segoe UI", Avenir, sans-serif;
}

.join-modal-body {
  background-color: #f6f6f6 !important;
}

@media screen and (max-width: 1279px) {
  body.modal-open {
      overflow: visible;
  }
}

@media screen and (min-width: 1280px) {
  body.modal-open {
      overflow: visible;
  }
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .modal-dialog {
      position: relative;
      width: auto;
      margin: 10px auto;
  }
}

/*****************/
/* LAYOUT STYLES */
/*****************/

* {padding: 0; margin: 0;}
html,body {
  font-family: -apple-system, system-ui, BlinkMacSys<PERSON><PERSON><PERSON>, <PERSON><PERSON>, "Segoe UI", Avenir, sans-serif;
  font-weight: 300; 
  font-size: 100%; 
  line-height: 2; 
  letter-spacing: .03125em;
  color: #333;
  background-color: #333;
  height: 100%; 
  min-height: 100%;}
  
html 
 {
 height: 100%;
 margin-bottom: 1px;
 overflow-y: scroll;
 overflow-x: hidden;
 }
 
 a {
   cursor: pointer;
 }
 
 body {
   -webkit-overflow-scrolling: touch;
   /*overflow: auto;*/
 }
 
 td, th {
    letter-spacing: 0.03125em;
}

.nav-explore-container-open {
  position: relative;
  height: 60px;
  float: right;
  width: 290px;
  margin-bottom: -10px;
}

.nav-profile-hr {
  margin-top: 0px;
  margin-bottom: 0px;
  margin-left: 20px;
  margin-right: 20px;
  border-color: #505050;
}

.filter-bar {
    position: fixed;
    top: 100px;
    left: 0px;
    width: 100%;
    margin-left: 0px;
    z-index: 1020;
}
@media screen and (min-width: 1px) and (max-width: 767px) {
  .filter-bar {
    background-color: #eee;
    font-size: 12px; 
    font-weight: 500;
    letter-spacing: 0.03125em;
  }
}  
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .filter-bar {
    background-color: #eee;
    font-size: 12px; 
    font-weight: 500;
    letter-spacing: 0.03125em;
  }
}  

#main-nav-logo {
  transition: 0.3s ease;
}

#main-nav-logo:hover {
  -webkit-transform: scale(1.3);
  transform: scale(1.3);
  transition: 0.2s ease;
}

#main-content {
  height: 100%;
}

table {
  background-color: #f1f1f1;
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: #f6f6f6;
}

.help-block {
  color: #ff0000;
  font-weight: 500;
}

.profile-stats-table {
  background-color: transparent;
}

.navbar-toggle {
  margin-right: 10px;
}  

 input {
    letter-spacing: 0.03125em;
}

.btn:focus, .btn:active:focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn.active.focus {
  outline: none;
  box-shadow: none;
}

.table-striped > tbody > tr:nth-of-type(even) {
    background-color: #fff;
}

.table-bordered > thead > tr > th, .table-bordered > tbody > tr > th, .table-bordered > tfoot > tr > th, .table-bordered > thead > tr > td, .table-bordered > tbody > tr > td, .table-bordered > tfoot > tr > td {
    border: 1px solid #eee;
    padding: 5px;
}
 
input[disabled] { 
  -webkit-text-fill-color: #aaa;
  -webkit-opacity: 1;
} 

input[type="text"] {
  border-radius: 0;
  border: 1px solid #CCC;
  font-size: 14px;
  padding: 8px 10px;
}

input[type="number"] {
  border-radius: 0;
  border: 1px solid #CCC;
  font-size: 14px;
  padding: 8px 10px;
}

input[type="search"] {
  border-radius: 0;
  border: 1px solid #CCC;
  font-size: 14px;
  padding: 8px 10px;
}

input[type="url"] {
  border-radius: 0;
  border: 1px solid #CCC;
  font-size: 14px;
  padding: 8px 10px;
}

input[type="email"] {
  border-radius: 0;
  border: 1px solid #CCC;
  font-size: 14px;
  padding: 8px 10px;
}

input[type="radio"] {
  transform: scale(1.5);
}

input[type="text"], input[type="search"], input[type="password"], input[type="url"], input[type="email"] {
  -webkit-appearance: none;
  -webkit-border-radius:0; 
  border-radius:0;
}

textarea {
  -webkit-appearance: none;
  -webkit-border-radius:0; 
  border-radius:0;
  border: 1px solid #CCC;
}

/* Move down content because we have a fixed navbar that is 50px tall */
body {
  padding-top: 50px;
  background-color:#333;
}

.fixed-subnav-header {
  margin-top: 0px !important;
}

.news_icon_text {
  margin-left: 25px;
  font-weight: 700;
  color: #fff;
  background-color: #00b1f2;
  border: solid 2px #00b1f2;
  padding: 3px;
  border-radius: 10px;
  font-size: 12px;
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  
  .news_icon_text {
    margin-left: 8px;
    font-weight: 500;
    color: #fff;
    background-color: #00b1f2;
    border: solid 3px #00b1f2;
    padding: 2px 6px 2px 6px;
    border-radius: 100%;
    font-size: 11px;
    text-align: center;
    width: 27px;
  }
  
}

@media screen and (min-width: 768px) and (max-width: 1023px) {

  .news_icon_text {
    margin-top: 2px;
    margin-left: 0px;
    font-weight: 500;
    color: #fff;
    background-color: #00b1f2;
    border: none;
    padding: 2px 2px 1px 2px;
    border-radius: 100%;
    font-size: 12px;
    text-align: center;
    width: 18px;
    line-height: 22px;
  }
}

@media screen and (min-width: 1024px) {
  
  .news_icon_text {
    margin-left: 0px;
    font-weight: 500;
    color: #fff;
    background-color: #00b1f2;
    border: solid 0px #00b1f2;
    padding: 2px 1px 1px 2px;
    border-radius: 100%;
    font-size: 12px;
    text-align: center;
    width: 26px;
    line-height: 20px;
  }
  
}  

@media screen and (min-width: 1px) and (max-width: 767px) {
  .caret {
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
    border-top: 4px dashed;
    margin-top: -1px;
    margin-left: 5px !important;
  }
  .stat-box {
    padding-left: 10px;
    padding-right: 10px;
  }
  .no-border-mobile {
    border: none;
  }
}

@media screen and (min-width: 768px) {
  .caret {
    border-right: 5px solid transparent;
    border-left: 5px solid transparent;
    border-top: 5px dashed;
    margin-top: -3px;
    margin-left: 6px !important;
  }
}

.container {
  width: 100%;
  max-width: 1280px;
}

.full-width-container {
  width: 100%;
  max-width: 100%;
}

.content-container {
  height: 100vh;
  background-color: #ffffff;
}

#content-holder{
    min-height: 100%;
    position:relative;
}

@media screen and (min-width: 768px) {
  #content-body{
      padding-bottom: 0px; /* height of footer */
      margin-top: 71px;
      min-height: 100%;
      height: 100%;
      background-color:#333;
  }
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  #content-body{
      padding-bottom: 0px; /* height of footer */
      margin-top: 71px;
      min-height: 100%;
      height: 100%;
      background-color:#333;
  }
}

.item-row {
  background-color: #fff;
}

.main-header-row {
  background-color: #fff;
  border-bottom: solid 1px #cfcfcf;
}

.map-header-row {
  position: absolute;
  left: 15px;
  width: 100%;
}

.sub-header-row {
  background-color: #f2f2f2;
  border-bottom: solid 1px #cfcfcf;
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .pad-mobile {
    padding-bottom: 130px;
  }
}

.map-sub-header-row {
  position: absolute;
  left: 15px;
  top: 80px;
  width: 100%;
}

.col-full-width {
  padding-left: 0px;
  padding-right: 0px;
}

.row-full-width {
  margin-left: 0px;
  margin-right: 0px;
}

.border-right {
  border-right: solid 1px #cfcfcf;
}

.border-left {
  border-left: solid 1px #cfcfcf;
}

.border-bottom {
  border-bottom: solid 1px #cfcfcf;
}

.border-top {
  border-top: solid 1px #cfcfcf;
}

.memberNumberCircle {
  display:inline-block;
  line-height:0px;
  border-radius:50%;
  font-size:20px;
  background-color: #00B1F2;
  position: relative;
  top: -2px;
  font-weight: 500;
}

.memberNumberCircle span {
  display: inline-block;
  padding-top: 50%;
  padding-bottom: 48%;
  width: 30px;
  color: #fff;
  font-size: 14px;
  text-align: center;
  padding-left: 2px;
}

.route-rank-1 {
  background-color: #fc202e;
  color: #fc202e;
}
.route-rank-2 {
  background-color: #f28300;
  color: #f28300;
}
.route-rank-3 {
  background-color: #f0b800;
  color: #f0b800;
}
.route-rank-4 {
  background-color: #00b330;
  color: #00b330;
}
.route-rank-5 {
  background-color: #00b1f2;
  color: #00b1f2;
}
.route-rank-6 {
  background-color: #8d00f2;
  color: #8d00f2;
}
.route-rank-7 {
  background-color: #f200f2;
  color: #f200f2;
}
.route-rank-8 {
  background-color: #fc202e;
  color: #fc202e;
}
.route-rank-9 {
  background-color: #f28300;
  color: #f28300;
}
.route-rank-10 {
  background-color: #f0b800;
  color: #f0b800;
}
.route-rank-11 {
  background-color: #00b330;
  color: #00b330;
}
.route-rank-12 {
  background-color: #00b1f2;
  color: #00b1f2;
}
.route-rank-13 {
  background-color: #8d00f2;
  color: #8d00f2;
}
.route-rank-14 {
  background-color: #f200f2;
  color: #f200f2;
}
.route-rank-15 {
  background-color: #fc202e;
  color: #fc202e;
}
.route-rank-16 {
  background-color: #f28300;
  color: #f28300;
}
.route-rank-17 {
  background-color: #f0b800;
  color: #f0b800;
}
.route-rank-18 {
  background-color: #00b330;
  color: #00b330;
}
.route-rank-19 {
  background-color: #00b1f2;
  color: #00b1f2;
}
.route-rank-20 {
  background-color: #8d00f2;
  color: #8d00f2;
}
.route-rank-21 {
  background-color: #f200f2;
  color: #f200f2;
}
.route-rank-22 {
  background-color: #fc202e;
  color: #fc202e;
}
.route-rank-23 {
  background-color: #f28300;
  color: #f28300;
}
.route-rank-24 {
  background-color: #f0b800;
  color: #f0b800;
}
.route-rank-25 {
  background-color: #00b330;
  color: #00b330;
}
.route-rank-26 {
  background-color: #00b1f2;
  color: #00b1f2;
}
.route-rank-27 {
  background-color: #8d00f2;
  color: #8d00f2;
}
.route-rank-28 {
  background-color: #f200f2;
  color: #f200f2;
}
.route-rank-29 {
  background-color: #fc202e;
  color: #fc202e;
}
.route-rank-30 {
  background-color: #f28300;
  color: #f28300;
}
.route-rank-31 {
  background-color: #f0b800;
  color: #f0b800;
}
.route-rank-32 {
  background-color: #00b330;
  color: #00b330;
}
.route-rank-33 {
  background-color: #00b1f2;
  color: #00b1f2;
}
.route-rank-34 {
  background-color: #8d00f2;
  color: #8d00f2;
}
.route-rank-35 {
  background-color: #f200f2;
  color: #f200f2;
}


@media screen and (min-width: 1280px) {
  #content-body {
    background-color: #222;
  }
  .subnav {
    z-index: 999;
    width: 1280px;
  }
  .navbar-brand {
    margin-right: 42px;
  }
  .nav > li > a {
    padding: 0px 35px;
  }
}
@media screen and (max-width: 1279px) {
  #content-body {
    background-color: white;
  }
  .subnav {
    z-index: 999;
  }
  .affix {
    width: 100%;
  }
  .navbar-brand {
    margin-right: 42px;
  }
  .nav > li > a {
    padding: 0px 15px;
  }
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  #main {
    background-color:#333;
    min-height: 100%;
    height: 100%;
  }
  .topo-bg {
    background: url("https://s3-us-west-1.amazonaws.com/peakery-static/img/site-background-mobile.png");
  }
  .topo-bg-nogradient {
    background: url("https://s3-us-west-1.amazonaws.com/peakery-static/img/white-topo-background.jpg");
  }
  .navbar {
    /*min-height: 49px;
    height: 49px;*/
  }
}

@media screen and (min-width: 768px) and (max-width: 1279px) {
  #main {
    background-color:#333;
    min-height: 100%;
    height: 100%;
  }
  .topo-bg {
    background: url("https://s3-us-west-1.amazonaws.com/peakery-static/img/site-background.png");
  }
  .topo-bg-nogradient {
    background: url("https://s3-us-west-1.amazonaws.com/peakery-static/img/white-topo-background.jpg");
  }
}

@media screen and (min-width: 1280px) {
  #main {
    background-color:#333;
    min-height: 100%;
    height: 100%;
  }
  .topo-bg {
    background: url("https://s3-us-west-1.amazonaws.com/peakery-static/img/site-background.png");
  }
  .topo-bg-nogradient {
    background: url("https://s3-us-west-1.amazonaws.com/peakery-static/img/white-topo-background.jpg");
  }
  .background-row {
    background-color:#333;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
  }
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .navbar-fixed-top {
    background-color:#333;
    padding-bottom: 0px;
  }
}

@media screen and (min-width: 768px) {
  .navbar-fixed-top {
    background-image: url("https://s3-us-west-1.amazonaws.com/peakery-static/img/top-nav-bg.png");
  }
}

.navbar-inverse {
  background-color:#333;
}

.dark-background-row {
    background-color:#333;
  }
  
/***************************/
/* Absolute Center Spinner */
/***************************/   
  
.loading {
  position: fixed;
  z-index: 9999;
  height: 2em;
  width: 2em;
  overflow: show;
  margin: auto;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

/* Transparent Overlay */
.loading:before {
  content: '';
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.3);
}

/* :not(:required) hides these rules from IE9 and below */
.loading:not(:required) {
  /* hide "loading..." text */
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.loading:not(:required):after {
  content: '';
  display: block;
  font-size: 10px;
  width: 1em;
  height: 1em;
  margin-top: -0.5em;
  -webkit-animation: spinner 1500ms infinite linear;
  -moz-animation: spinner 1500ms infinite linear;
  -ms-animation: spinner 1500ms infinite linear;
  -o-animation: spinner 1500ms infinite linear;
  animation: spinner 1500ms infinite linear;
  border-radius: 0.5em;
  -webkit-box-shadow: rgba(255, 255, 255, 1) 1.5em 0 0 0, rgba(255, 255, 255, 1) 1.1em 1.1em 0 0, rgba(255, 255, 255, 1) 0 1.5em 0 0, rgba(255, 255, 255, 1) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.5) -1.5em 0 0 0, rgba(0, 0, 0, 0.5) -1.1em -1.1em 0 0, rgba(255, 255, 255, 1) 0 -1.5em 0 0, rgba(255, 255, 255, 1) 1.1em -1.1em 0 0;
  box-shadow: rgba(255, 255, 255, 1) 1.5em 0 0 0, rgba(255, 255, 255, 1) 1.1em 1.1em 0 0, rgba(255, 255, 255, 1) 0 1.5em 0 0, rgba(255, 255, 255, 1) -1.1em 1.1em 0 0, rgba(255, 255, 255, 1) -1.5em 0 0 0, rgba(255, 255, 255, 1) -1.1em -1.1em 0 0, rgba(255, 255, 255, 1) 0 -1.5em 0 0, rgba(255, 255, 255, 1) 1.1em -1.1em 0 0;
}

/* Animation */

@-webkit-keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-moz-keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-o-keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}  
  
/************************/
/*     NO-UI SLIDER     */
/************************/  

@media screen and (min-width: 1px) and (max-width: 768px) {
  .noUi-horizontal .noUi-handle {
    width: 40px;
    height: 40px;
    left: -26px;
    top: -19px;
    background-color: transparent;
    background-image: url(https://s3-us-west-1.amazonaws.com/peakery-static/img/search-drag-handle-blue.png);
    background-size: contain;
  }
}
@media screen and (min-width: 769px) {
  .noUi-horizontal .noUi-handle {
    width: 40px;
    height: 40px;
    left: -26px;
    top: -19px;
    background-color: transparent;
    background-size: contain;
    background-image: url(https://s3-us-west-1.amazonaws.com/peakery-static/img/search-drag-handle-blue.png);
  }
} 

.noUi-handle {
    border: none;
    /*border-radius: 8px;
    background: #00b2f2;*/
    cursor: pointer;
    box-shadow: none;
}

.noUi-handle::after, .noUi-handle::before {
    content: none;
}

.noUi-target {
    border: none;
    cursor: pointer;
}

.noUi-connect {
    background: #00b2f2;
}   

/************************/
/*     CHALLENGES       */
/************************/ 

@media screen and (min-width: 1200px) {
  #challenges-overview > p {
    font-size: 18px;
    line-height: 30px;
    margin-top: 40px;
  }
  #challenges-overview > div > a {
    font-size: 18px;
    line-height: 30px;
    margin-top: 40px;
  }
  #add-peak-challenge {
    position: absolute;
    bottom: 20px;
  }
  .member-username {
    margin-top: 0px;
    font-size: 20px;
    font-weight: 500;
  }
  .challenge-name {
    margin-top: 0px;
    font-size: 20px;
    font-weight: 500;
  }
  .progress {
    margin-top: 15px;
  }
  .member-last-summit {
    margin-left: 4px;
    font-size: 14px;
    margin-top: 0px;
  }
  .challenge-last-summit {
    margin-left: 4px;
    font-size: 14px;
    margin-top: 0px;
  }
  .member-username-two-bars {
    margin-top: 0px;
    font-size: 20px;
    font-weight: 500;
  }
  .challenge-name-two-bars {
    margin-top: 0px;
    font-size: 20px;
    font-weight: 500;
  }
  .progress-two-bars {
    margin-top: 15px;
  }
  .member-last-summit-two-bars {
    margin-left: 4px;
    font-size: 14px;
    margin-top: 0px;
  }
  .challenge-last-summit-two-bars {
    margin-left: 4px;
    font-size: 14px;
    margin-top: 0px;
  }
}

@media screen and (min-width: 1024px) and (max-width: 1199px) {
  #challenges-overview > p {
    font-size: 15px;
    line-height: 24px;
    margin-top: 15px;
  }
  #challenges-overview > div > a {
    font-size: 15px;
    line-height: 24px;
    margin-top: 15px;
  }
  #add-peak-challenge {
    position: absolute;
    bottom: 10px;
  }
  .member-username {
    margin-top: 0px;
    font-size: 18px;
    font-weight: 500;
  }
  .challenge-name {
    margin-top: 0px;
    font-size: 18px;
    font-weight: 500;
  }
  .progress {
    margin-top: 10px;
  }
  .member-last-summit {
    margin-left: 4px;
    font-size: 12px;
    margin-top: 0px;
  }
  .challenge-last-summit {
    margin-left: 4px;
    font-size: 12px;
    margin-top: 0px;
  }
  .member-username-two-bars {
    margin-top: 0px;
    font-size: 18px;
    font-weight: 500;
  }
  .challenge-name-two-bars {
    margin-top: 0px;
    font-size: 18px;
    font-weight: 500;
  }
  .progress-two-bars {
    margin-top: 10px;
  }
  .member-last-summit-two-bars {
    margin-left: 4px;
    font-size: 12px;
    margin-top: 0px;
  }
  .challenge-last-summit-two-bars {
    margin-left: 4px;
    font-size: 12px;
    margin-top: 0px;
  }
}

@media screen and (min-width: 992px) and (max-width: 1023px) {
  #challenges-overview > p {
    font-size: 15px;
    line-height: 24px;
    margin-top: 5px;
  }
  #challenges-overview > div > a {
    font-size: 15px;
    line-height: 24px;
    margin-top: 5px;
  }
  #challenges-overview {
    height: auto !important;
  }
  #add-peak-challenge {
    position: relative;
    margin-bottom: 10px;
  }
  .member-username {
    margin-top: 0px;
    font-size: 16px;
    font-weight: 500;
  }
  .challenge-name {
    margin-top: 0px;
    font-size: 16px;
    font-weight: 500;
  }
  .progress {
    margin-top: 10px;
  }
  .member-last-summit {
    margin-left: 4px;
    font-size: 12px;
    margin-top: -5px;
  }
  .challenge-last-summit {
    margin-left: 4px;
    font-size: 12px;
    margin-top: 0px;
  }
  .member-username-two-bars {
    margin-top: 0px;
    font-size: 16px;
    font-weight: 500;
  }
  .challenge-name-two-bars {
    margin-top: 0px;
    font-size: 16px;
    font-weight: 500;
  }
  .progress-two-bars {
    margin-top: 10px;
  }
  .member-last-summit-two-bars {
    margin-left: 4px;
    font-size: 12px;
    margin-top: -5px;
  }
  .challenge-last-summit-two-bars {
    margin-left: 4px;
    font-size: 12px;
    margin-top: 0px;
  }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
  #challenges-overview > p {
    font-size: 15px;
    line-height: 24px;
    margin-top: 5px;
  }
  #challenges-overview > div > a {
    font-size: 15px;
    line-height: 24px;
    margin-top: 5px;
  }
  #challenges-overview {
    height: auto !important;
  }
  #add-peak-challenge {
    position: relative;
    margin-bottom: 10px;
  }
  .member-username {
    margin-top: 0px;
    font-size: 16px;
    font-weight: 500;
  }
  .challenge-name {
    margin-top: 0px;
    font-size: 16px;
    font-weight: 500;
  }
  .progress {
    margin-top: 0px;
    margin-bottom: 10px;
  }
  .member-last-summit {
    margin-left: 2px;
    font-size: 12px;
    margin-top: -5px;
  }
  .challenge-last-summit {
    margin-left: 2px;
    font-size: 12px;
    margin-top: 0px;
  }
  .member-username-two-bars {
    margin-top: 0px;
    font-size: 16px;
    font-weight: 500;
  }
  .challenge-name-two-bars {
    margin-top: 0px;
    font-size: 16px;
    font-weight: 500;
  }
  .progress-two-bars {
    margin-top: 0px;
    margin-bottom: 10px;
  }
  .member-last-summit-two-bars {
    margin-left: 2px;
    font-size: 12px;
    margin-top: -5px;
  }
  .challenge-last-summit-two-bars {
    margin-left: 2px;
    font-size: 12px;
    margin-top: 0px;
  }
}

@media screen and (min-width: 480px) and (max-width: 767px) {
  #challenges-overview > p {
    font-size: 15px;
    line-height: 24px;
    margin-top: 5px;
  }
  #challenges-overview > div > a {
    font-size: 15px;
    line-height: 24px;
    margin-top: 5px;
  }
  #challenges-overview {
    height: auto !important;
  }
  #add-peak-challenge {
    position: relative;
    margin-bottom: 10px;
  }
  .member-username {
    margin-top: 0px;
    font-size: 12px;
    font-weight: 500;
  }
  .challenge-name {
    margin-top: 0px;
    font-size: 12px;
    font-weight: 500;
  }
  .progress {
    margin-top: 0px;
    margin-bottom: 5px;
  }
  .member-last-summit {
    margin-left: 2px;
    font-size: 10px;
    margin-top: -3px;
  }
  .challenge-last-summit {
    margin-left: 2px;
    font-size: 10px;
    margin-top: -3px;
  }
  .member-username-two-bars {
    margin-top: 0px;
    font-size: 12px;
    font-weight: 500;
  }
  .challenge-name-two-bars {
    margin-top: 0px;
    font-size: 12px;
    font-weight: 500;
  }
  .progress-two-bars {
    margin-top: 0px;
    margin-bottom: 5px;
  }
  .member-last-summit-two-bars {
    margin-left: 2px;
    font-size: 10px;
    margin-top: -3px;
  }
  .challenge-last-summit-two-bars {
    margin-left: 2px;
    font-size: 10px;
    margin-top: -3px;
  }
}

@media screen and (min-width: 1px) and (max-width: 479px) {
  #challenges-overview > p {
    font-size: 15px;
    line-height: 24px;
    margin-top: 5px;
  }
  #challenges-overview > div > a {
    font-size: 15px;
    line-height: 24px;
    margin-top: 5px;
  }
  #challenges-overview {
    height: auto !important;
  }
  #add-peak-challenge {
    position: relative;
    margin-bottom: 10px;
  }
  .member-username {
    margin-top: 0px;
    font-size: 12px;
    font-weight: 500;
  }
  .challenge-name {
    margin-top: 0px;
    font-size: 12px;
    font-weight: 500;
  }
  .progress {
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .member-last-summit {
    margin-left: 2px;
    font-size: 10px;
    margin-top: -3px;
  }
  .challenge-last-summit {
    margin-left: 2px;
    font-size: 10px;
    margin-top: -3px;
  }
  .member-username-two-bars {
    margin-top: 0px;
    font-size: 12px;
    font-weight: 500;
  }
  .challenge-name-two-bars {
    margin-top: 0px;
    font-size: 12px;
    font-weight: 500;
  }
  .progress-two-bars {
    margin-top: 0px;
    margin-bottom: 5px;
  }
  .member-last-summit-two-bars {
    margin-left: 2px;
    font-size: 10px;
    margin-top: -3px;
  }
  .challenge-last-summit-two-bars {
    margin-left: 2px;
    font-size: 10px;
    margin-top: -3px;
  }
}

/************************/
/*     PHOTO GRIDS      */
/************************/

@media screen and (max-width: 767px) and (min-width: 1px) {
  .summit-card-photo-1 {
    border-right: solid 1px #e0e0e0;
    border-bottom: solid 1px #e0e0e0;
  }
  .summit-card-photo-2 {
    border-bottom: solid 1px #e0e0e0;
  }
  .summit-card-photo-3 {
    border-right: solid 1px #e0e0e0;
  }
}
@media screen and (max-width: 1023px) and (min-width: 768px) {
  .summit-card-photo-1 {
    border-right: solid 1px #e0e0e0;
  }
  .summit-card-photo-2 {
    border-right: solid 1px #e0e0e0;
  }
  .summit-card-photo-3 {
    border-right: solid 1px #e0e0e0;
  }
}
@media screen and (min-width: 1024px) {
  .summit-card-photo-1 {
    border-right: solid 1px #e0e0e0;
  }
  .summit-card-photo-2 {
    border-right: solid 1px #e0e0e0;
  }
  .summit-card-photo-3 {
    border-right: solid 1px #e0e0e0;
  }
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .photo-grid-1 {
    border-top: none !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-2 {
    border-top: none !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-3 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-4 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-5 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-6 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-7 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-8 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-9 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-10 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-11 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-12 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-13 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-14 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-15 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-16 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-17 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-18 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-19 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-20 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-21 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-22 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-23 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-24 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
}  

@media screen and (min-width: 768px) and (max-width: 1023px) {
  .four-photo-grid-4 {
    display: none;
  }
  .photo-grid-1 {
    border-top: none !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-2 {
    border-top: none !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-3 {
    border-top: none !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-4 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-5 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-6 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-7 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-8 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-9 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-10 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-11 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-12 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-13 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-14 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-15 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-16 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-17 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-18 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-19 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-20 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-21 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-22 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-23 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-24 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
}

@media screen and (min-width: 1023px) {
  .photo-grid-1 {
    border-top: none !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-2 {
    border-top: none !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-3 {
    border-top: none !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-4 {
    border-top: none !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-5 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-6 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-7 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-8 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-9 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-10 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-11 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-12 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-13 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-14 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-15 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-16 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-17 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-18 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-19 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-20 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-21 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-22 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-23 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: solid 2px #e0e0e0 !important;
    border-bottom: none !important;
    border-left: none !important;
  }
  .photo-grid-24 {
    border-top: solid 2px #e0e0e0 !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
  }
}  

/************************/
/*    HEADERS/TITLES    */
/************************/

h2, .h2 {
  font-size: 140%;
  font-weight: 400;
}

.page-title {
  font-size: 21px;
  font-weight: 500;
}

.page-subtitle {
  font-size: 17px;
  font-weight: 500;
  color: #f24100;
}

.card-header {
  font-weight: 500;
  font-size: 24px;
}

.card-subheader {
  color: #999;
  font-size: 14px;
  font-weight: 300;
}

.pagination {
  margin: 15px 0px 0px 15px;
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .section-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    letter-spacing: 1.0px;
  }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  .section-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    letter-spacing: 1.0px;
  }
}

@media screen and (min-width: 1024px) {
  .section-title {
    font-size: 20px;
    font-weight: 500;
    color: #333;
    letter-spacing: 1.0px;
  }
}

@media screen and (min-width: 768px) {
  .section-title-li {
    margin-top: 2px !important;
  }
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .section-title-li {
    margin-top: 5px !important;
  }
}

/*.section-title-stats {
  margin-top: -8px !important;
}*/

/******************/
/*     MODALS     */
/******************/

div#password_reset_error {
    background: none repeat scroll 0 0 #FFDFDF;
    border: 1px solid #999;
    color: red;
    font-size: 11px;
    line-height: 14px;
    margin-bottom: 15px;
    padding-top: 14px;
    padding-bottom: 2px;
    text-align: center !important;
}

ul.errorlist {
  color: #ff0000;
  text-align: center !important;
}

.modal-content {
  border-radius: 0px;
  background-color: #333333;
}

.modal-header {
  border-bottom: none;
}

.modal-footer {
  background-color: #cccccc;
  text-align: center;
  height: 160px;
  padding-top: 50px;
}

.edit-profile-modal-footer {
  background-color: #333333;
  text-align: left;
  height: 75px;
  padding-top: 10px;
  padding-left: 10px;
}

.accounts-sign-up-modal-footer {
  background-color: #333333;
  text-align: left;
  height: 15px;
  padding-top: 10px;
  padding-left: 10px;
}

.modal-title {
  background-color: #333333;
  color: #fff;
  margin-top: 10px;
  margin-bottom: 10px;
}

.modal-body {
  background-color: #f6f6f6;
}

.modal-close {
  color: #fff;
  opacity: 1;
  font-size: 30px;
  line-height: 50px;
}

.modal-close:hover {
  color: #fff;
  opacity: 1;
}

.striped-row:nth-of-type(odd) {
    background: #f2f2f2;
}

.collapsing {
  -webkit-transition: height .2s ease;
       -o-transition: height .2s ease;
          transition: height .2s ease;
}

#darkness {
    background:rgba(0, 0, 0, 0.5);
    display:none;
    height:100%;
    left:0;
    position:absolute;
    top:0;
    width:100%;
    z-index:5;
}

/*****************/
/*     LINKS     */
/*****************/

a {
  color: #00B1F2;
}

a:hover {
  color: #00bbff;
  text-decoration: none;
}

a:focus {
  outline: none;
  text-decoration: none;
  font-weight: 500;
}

.ajax-link:hover {
  cursor:pointer;
}

.modal-link:hover {
  cursor:pointer;
}

.main-header-sub-links {
  font-size: 14px;
  font-weight: 300;
  color: #666;
  margin-left: 65px;
}

.region-header-sort-links {
  font-size: 14px;
  font-weight: 300;
  color: #666;
  margin-left: 50px;
}

@media screen and (max-width: 1023px) and (min-width: 1px) {
  #edit-highlights-link {
     font-size: 12px;
  }
}
@media screen and (min-width: 1024px) {
  #edit-highlights-link {
     font-size: 14px;
  }
}

.highlights-info-content {
  font-size: 16px;
  line-height: 28px;
}


@media screen and (min-width: 1200px) {
  .region-header-sub-links {
    font-size: 14px;
    font-weight: 300;
    color: #999;
    margin-left: 55px;
  }
  .btn {
    font-size: 14px;
  }
}

@media screen and (min-width: 1100px) and (max-width: 1199px) {
  .region-header-sub-links {
    font-size: 12px;
    font-weight: 300;
    color: #999;
    margin-left: 45px;
  }
  .btn {
    font-size: 12px;
  }
}

@media screen and (min-width: 1050px) and (max-width: 1099px) {
  .region-header-sub-links {
    font-size: 12px;
    font-weight: 300;
    color: #999;
    margin-left: 35px;
  }
  .btn {
    font-size: 12px;
  }
}

@media screen and (min-width: 1px) and (max-width: 1049px) {
  .region-header-sub-links {
    font-size: 12px;
    font-weight: 300;
    color: #999;
    margin-left: 25px;
  }
  .btn {
    font-size: 12px;
  }
}

.mobile-header-sub-links {
  font-size: 12px;
  font-weight: 300;
  color: #999;
}

.badge-header-sub-links {
  font-size: 15px;
  font-weight: 300;
  color: #000;
  margin-left: 45px;
}

/**********************/
/*       LIKING       */
/**********************/

.liked {
  background-image: url('https://s3-us-west-1.amazonaws.com/peakery-static/img/fa-heart_256_0_ff0000_none.png') !important;
}

@-webkit-keyframes blink {
    0% {
        opacity:1;
    }
    50% {
        opacity:.25;
    }
    100% {
        opacity:1;
    }
}
@-moz-keyframes blink {
    0% {
        opacity:1;
    }
    50% {
        opacity:.25;
    }
    100% {
        opacity:1;
    }
}
.liking {
-webkit-transition: all 1s ease-in;
    -moz-transition: all 1s ease-in;
    -o-transition: all 1s ease-in;
    -ms-transition: all 1s ease-in;
    transition: all 1s ease-in;
    
    -webkit-animation-direction: normal;
    -webkit-animation-duration: 2s;
    -webkit-animation-iteration-count: 1;
    -webkit-animation-name: fade-in;
    -webkit-animation-timing-function: ease-in;
    
-moz-animation-direction: normal;
    -moz-animation-duration: 2s;
    -moz-animation-iteration-count: 1;
    -moz-animation-name: fade-in;
    -moz-animation-timing-function: ease-in;    
}

/**********************/
/*       FORMS        */
/**********************/

select {
  letter-spacing: 0.03125em;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
input[type="number"] {
    -moz-appearance: textfield;
    -webkit-appearance: none;
    margin: 0;
}
input[type="number"]:hover,
input[type="number"]:focus {
    -moz-appearance: textfield;
    -webkit-appearance: none;
    margin: 0;
}

.cancel-x-button {
  cursor: pointer;
  color: #999;
}

.cancel-x-button:hover {
  color: #fff;
}

input[type="radio"] {
    margin-left: 20px;
    margin-right: 5px;
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  label.btn span {
    font-size: 16px;
  }
  label input[type="radio"] ~ i.far.fa-circle{
      color: #999;
      display: inline;
      position: relative;
      top: 5px;
  }
  label input[type="radio"] ~ i.far.fa-dot-circle{
      display: none;
  }
  label input[type="radio"]:checked ~ i.far.fa-circle{
      display: none;
  }
  label input[type="radio"]:checked ~ i.far.fa-dot-circle{
      color: #00b1f2;
      display: inline;
      position: relative;
      top: 5px;
  }
  label:hover input[type="radio"] ~ i.fa {
      color: #00b1f2;
  }
}  

@media screen and (min-width: 768px) {
  label.btn span {
    font-size: 18px;
  }
  label input[type="radio"] ~ i.far.fa-circle{
      color: #999;
      display: inline;
      position: relative;
      top: 4px;
  }
  label input[type="radio"] ~ i.far.fa-dot-circle{
      display: none;
  }
  label input[type="radio"]:checked ~ i.far.fa-circle{
      display: none;
  }
  label input[type="radio"]:checked ~ i.far.fa-dot-circle{
      color: #00b1f2;
      display: inline;
      position: relative;
      top: 4px;
  }
  label:hover input[type="radio"] ~ i.fa {
      color: #00b1f2;
  }
} 

div[data-toggle="buttons"] label.active{
    color: #333;
}

div[data-toggle="buttons"] label {
display: inline-block;
padding: 6px 30px;
padding-left: 1px;
margin-bottom: 0;
font-size: 14px;
font-weight: normal;
line-height: 2em;
text-align: left;
white-space: nowrap;
vertical-align: top;
cursor: pointer;
background: none;
border: 0px solid
#333;
border-radius: 3px;
color: #333;
-webkit-user-select: none;
-moz-user-select: none;
-ms-user-select: none;
-o-user-select: none;
user-select: none;
z-index: 1 !important;
}

div[data-toggle="buttons"] label:hover {
color: #333;
}

div[data-toggle="buttons"] label:active, div[data-toggle="buttons"] label.active {
-webkit-box-shadow: none;
box-shadow: none;
}

div[data-toggle="buttons"] label.active span {
  color: #333;
}

div[data-toggle="buttons"] label span {
  color: #999;
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .toggle-switch {
      width: 166px;
      height: 57px;
      float: left;
      position: relative;
      border: solid 1px #ccc;
      border-radius: 4px;
      margin: 0px 0px 15px 15px;
  }
  .cancel-x-button-div {
    margin-top: 26px;
    margin-right: 5px;
    font-size: 18px;
  }
  .save-changes-button-div {
    margin-top: 10px;
    margin-right: 26px;
  }
  .save-changes-button {
    height: 46px !important;
    font-size: 14px;
    margin-right: 0px;
    padding: 0 22px !important;
    font-weight: 500;
    letter-spacing: .03125em;
  }
  .form-header-bar {
    height: 70px;
    line-height: 70px;
    background-color: #333;
  }
  .form-header-bar-img {
    margin-top: -12px;
    margin-right: 20px;
    height: 70px;
  }
  .form-header-bar-img-div {
    float: left;
    margin-top: 12px;
  }
  #route_up_container select {
    background: transparent;
    width: 100%;
    padding: 5px;
    font-size: 14px;
    line-height: 1;
    border: 0;
    border-radius: 0;
    height: 55px;
    -webkit-appearance: none;
  }
}  

@media screen and (min-width: 768px) and (max-width: 1023px)  {
  .toggle-switch {
      width: 173px;
      height: 57px;
      float: left;
      position: relative;
      border: solid 1px #ccc;
      border-radius: 4px;
      margin: 0px 0px 15px 15px;
  }
  .cancel-x-button-div {
    margin-top: 26px;
    margin-right: 10px;
  }
  .save-changes-button-div {
    margin-top: 13px;
    margin-right: 20px;
  }
  .save-changes-button {
    height: 50px;
    font-size: 18px;
    margin-right: 20px;
    padding: 0 20px;
  }
  .form-header-bar {
    height: 80px;
    line-height: 80px;
    background-color: #333;
  }
  .form-header-bar-img {
    margin-top: -12px;
    margin-right: 20px;
    height: 80px;
  }
  .form-header-bar-img-div {
    float: left;
    margin-top: 12px;
  }
  #route_up_container select {
    background: transparent;
    width: 100%;
    padding: 5px;
    font-size: 16px;
    line-height: 1;
    border: 0;
    border-radius: 0;
    height: 55px;
    -webkit-appearance: none;
  }
}  

@media screen and (min-width: 1024px) {
  .toggle-switch {
      width: 196px;
      height: 57px;
      float: left;
      position: relative;
      border: solid 1px #ccc;
      border-radius: 4px;
      margin: 0px 0px 15px 15px;
  }
  .cancel-x-button-div {
    margin-top: 26px;
    margin-right: 10px;
  }
  .save-changes-button-div {
    margin-top: 13px;
    margin-right: 20px;
  }
  .save-changes-button {
    height: 50px;
    font-size: 18px;
    margin-right: 20px;
    padding: 0 30px;
  }
  .form-header-bar {
    height: 80px;
    line-height: 80px;
    background-color: #333;
  }
  .form-header-bar-img {
    margin-top: -12px;
    margin-right: 20px;
    height: 80px;
  }
  .form-header-bar-img-div {
    float: left;
    margin-top: 12px;
  }
  #route_up_container select {
    background: transparent;
    width: 100%;
    padding: 5px;
    font-size: 18px;
    line-height: 1;
    border: 0;
    border-radius: 0;
    height: 55px;
    -webkit-appearance: none;
  }
}  

.toggle-switch input {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: alpha(opacity=0);
    -moz-opacity: 0;
    opacity: 0;
    z-index: 100;
    position: absolute;
    width: 100%;
    height: 100%;
    cursor: pointer;
    background: #eee;
} 

.toggle-switch input:checked ~ label {
    /*background: #ff0000;*/
}

.toggle-switch label {
    background: #eaeaea;
    font-weight: 300 !important;
    font-size: 12px !important;
    border-radius: 4px !important;
    width: 100%;
    height: 100%;
    position: relative;
    display: block;
    text-align: center;
    line-height: 57px;
}

.toggle-switch input:checked ~ label:before {
    background: #25d025;
    background: radial-gradient(40% 35%, #5aef5a, #25d025 60%);
    box-shadow: inset 0 3px 5px 1px rgba(0,0,0,0.1), 0 1px 0 rgba(255,255,255,0.4), 0 0 10px 2px rgba(0, 210, 0, 0.5);
}

textarea {
  border-radius: 0px;
  border-color: #ccc;
}

form.add_form input[type="text"], form.add_form input[type="password"] {
  border: 1px solid #CCC;
}

form.summitlog_form input[type="text"], form.summitlog_form input[type="password"] {
  border: 1px solid #CCC;
}

.btn-gpx-file {
    position: relative;
    overflow: hidden;
}
.btn-gpx-file input[type=file] {
    position: absolute;
    top: 0;
    right: 0;
    min-width: 100%;
    min-height: 100%;
    font-size: 100px;
    text-align: right;
    filter: alpha(opacity=0);
    opacity: 0;
    outline: none;
    background: white;
    cursor: inherit;
    display: block;
}

ul#summitlog-gpx-files li div.a {
    width: 245px;
    height: 70px;
    text-align: center;
    background-color: #33c1f5;
    margin: 5px;
}

ul#summitlog-gpx-files .qq-upload-button {
    cursor: pointer;
    /*background: url('https://peakery-static.s3-us-west-1.amazonaws.com/img/choose-gpx-file.png') no-repeat center center;*/
    width: 245px;
    height: 70px;
    margin: 0;
    /*padding: 34px 115px;*/
    padding: 20px 0px;
}

ul#summitlog-gpx-files .qq-upload-button:after {
    content: 'Choose GPX file';
}

ul#summitlog-gpx-files .qq-upload-loading:after {
    content: '';
    display: inline-block;
    margin-left: -10px;
    font-size: 10px;
    width: 1em;
    height: 1em;
    margin-top: -0.5em;
    -webkit-animation: spinner 1500ms infinite linear;
    -moz-animation: spinner 1500ms infinite linear;
    -ms-animation: spinner 1500ms infinite linear;
    -o-animation: spinner 1500ms infinite linear;
    animation: spinner 1500ms infinite linear;
    border-radius: 0.5em;
    -webkit-box-shadow: rgba(255, 255, 255, 1) 1.5em 0 0 0, rgba(255, 255, 255, 1) 1.1em 1.1em 0 0, rgba(255, 255, 255, 1) 0 1.5em 0 0, rgba(255, 255, 255, 1) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.5) -1.5em 0 0 0, rgba(0, 0, 0, 0.5) -1.1em -1.1em 0 0, rgba(255, 255, 255, 1) 0 -1.5em 0 0, rgba(255, 255, 255, 1) 1.1em -1.1em 0 0;
    box-shadow: rgba(255, 255, 255, 1) 1.5em 0 0 0, rgba(255, 255, 255, 1) 1.1em 1.1em 0 0, rgba(255, 255, 255, 1) 0 1.5em 0 0, rgba(255, 255, 255, 1) -1.1em 1.1em 0 0, rgba(255, 255, 255, 1) -1.5em 0 0 0, rgba(255, 255, 255, 1) -1.1em -1.1em 0 0, rgba(255, 255, 255, 1) 0 -1.5em 0 0, rgba(255, 255, 255, 1) 1.1em -1.1em 0 0;
}

@media screen and (min-width: 768px) {
  ul#peakroute-files .qq-upload-button {
      font-size: 22px;
      line-height: 38px;
      padding-top: 33%;
      padding-right: 3%;
      background: none;
  }
  ul#peakroute-files .qq-upload-button:after {
      content: 'Add photo for step';
  }
  
  ul#user-files .qq-upload-button {
      font-size: 22px;
      line-height: 38px;
      padding-top: 33%;
      padding-right: 3%;
      background: none;
  }
  ul#user-files .qq-upload-button:after {
      content: 'Add your photo';
  }
  
}  

@media screen and (min-width: 1px) and (max-width: 767px) {
  ul#peakroute-files .qq-upload-button {
      font-size: 20px;
      line-height: 38px;
      padding-top: 33%;
      padding-right: 5%;
      background: none;
  } 
  ul#peakroute-files .qq-upload-button:after {
      content: 'Add photo';
  } 
  
  ul#user-files .qq-upload-button {
      font-size: 20px;
      line-height: 38px;
      padding-top: 33%;
      padding-right: 5%;
      background: none;
  } 
  ul#user-files .qq-upload-button:after {
      content: 'Add photo';
  } 
  
}  

ul#peakroute-files .qq-upload-loading:after {
    content: '';
    display: inline-block;
    margin-left: -10px;
    font-size: 10px;
    width: 1em;
    height: 1em;
    margin-top: -0.5em;
    -webkit-animation: spinner 1500ms infinite linear;
    -moz-animation: spinner 1500ms infinite linear;
    -ms-animation: spinner 1500ms infinite linear;
    -o-animation: spinner 1500ms infinite linear;
    animation: spinner 1500ms infinite linear;
    border-radius: 0.5em;
    -webkit-box-shadow: rgba(255, 255, 255, 1) 1.5em 0 0 0, rgba(255, 255, 255, 1) 1.1em 1.1em 0 0, rgba(255, 255, 255, 1) 0 1.5em 0 0, rgba(255, 255, 255, 1) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.5) -1.5em 0 0 0, rgba(0, 0, 0, 0.5) -1.1em -1.1em 0 0, rgba(255, 255, 255, 1) 0 -1.5em 0 0, rgba(255, 255, 255, 1) 1.1em -1.1em 0 0;
    box-shadow: rgba(255, 255, 255, 1) 1.5em 0 0 0, rgba(255, 255, 255, 1) 1.1em 1.1em 0 0, rgba(255, 255, 255, 1) 0 1.5em 0 0, rgba(255, 255, 255, 1) -1.1em 1.1em 0 0, rgba(255, 255, 255, 1) -1.5em 0 0 0, rgba(255, 255, 255, 1) -1.1em -1.1em 0 0, rgba(255, 255, 255, 1) 0 -1.5em 0 0, rgba(255, 255, 255, 1) 1.1em -1.1em 0 0;
}

ul#user-files .qq-upload-loading:after {
    content: '';
    display: inline-block;
    margin-left: -10px;
    font-size: 10px;
    width: 1em;
    height: 1em;
    margin-top: -0.5em;
    -webkit-animation: spinner 1500ms infinite linear;
    -moz-animation: spinner 1500ms infinite linear;
    -ms-animation: spinner 1500ms infinite linear;
    -o-animation: spinner 1500ms infinite linear;
    animation: spinner 1500ms infinite linear;
    border-radius: 0.5em;
    -webkit-box-shadow: rgba(255, 255, 255, 1) 1.5em 0 0 0, rgba(255, 255, 255, 1) 1.1em 1.1em 0 0, rgba(255, 255, 255, 1) 0 1.5em 0 0, rgba(255, 255, 255, 1) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.5) -1.5em 0 0 0, rgba(0, 0, 0, 0.5) -1.1em -1.1em 0 0, rgba(255, 255, 255, 1) 0 -1.5em 0 0, rgba(255, 255, 255, 1) 1.1em -1.1em 0 0;
    box-shadow: rgba(255, 255, 255, 1) 1.5em 0 0 0, rgba(255, 255, 255, 1) 1.1em 1.1em 0 0, rgba(255, 255, 255, 1) 0 1.5em 0 0, rgba(255, 255, 255, 1) -1.1em 1.1em 0 0, rgba(255, 255, 255, 1) -1.5em 0 0 0, rgba(255, 255, 255, 1) -1.1em -1.1em 0 0, rgba(255, 255, 255, 1) 0 -1.5em 0 0, rgba(255, 255, 255, 1) 1.1em -1.1em 0 0;
}

ul#summitlog-files li div.a {
    margin: 2px !important;
    margin-bottom: 20px !important;
}

#country_region_container select {
  background: transparent;
  width: 100%;
  padding: 5px;
  font-size: 16px;
  line-height: 1;
  border: 0;
  border-radius: 0;
  height: 70px;
  -webkit-appearance: none;
}

#country_region_container {
  width: 100%;
  height: 70px;
  overflow: hidden;
  background: url('https://peakery-static.s3-us-west-1.amazonaws.com/img/form-select-down-arrow.png') no-repeat right #fdfdfd;
  border: 1px solid #999;
  padding-left: 20px;
}

#route_up_container {
  width: 100%;
  height: 55px;
  overflow: hidden;
  border: 1px solid #CCC;
  padding-left: 5px;
}

#route_up_container:after {
  content: " ";
  color: #666;
  padding-left: 5px;
  vertical-align: middle;
  font-size: 20px;
  margin-bottom: 2px;
  position: relative;
  top: -47px;
  right: 20px;
  float: right;
}

#difficulty_rating_container select {
  background: transparent;
  width: 100%;
  padding: 5px;
  font-size: 16px;
  line-height: 18px;
  border: 0;
  border-radius: 0;
  height: 56px;
  -webkit-appearance: none;
}

#difficulty_rating_container {
  width: 50%;
  max-width: 421px;
  height: 56px;
  overflow: hidden;
  border: 1px solid #CCC;
  padding-left: 5px;
  background-color: #eee;
}

#difficulty_rating_container:after {
  font-family: FontAwesome;
  content: "\f078";
  color: #666;
  padding-left: 5px;
  vertical-align: middle;
  font-size: 20px;
  margin-bottom: 2px;
  position: relative;
  top: -47px;
  right: 20px;
  float: right;
}

.edit-peak-country-region select {
  cursor: pointer;
  background: transparent;
  width: 100%;
  max-width: 560px;
  padding: 5px;
  font-size: 18px;
  border: 0;
  border-radius: 0;
  height: 55px;
  -webkit-appearance: none;
}

.edit-peak-country-region:after {
    font-family: FontAwesome;
    content: "\f078";
    color: #666;
    padding-left: 5px;
    vertical-align: middle;
    font-size: 20px;
    margin-bottom: 2px;
    position: relative;
    top: -47px;
    right: 20px;
    float: right;
    height: 0px;
}

.edit-peak-country-region {
  height: 55px;
  max-width: 560px;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #999;
  padding-left: 10px;
}

#alpine_grade_container select {
  background: transparent;
  width: 100%;
  padding: 5px;
  font-size: 16px;
  line-height: 1;
  border: 0;
  border-radius: 0;
  height: 70px;
  -webkit-appearance: none;
}

#alpine_grade_container {
  width: 100%;
  height: 70px;
  overflow: hidden;
  background: url('https://peakery-static.s3-us-west-1.amazonaws.com/img/form-select-down-arrow.png') no-repeat right #fdfdfd;
  border: 1px solid #999;
  padding-left: 20px;
}

ul#peakroute-files .qq-upload-button {
  cursor: pointer;
  /*background: url('https://peakery-static.s3-us-west-1.amazonaws.com/img/add-step-photo.png') no-repeat center center;*/
  width: 100%;
    margin: 0;
}
ul#peakroute-files li div.a {
    width:300px;
    height: 240px;
    text-align: center;
    /*border: 5px solid #FFF;
    box-shadow: 0 2px 2px #808080;
    -moz-box-shadow: 0 2px 2px #808080;
    -webkit-box-shadow: 0 2px 2px #808080;*/
    background-color: #33c1f5;
    margin: 0px;
}
ul#peakroute-files li div.a:hover {
    -webkit-box-shadow: 0 3px 6px rgba(0,0,0,0.4);
  -moz-box-shadow: 0 3px 6px rgba(0,0,0,0.4);
  box-shadow: 0 3px 6px rgba(0,0,0,0.4);
}
ul#peakroute-files li:last-child {
    float: left;
    width: auto;
    border: none;
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    margin: 5px;
}
ul#peakroute-files li div.imageContainer {
    width: 300px;
    height: 240px;
    display: block;
    overflow: hidden;
}
ul#peakroute-files li div.textareaContainer {
    width: 300px !important;
    margin-top: -115px !important;
    display: block;
    margin: 0;
}
ul#peakroute-files li div.textareaContainer textarea {
    width: 300px !important;
    padding: 9px !important;
    resize: none;
    border: 1px solid #CCC;
}

ul#peakroute-files .qq-upload-button.loading {
    background: white url('/static/img/misc/loading.gif?63b46d66015a') center center no-repeat !important;
}

/*Member profile edit*/
ul#user-files .qq-upload-button {
  cursor: pointer;
  /*background: url('https://peakery-static.s3-us-west-1.amazonaws.com/img/add-step-photo.png') no-repeat center center;*/
  width: 100%;
    margin: 0;
}
ul#user-files li div.a {
    width:300px;
    height: 240px;
    text-align: center;
    /*border: 5px solid #FFF;
    box-shadow: 0 2px 2px #808080;
    -moz-box-shadow: 0 2px 2px #808080;
    -webkit-box-shadow: 0 2px 2px #808080;*/
    background-color: #33c1f5;
    margin: 0px;
}
ul#user-files li div.a:hover {
    -webkit-box-shadow: 0 3px 6px rgba(0,0,0,0.4);
  -moz-box-shadow: 0 3px 6px rgba(0,0,0,0.4);
  box-shadow: 0 3px 6px rgba(0,0,0,0.4);
}
ul#user-files li div.imageContainer {
    width: 300px;
    height: 240px;
    display: block;
    overflow: hidden;
}
ul#user-files li div.textareaContainer {
    width: 300px !important;
    margin-top: -115px !important;
    display: block;
    margin: 0;
}
ul#user-files li div.textareaContainer textarea {
    width: 300px !important;
    padding: 9px !important;
    resize: none;
    border: 1px solid #CCC;
}

ul#user-files .qq-upload-button.loading {
    background: white url('/static/img/misc/loading.gif?63b46d66015a') center center no-repeat !important;
}

.toggle-group label {
  margin-bottom: 0px !important;
  font-weight: 300 !important;
  font-size: 12px !important;
}

.toggle-handle {
  z-index: -1;
}

.toggle.btn {
  min-width: 130px !important;
  min-height: 34px !important;
  margin: 10px;
}

.toggle-off.btn {
  -webkit-box-shadow: none;
  box-shadow: none;
}

/**********************/
/*     STATS/INFO     */
/**********************/

h1.peak-title, h1.section-title {
  margin: 0;
  padding: 0;
  line-height: inherit;
}

h1.mobile-logo-tagline {
  padding: 0;
  line-height: inherit;
}

h2.section-header {
  margin: 0;
  padding: 0;
  line-height: inherit;
}

h2.stats-header {
  line-height: inherit;
}

.route-list-table > tbody > tr > td > div > a > h2 {
  font-size: inherit;
  display: table-cell;
}

.continent-link-div > a > h2 {
  font-size: inherit;
  margin: inherit;
  padding: inherit;
  line-height: inherit;
}

.regions-title {
  line-height: inherit;
  margin: 0;
  padding: 0;
  font-size: inherit;
  font-weight: inherit;
}

.region-info-section-header > div > h2, .region-info-section-header > h2, .region-info-section-header > div > span > h2 {
  line-height: inherit;
  margin: 0;
  padding: 0;
  font-size: inherit;
  font-weight: inherit;
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .summit-card-header {
    height: 70px;
    line-height: 50px;
  }
}

@media screen and (min-width: 768px) {
  .summit-card-header {
    height: 70px;
    line-height: 70px;
  }
}

.summit-attempt {
  color: #f24100;
}

.summit-card-mobile-stats {
  position: absolute;
  top: 15px;
  left: 10px;
  margin-right: 10px;
  font-size: 12px !important;
  line-height: 20px;
}

.news-card-mobile-stats {
    position: absolute;
    top: 11px;
    left: 10px;
    font-size: 12px !important;
}

@media screen and (min-width: 1px) and (max-width: 767px) {  
  .summit-card-peak-stats {
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 10px !important;
  }
  
  .summit-card-user-stats {
    position: absolute;
    top: 30px;
    left: 10px;
    font-size: 10px !important;
  }
}

@media screen and (min-width: 768px) {
  .summit-card-peak-stats {
    margin-left: 10px;
    font-weight: 300;
  }
  
  .summit-card-user-stats {
    float: right;
  }
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .stats-header {
    margin-top: 10px;
    margin-left: -6px;
    font-size: 14px;
    font-weight: 300;
    margin-bottom: 20px;
  }
  
  .stats-data {
    font-size: 14px;
    line-height: 24px;
    font-weight: 300;
    margin-bottom: 14px;
    margin-left: -6px;
  }
  
  .stats-data-link {
    font-size: 12px;
    line-height: 14px;
  }
  
  .stats-data-missing {
    font-size: 14px;
    font-weight: 300;
    margin-left: -6px;
    margin-bottom: 16px;
    color: #f24100;
    opacity: .5;
  }
  
  .stats-data-highlight {
    font-size: 14px;
    line-height: 24px;
    margin-left: -6px;
    font-weight: 300;
    color: #00B1F2;
    margin-bottom: 14px;
  }
  .summit-card-peak-title {
    position: absolute;
    top: 0px;
    left: 10px;
    font-size: 12px !important;
    color: #333;
  }
  .stats-data-bottom {
    font-size: 11px;
    position: absolute;
    bottom: 0px;
    margin-bottom: 10px;
  }
  .summit-card-user-avatar {
    width: 20px;
  }
  .summit-log-summary {
    font-size: 16px;
    line-height: 1.8;
    letter-spacing: 0;
    margin: 10px 0px 10px 0px;

  }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  .stats-header {
    margin-top: 8px;
    font-size: 16px;
    font-weight: 300;
    margin-bottom: 22px;
  }
  
  .stats-data {
    font-size: 16px;
    line-height: 26px;
    font-weight: 300;
    margin-bottom: 16px;
  }
  
  .stats-data-link {
    font-size: 12px;
    line-height: 14px;
  }
  
  .stats-data-missing {
    font-size: 16px;
    font-weight: 300;
    margin-bottom: 18px;
    color: #f24100;
    opacity: .5;
  }
  
  .stats-data-highlight {
    font-size: 16px;
    line-height: 26px;
    font-weight: 300;
    color: #00B1F2;
    margin-bottom: 16px;
  }
  .summit-card-peak-title {
    font-size: 14px !important;
    color: #333;
  }
  .stats-data-bottom {
    font-size: 14px;
    position: absolute;
    bottom: 0px;
    margin-bottom: 10px;
  }
  .summit-card-user-avatar {
    width: 25px;
  }
  .summit-log-summary {
    font-size: 18px;
    line-height: 1.8;
    letter-spacing: 0;
    margin: 20px 5px 20px 5px;

  }
}

@media screen and (min-width: 1024px) {
  .stats-header {
    margin-top: 10px;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
  }
  
  .stats-data {
    font-size: 18px;
    line-height: 30px;
    font-weight: 300;
    margin-bottom: 20px;
  }
  
  .stats-data-link {
    font-size: 14px;
    line-height: 16px;
  }
  
  .stats-data-missing {
    font-size: 18px;
    font-weight: 300;
    margin-bottom: 20px;
    color: #f24100;
    opacity: .5;
  }
  
  .stats-data-highlight {
    font-size: 18px;
    line-height: 30px;
    font-weight: 300;
    color: #00B1F2;
    margin-bottom: 20px;
  }
  .summit-card-peak-title {
    font-size: 18px !important;
    color: #333;
  }
  .stats-data-bottom {
    font-size: 14px;
    position: absolute;
    bottom: 0px;
    margin-bottom: 10px;
  }
  .summit-card-user-avatar {
    width: 40px;
    border-radius: 10px;
  }
  .summit-log-summary {
    font-size: 18px;
    line-height: 1.8;
    letter-spacing: 0;
    margin: 20px 5px 20px 5px;

  }
}

.stats-giant-red {
  font-size: 64px;
  font-weight: 300;
  color: #ff0000;
}

.stats-giant-blue {
  font-size: 64px;
  font-weight: 300;
  color: #00B1F2;
}

.caption {
  text-align: left;
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .profile-stats-table {
    margin-left: -6px;
    width: 100%;
  }
  .profile-header {
    margin-top: 10px;
    margin-left: -6px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 20px;
  }
  
  .profile-header-with-giant-stats {
    margin-top: 10px;
    margin-left: -6px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 26px;
  }
  
  .profile-header-with-text {
    margin-top: 10px;
    margin-left: -6px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 10px;
  }
  
  .profile-data {
    font-size: 24px;
    margin-left: -6px;
    font-weight: 300;
    margin-bottom: 20px;
  }
  
  .profile-data-missing {
    font-size: 14px;
    margin-left: -6px;
    font-weight: 300;
    margin-bottom: 20px;
    color: #f24100;
    opacity: .5;
  }
  
  .profile-data-highlight {
    font-size: 16px;
    margin-left: -6px;
    line-height: 24px;
    font-weight: 300;
    color: #00B1F2;
    margin-bottom: 0px;
  }
  
  .profile-data-black {
    font-size: 12px;
    color: #999;
    margin-left: -6px;
    line-height: 24px;
    font-weight: 300;
    margin-bottom: 20px;
  }
  
  .profile-data-basecamp {
    font-size: 16px;
  }
  
  .profile-data-bottom {
    font-size: 11px;
    margin-left: -6px;
    position: absolute;
    bottom: 0px;
    margin-bottom: 10px;
    color: #999999;
  }
  
  .profile-giant-red {
    font-size: 38px;
    margin-left: -6px;
    font-weight: 300;
    color: #ff0000;
  }
  
  .profile-giant-blue {
    font-size: 38px;
    margin-left: -6px;
    font-weight: 300;
    color: #00B1F2;
  }
  
  .profile-giant-green {
    font-size: 38px;
    margin-left: -6px;
    font-weight: 300;
    color: #00b330;
    text-shadow: 0px 0px 40px rgba(204, 255, 51, 1);
  }
  
  .profile-subdata-highlight {
    font-size: 11px;
    font-weight: 300;
    margin-left: -6px;
    color: #00B1F2;
  }
  
  .profile-subdata-grey {
    font-size: 11px;
    margin-bottom: 5px;
    margin-left: -6px;
    color: #999999;
    font-weight: 300;
  }
  
  .profile-subdata-black {
    font-size: 11px;
    margin-bottom: 5px;
    margin-left: -6px;
    color: #000;
    font-weight: 300;
  }
  
  .profile-about-bagger {
    font-size: 14px;
    line-height: 22px;
    margin-left: -6px;
  }
  
  .profile-edit-link {
    font-size: 12px;
    font-weight: 300;
    cursor: pointer;
    line-height: 26px;
  }
  
  .profile-modal-link {
    font-size: 12px;
    font-weight: 300;
    cursor: pointer;
    line-height: 26px;
  }
  
  .profile-awards-stats {
    margin-bottom: 10px;
    margin-left: -6px;
    clear: left;
  }
  
  .profile-social-icons {
    font-size: 20px;
    margin-right: 50px;
    margin-bottom: 5px;
    color: #00B1F2;
  }
  
  .profile-social-icon-website {
    font-size: 18px;
  }
  
  .profile-awards-icon-kom {
    height: 16px;
    margin-right: 12px;
  }
  
  .profile-awards-icon-first {
    height: 20px;
    margin-right: 16px;
    margin-left: 6px;
  }
  
  .profile-awards-icon-steward {
    height: 18px;
    margin-right: 15px;
    margin-left: 4px;
  }
  
  .profile-top-stats-div {
    margin-bottom: 26px;
  }
  
  .top-companions-div {
    margin-bottom: 26px;
  }
  
  .profile-top-stats {
    font-size: 14px;
    font-weight: 300;
  }
  
  .profile-completion-radius-header {
    margin-right: -8px;
  }
  
  .profile-about-completion-radius {
    font-size: 10px;
    line-height: 30px;
  }
  
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  .profile-stats-table {
    width: 100%;
  }
  .profile-header {
    margin-top: 10px;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
  }
  
  .profile-header-with-giant-stats {
    margin-top: 10px;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 40px;
  }
  
  .profile-header-with-text {
    margin-top: 10px;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
  }
  
  .profile-data {
    font-size: 24px;
    font-weight: 300;
    margin-bottom: 20px;
  }
  
  .profile-data-missing {
    font-size: 20px;
    font-weight: 300;
    margin-bottom: 20px;
    color: #f24100;
    opacity: .5;
  }
  
  .profile-data-highlight {
    font-size: 20px;
    line-height: 24px;
    font-weight: 300;
    color: #00B1F2;
    margin-bottom: 10px;
  }
  
  .profile-data-black {
    font-size: 16px;
    color: #999;
    line-height: 24px;
    font-weight: 300;
    margin-bottom: 20px;
  }
  
  .profile-data-bottom {
    font-size: 14px;
    position: absolute;
    bottom: 0px;
    margin-bottom: 10px;
    color: #999999;
  }
  
  .profile-giant-red {
    font-size: 64px;
    font-weight: 300;
    color: #ff0000;
  }
  
  .profile-giant-blue {
    font-size: 64px;
    font-weight: 300;
    color: #00B1F2;
  }
  
  .profile-followers-stats {
    font-size: 52px;
  }
  
  .profile-giant-green {
    font-size: 75px;
    font-weight: 300;
    color: #00b330;
    text-shadow: 0px 0px 40px rgba(204, 255, 51, 1);
  }
  
  .profile-subdata-highlight {
    font-size: 16px;
    color: #00B1F2;
    font-weight: 300;
  }
  
  .profile-subdata-grey {
    font-size: 14px;
    color: #999999;
    font-weight: 300;
  }
  
  .profile-subdata-black {
    font-size: 16px;
    color: #000;
    font-weight: 300;
  }
  
  .profile-about-bagger {
    font-size: 16px;
    line-height: 26px;
  }
  
  .profile-edit-link {
    font-size: 16px;
    font-weight: 300;
    cursor: pointer;
  }
  
  .profile-modal-link {
    font-size: 16px;
    font-weight: 300;
    cursor: pointer;
  }
  
  .profile-awards-stats {
    margin-bottom: 10px;
    clear: left;
  }
  
  .profile-social-icons {
    font-size: 24px;
    margin-right: 50px;
    margin-bottom: 5px;
    color: #00B1F2;
  }
  
  .profile-social-icon-website {
    font-size: 22px;
  }
  
  .profile-awards-icon-kom {
    height: 16px;
    margin-right: 10px;
  }
  
  .profile-awards-icon-first {
    height: 20px;
    margin-right: 14px;
    margin-left: 6px;
  }
  
  .profile-awards-icon-steward {
    height: 18px;
    margin-right: 12px;
    margin-left: 4px;
  }
  
  .profile-top-stats-div {
    margin-bottom: 20px;
  }
  
  .top-companions-div {
    margin-bottom: 20px;
  }
  
  .profile-top-stats {
    font-size: 14px;
  }
  
}

@media screen and (min-width: 1024px) {
  .profile-stats-table {
    width: 100%;
  }
  .profile-header {
    margin-top: 10px;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
  }
  
  .profile-header-with-giant-stats {
    margin-top: 10px;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 50px;
  }
  
  .profile-header-with-text {
    margin-top: 10px;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
  }
  
  .profile-data {
    font-size: 24px;
    font-weight: 300;
    margin-bottom: 20px;
  }
  
  .profile-data-missing {
    font-size: 20px;
    font-weight: 300;
    margin-bottom: 20px;
    color: #f24100;
    opacity: .5;
  }
  
  .profile-data-highlight {
    font-size: 20px;
    line-height: 24px;
    font-weight: 300;
    color: #00B1F2;
    margin-bottom: 16px;
  }
  
  .profile-data-black {
    font-size: 18px;
    color: #999;
    line-height: 24px;
    font-weight: 300;
    margin-bottom: 20px;
  }
  
  .profile-data-bottom {
    font-size: 14px;
    position: absolute;
    bottom: 0px;
    margin-bottom: 10px;
    color: #999999;
  }
  
  .profile-giant-red {
    font-size: 64px;
    font-weight: 300;
    color: #ff0000;
  }
  
  .profile-giant-blue {
    font-size: 64px;
    font-weight: 300;
    color: #00B1F2;
  }
  
  .profile-followers-stats {
    font-size: 52px;
  }
  
  .profile-giant-green {
    font-size: 75px;
    font-weight: 300;
    color: #00b330;
    text-shadow: 0px 0px 40px rgba(204, 255, 51, 1);
  }
  
  .profile-subdata-highlight {
    font-size: 16px;
    color: #00B1F2;
    font-weight: 300;
  }
  
  .profile-subdata-grey {
    font-size: 15px;
    color: #999999;
    font-weight: 300;
  }
  
  .profile-subdata-black {
    font-size: 16px;
    color: #000;
    font-weight: 300;
  }
  
  .profile-about-bagger {
    font-size: 16px;
    line-height: 26px;
  }
  
  .profile-edit-link {
    font-size: 16px;
    font-weight: 300;
    cursor: pointer;
  }
  
  .profile-modal-link {
    font-size: 16px;
    font-weight: 300;
    cursor: pointer;
  }
  
  .profile-awards-stats {
    height: 35px;
    margin-bottom: 26px;
    clear: left;
  }
  
  .profile-social-icons {
    font-size: 24px;
    margin-right: 50px;
    margin-bottom: 5px;
    color: #00B1F2;
  }
  
  .profile-social-icon-website {
    font-size: 22px;
  }
  
  .profile-awards-icon-kom {
    height: 20px;
    margin-right: 10px;
  }
  
  .profile-awards-icon-first {
    height: 25px; 
    margin-right: 14px;
    margin-left: 8px;
  }
  
  .profile-awards-icon-steward {
    height: 25px;
    margin-right: 12px;
    margin-left: 4px;
  }
  
  .profile-top-stats-div {
    margin-bottom: 20px;
  }
  
  .top-companions-div {
    margin-bottom: 20px;
  }
  
  .profile-top-stats {
    font-size: 16px;
  }
  
}

@media screen and (min-width: 1280px) {
  .profile-award-names {
    font-size: 16px;
  }
  .profile-award-stats {
    font-size: 15px;
  }
  .profile-awards-stats {
    margin-bottom: 26px;
  }
  .profile-awards-stats-data {
    font-size: 18px;
    height: 55px;
    vertical-align: top;
  }
}  

@media screen and (min-width: 1226px) and (max-width: 1279px) {  
  .profile-award-stats {
    font-size: 14px;
  }
  .profile-awards-stats {
    margin-bottom: 20px;
  }
  .profile-awards-stats-data {
    font-size: 18px;
    height: 60px;
    vertical-align: top;
  }
}  

@media screen and (min-width: 1173px) and (max-width: 1225px) {  
  .profile-award-stats {
    font-size: 14px;
  }
  .profile-awards-stats {
    margin-bottom: 20px;
  }
  .profile-awards-stats-data {
    font-size: 18px;
    height: 55px;
    vertical-align: top;
  }
}  

@media screen and (min-width: 1066px) and (max-width: 1172px) {  
  .profile-award-names {
    font-size: 14px;
  }
  .profile-award-stats {
    font-size: 14px;
  }
  .profile-awards-stats-data {
    font-size: 14px;
    height: 50px;
    vertical-align: top;
  }
  .profile-awards-stats {
    margin-bottom: 16px;
  }
}  

@media screen and (min-width: 1024px) and (max-width: 1065px) {  
  .profile-award-names {
    font-size: 13px;
  }
  .profile-award-stats {
    font-size: 13px;
  }
  .profile-awards-stats-data {
    font-size: 13px;
    height: 40px;
    vertical-align: top;
  }
  .profile-awards-stats {
    margin-bottom: 12px;
  }
}  

@media screen and (min-width: 906px) and (max-width: 1023px) {  
  .profile-award-names {
    font-size: 15px;
  }
  
  .profile-award-stats {
    font-size: 12px;
  }
  .profile-awards-stats-data {
    font-size: 14px;
    height: 60px;
    vertical-align: top;
  }
}

@media screen and (min-width: 800px) and (max-width: 905px) {  
  .profile-award-names {
    font-size: 14px;
  }
  
  .profile-award-stats {
    font-size: 12px;
  }
  .profile-awards-stats-data {
    font-size: 14px;
    height: 50px;
    vertical-align: top;
  }
}

@media screen and (min-width: 768px) and (max-width: 799px) {  
  .profile-award-names {
    font-size: 13px;
  }
  
  .profile-award-stats {
    font-size: 12px;
  }
  .profile-awards-stats-data {
    font-size: 14px;
    height: 40px;
    vertical-align: top;
  }
}

@media screen and (min-width: 1px) and (max-width: 767px) {  
  .profile-award-names {
    font-size: 11px;
    font-weight: 300;
  }
  
  .profile-award-stats {
    font-size: 11px;
  }
  .profile-awards-stats-data {
    font-size: 14px;
    height: 30px;
    vertical-align: top;
  }
}

.striped-div-table:nth-of-type(odd) {
   background-color: #f2f2f2;
}

.striped-div-table:nth-of-type(even) {
   background-color: #fff;
}

/*****************/
/*    BUTTONS    */
/*****************/

.round-button {
	width:10%;
}

.round-button-circle {
	width: 100%;
	height:0;
	padding-bottom: 100%;
    border-radius: 50%;
    overflow:hidden;
    
    background: #4679BD; 
    box-shadow: 0 0 3px gray;
}

.round-button-circle:hover {
	background:#30588e;
}

.round-button span {
    display:block;
	float:left;
	width:100%;
	padding-top:50%;
    padding-bottom:50%;
	line-height:1em;
	margin-top:-0.5em;
    
	text-align:center;
	color:#e2eaf3;
    font-size:1.5em;
    text-decoration:none;
}

.btn {
  border-radius: 12px;
  padding: 15px 15px;
  letter-spacing: 0.03125em;
}

.btn:hover {
  background-image: none;
}

.btn-primary {
  background-color: #f24100;
  border-color: #f24100;
}

.btn-primary:hover {
  background-color: #ff4400;
  border-color: #ff4400;
}

.btn-secondary {
  background-color: #00b1f2;
  border-color: #00b1f2;
}

.btn-secondary:hover {
  background-color: #33c9ff;
  border-color: #33c9ff;
  color: #fff;
}

.btn-info {
  background-color: #3b5998;
  border-color: #3b5998;
}

.btn-info:hover {
  background-color: #2d4373;
  border-color: #2d4373;
}

.btn-info:active {
  background-color: #2d4373;
  border-color: #2d4373;
}

/*********************/
/*    PEAK BADGES    */
/*********************/

@media screen and (min-width: 1px) and (max-width: 599px) {

  .grow-badge {
    height: 108px;
    width: 160px;
    margin: auto;
    border-top-left-radius: 45px;
    border-top-right-radius: 45px;
    box-shadow: none;
  }
  
  .grow-badge-icons {
    display: block;
    height: 40px;
    position: relative;
    top: 70px;
  }
  
  .grow-badge-icons .item_icon_text {
    float: left;
    margin-left: 8px;
    font-weight: 300;
    color: #fff;
    background-color: #f24100;
    border: solid 2px #fff;
    padding: 2px;
    border-radius: 100%;
    font-size: 10px;
    text-align: center;
    width: 27px;
  }
  
  .grow-badge-icons .item_icon_imgspan {
    float: left;
    margin-left: 8px;
    display: inline-block;
  }
  
  .grow-badge-icons .item_icon_kom {
    height: 26px;
    margin: 0 auto;
  }
  
  .grow-badge-icons .item_icon_steward {
    height: 26px;
    margin: 0 auto;
  }
  
  .grow-badge-icons .item_icon_first {
    height: 26px;
    margin: 0 auto;
  }
  
  .grow-badge-info {
    width: 160px;
    border-bottom-right-radius: 34px;
    border-bottom-left-radius: 34px;
    top: 65px;
    background-image: url(https://s3-us-west-1.amazonaws.com/peakery-static/img/top-nav-bg-bright.png);
    left: 0px;
    display: block;
    font-size: 12px;
    padding-bottom: 2px;
    padding-top: 2px;
    position: relative;
    text-align: center;
    text-decoration: none;
    box-shadow: 0 10px 16px -6px #666;
  }
  
  .grow-badge-info .item_name {
    font-size: 12px;
    font-weight: 400;
    padding-left: 5px;
    padding-right: 5px;
    display: block;
    color: #fff;
    margin-bottom: -2px;
  }
  
  .grow-badge-info .item_info {
    font-size: 10px;
    color: #999;
    letter-spacing: 0.03125em;
    font-weight: 300;
  }
  
  
  
  
  
  
  
  .splash-grow-badge {
    height: 108px;
    width: 160px;
    margin: auto;
    border-top-left-radius: 45px;
    border-top-right-radius: 45px;
    box-shadow: none;
  }
  
  .splash-grow-badge-icons {
    display: block;
    height: 40px;
    position: relative;
    top: 70px;
  }
  
  .splash-grow-badge-icons .item_icon_text {
    float: left;
    margin-left: 8px;
    font-weight: 300;
    color: #fff;
    background-color: #f24100;
    border: solid 2px #fff;
    padding: 2px;
    border-radius: 100%;
    font-size: 10px;
    text-align: center;
    width: 27px;
  }
  
  .splash-grow-badge-icons .item_icon_imgspan {
    float: left;
    margin-left: 8px;
    display: inline-block;
  }
  
  .splash-grow-badge-icons .item_icon_kom {
    height: 26px;
    margin: 0 auto;
  }
  
  .splash-grow-badge-icons .item_icon_steward {
    height: 26px;
    margin: 0 auto;
  }
  
  .splash-grow-badge-icons .item_icon_first {
    height: 26px;
    margin: 0 auto;
  }
  
  .splash-grow-badge-info {
    width: 160px;
    border-bottom-right-radius: 34px;
    border-bottom-left-radius: 34px;
    top: 65px;
    background-image: url(https://s3-us-west-1.amazonaws.com/peakery-static/img/top-nav-bg-bright.png);
    left: 0px;
    display: block;
    font-size: 12px;
    padding-bottom: 2px;
    padding-top: 2px;
    position: relative;
    text-align: center;
    text-decoration: none;
    box-shadow: 0 10px 16px -6px #666;
  }
  
  .splash-grow-badge-info .item_name {
    font-size: 12px;
    font-weight: 400;
    padding-left: 5px;
    padding-right: 5px;
    display: block;
    color: #fff;
    margin-bottom: -2px;
  }
  
  .splash-grow-badge-info .item_info {
    font-size: 10px;
    color: #999;
    letter-spacing: 0.03125em;
    font-weight: 300;
  }

}

@media screen and (min-width: 600px) {

  .grow-badge {
    height: 155px;
    width: 230px;
    margin: auto;
    border-top-left-radius: 34px;
    border-top-right-radius: 34px;
    box-shadow: none;
  }
  
  .grow-badge-icons {
    display: block;
    height: 40px;
    position: relative;
    top: 115px;
  }
  
  .grow-badge-icons .item_icon_text {
    float: left;
    margin-left: 10px;
    font-weight: 300;
    color: #fff;
    background-color: #f24100;
    border: solid 2px #fff;
    padding: 1px;
    border-radius: 100%;
    font-size: 12px;
    text-align: center;
    width: 30px;
  }
  
  .grow-badge-icons .item_icon_imgspan {
    float: left;
    margin-left: 10px;
    display: inline-block;
  }
  
  .grow-badge-icons .item_icon_kom {
    height: 26px;
    margin: 0 auto;
  }
  
  .grow-badge-icons .item_icon_steward {
    height: 26px;
    margin: 0 auto;
  }
  
  .grow-badge-icons .item_icon_first {
    height: 26px;
    margin: 0 auto;
  }
  
  .grow-badge-info {
    width: 230px;
    border-bottom-right-radius: 34px;
    border-bottom-left-radius: 34px;
    top: 112px;
    background-image: url(https://s3-us-west-1.amazonaws.com/peakery-static/img/top-nav-bg-bright.png);
    left: 0px;
    display: block;
    font-size: 12px;
    padding-bottom: 2px;
    padding-top: 2px;
    position: relative;
    text-align: center;
    text-decoration: none;
    box-shadow: 0 10px 16px -6px #666;
  }
  
  .grow-badge-info .item_name {
    font-size: 12px;
    font-weight: 400;
    padding-left: 5px;
    padding-right: 5px;
    display: block;
    color: #fff;
    margin-bottom: -2px;
  }
  
  .grow-badge-info .item_info {
    font-size: 10px;
    color: #999;
    letter-spacing: 0.03125em;
    font-weight: 300;
  }
  
  
  
  
  
  
  .splash-grow-badge {
    height: 155px;
    width: 230px;
    margin: auto;
    border-top-left-radius: 34px;
    border-top-right-radius: 34px;
    box-shadow: none;
  }
  
  .splash-grow-badge-icons {
    display: block;
    height: 40px;
    position: relative;
    top: 115px;
  }
  
  .splash-grow-badge-icons .item_icon_text {
    float: left;
    margin-left: 10px;
    font-weight: 300;
    color: #fff;
    background-color: #f24100;
    border: solid 2px #fff;
    padding: 1px;
    border-radius: 100%;
    font-size: 12px;
    text-align: center;
    width: 30px;
  }
  
  .splash-grow-badge-icons .item_icon_imgspan {
    float: left;
    margin-left: 10px;
    display: inline-block;
  }
  
  .splash-grow-badge-icons .item_icon_kom {
    height: 26px;
    margin: 0 auto;
  }
  
  .splash-grow-badge-icons .item_icon_steward {
    height: 26px;
    margin: 0 auto;
  }
  
  .splash-grow-badge-icons .item_icon_first {
    height: 26px;
    margin: 0 auto;
  }
  
  .splash-grow-badge-info {
    width: 230px;
    border-bottom-right-radius: 34px;
    border-bottom-left-radius: 34px;
    top: 112px;
    background-image: url(https://s3-us-west-1.amazonaws.com/peakery-static/img/top-nav-bg-bright.png);
    left: 0px;
    display: block;
    font-size: 12px;
    padding-bottom: 2px;
    padding-top: 2px;
    position: relative;
    text-align: center;
    text-decoration: none;
    box-shadow: 0 10px 16px -6px #666;
  }
  
  .splash-grow-badge-info .item_name {
    font-size: 12px;
    font-weight: 400;
    padding-left: 5px;
    padding-right: 5px;
    display: block;
    color: #fff;
    margin-bottom: -2px;
  }
  
  .splash-grow-badge-info .item_info {
    font-size: 10px;
    color: #999;
    letter-spacing: 0.03125em;
    font-weight: 300;
  }

}

.summit_badges li {
  margin: 30px;
}

.summit_badges {
    overflow: inherit;
}

.summit_badges li {
  width: 220px; 
}

.summit_badges li > a.withoutNumber {
  -webkit-border-radius: 75px 75px 34px 34px;
}

.summit_badges li > a.withoutNumber:hover {
  -webkit-filter: brightness(1.1) saturate(1.5);
  -webkit-transition: opacity .25s ease-in-out;
}

.summit_badges li .thumb {
  height: 150px;
  width: 220px;
  margin-left: 0px;
}

.summit_badges li .peak {
  width: 220px;
  border-bottom-right-radius: 34px;
  border-bottom-left-radius: 34px;
  bottom: 0px;
  background-image: url("https://s3-us-west-1.amazonaws.com/peakery-static/img/top-nav-bg-bright.png");
  left: 0px;
}

.summit_badges li .peak .item_name:hover {
  color: #fff;
}

.summit_badges li >a:hover .peak {
    background-color: #00b1f2;
    -webkit-filter: brightness(1.0) saturate(1.0);
}

.summit_badges li >a:hover .item_info {
    color: #fff;
}

.summit_badges li .item_name {
  margin-bottom: 2px; 
  font-size: 12px;
  font-weight: 500;
  padding-left: 5px;
  padding-right: 5px;
}

.summit_badges li > a {
  padding-top: 0px; 
  -webkit-box-shadow: 3px 3px 10px #666;
}

.summit_badges li .item_info {
  font-size: 10px; 
  color: #999;
  letter-spacing: 0.03125em;
  font-weight: 300;
}

.summit_badges li .item_icons {
  margin-top: -30px;
  display: block;
  height: 40px;
}

.summit_badges li .item_icon_text {
  margin-left: 25px;
  font-weight: 700;
  color: #fff;
  background-color: #00b1f2;
  border: solid 2px #fff;
  padding: 3px;
  border-radius: 10px;
  font-size: 12px;
}

.summit_badges li .item_icon_imgspan {
  margin-left: 18px;
  display: inline-block;
}

.summit_badges li .item_icon_kom {
  height: 21px;
  width: 28px;
  margin-bottom: -5px;
}

.summit_badges li .item_icon_steward {
  height: 21px;
  width: 21px;
  margin-bottom: -5px;
}

.summit_badges li .item_icon_first {
  height: 21px;
  width: 21px;
  margin-bottom: -5px;
}

/*********************/
/*   PROGRESS BARS   */
/*********************/

@media screen and (min-width: 1200px) {
  .progress {
    height: 40px;
    border-radius: 0px;
    position: relative;
    background-color: #999999;
  }
  
  .progress span {
      position: absolute;
      display: block;
      width: 100%;
      color: #fff;
  }
  
  .progress-bar-blue {
    background-color: #00b1f2;
    color: #000000;
    font-size: 16px;
    line-height: 40px;
  }
  
  .progress-bar-green {
    background-color: #16b230;
    color: #ffffff;
    font-size: 16px;
    line-height: 40px;
  }
}

@media screen and (min-width: 1024px) and (max-width: 1199px) {
  .progress {
    height: 30px;
    border-radius: 0px;
    position: relative;
    background-color: #999999;
  }
  
  .progress span {
      position: absolute;
      display: block;
      width: 100%;
      color: #fff;
  }
  
  .progress-bar-blue {
    background-color: #00b1f2;
    color: #000000;
    font-size: 13px;
    line-height: 30px;
  }
  
  .progress-bar-green {
    background-color: #16b230;
    color: #ffffff;
    font-size: 13px;
    line-height: 30px;
  }
}

@media screen and (min-width: 992px) and (max-width: 1023px) {
  .progress {
    height: 25px;
    border-radius: 0px;
    position: relative;
    background-color: #999999;
  }
  
  .progress span {
      position: absolute;
      display: block;
      width: 100%;
      color: #fff;
  }
  
  .progress-bar-blue {
    background-color: #00b1f2;
    color: #000000;
    font-size: 12px;
    line-height: 25px;
  }
  
  .progress-bar-green {
    background-color: #16b230;
    color: #ffffff;
    font-size: 12px;
    line-height: 25px;
  }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
  .progress {
    height: 25px;
    border-radius: 0px;
    position: relative;
    background-color: #999999;
  }
  
  .progress span {
      position: absolute;
      display: block;
      width: 100%;
      color: #fff;
  }
  
  .progress-bar-blue {
    background-color: #00b1f2;
    color: #000000;
    font-size: 12px;
    line-height: 25px;
  }
  
  .progress-bar-green {
    background-color: #16b230;
    color: #ffffff;
    font-size: 12px;
    line-height: 25px;
  }
}

@media screen and (min-width: 480px) and (max-width: 767px) {
  .progress {
    height: 16px;
    border-radius: 0px;
    position: relative;
    background-color: #999999;
  }
  
  .progress span {
      position: absolute;
      display: block;
      width: 100%;
      color: #fff;
  }
  
  .progress-bar-blue {
    background-color: #00b1f2;
    color: #000000;
    font-size: 9px;
    line-height: 16px;
  }
  
  .progress-bar-green {
    background-color: #16b230;
    color: #ffffff;
    font-size: 9px;
    line-height: 16px;
  }
}

@media screen and (min-width: 1px) and (max-width: 479px) {
  .progress {
    height: 16px;
    border-radius: 0px;
    position: relative;
    background-color: #999999;
  }
  
  .progress span {
      position: absolute;
      display: block;
      width: 100%;
      color: #fff;
  }
  
  .progress-bar-blue {
    background-color: #00b1f2;
    color: #000000;
    font-size: 9px;
    line-height: 16px;
  }
  
  .progress-bar-green {
    background-color: #16b230;
    color: #ffffff;
    font-size: 9px;
    line-height: 16px;
  }
}

/******************/
/*    HOMEPAGE    */
/******************/

.chevron-link {
  color: #ffffff;
}

.chevron-link:hover {
  color: #cccccc;
}

@media screen and (min-width: 1024px) {
  .carousel-caption {
      width: 500px;
      height:100px;
      background: rgba(242, 65, 0, 0.8);
      -webkit-backdrop-filter: brightness(1.5) blur(5px); 
      backdrop-filter: brightness(1.5) blur(5px);
      position:absolute;
      top:50%;
      left:50%;
      margin-top:-50px;
      margin-left: -250px;
      line-height: 60px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-bottom: 0px;
      padding-top: 0px;
  }
  
  .carousel-chevron {
    font-size: 70px;
    text-align: center;
    color: #fff;
    opacity: .8;
    position: absolute;
    bottom: 0%;
    left: 0%;
    width: 100%;
    height: 15%;
    line-height: 100%;
    background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.8) 100%);
  }
  
  .carousel-info {
    font-size: 19px;
    text-align: right;
    color: #fff;
    position: absolute;
    bottom: 0px;
    right: 10px;
    width: 100%;
    line-height: 140%;
  }
  
  .carousel-sub-info {
    font-size: 13px;
    right: 1px;
    bottom: 12px;
    position: absolute;
  }
  
  #main-caption a {
    color: #ffffff; font-weight: 300; font-style: normal; font-size: 250%; text-decoration: none; text-shadow: none; letter-spacing: 2px;
  }
  
  #main-caption a:hover {
    color: #e0e0e0; font-weight: 300; font-style: normal; font-size: 250%; text-decoration: none; text-shadow: none; letter-spacing: 2px;
  }
  
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  .carousel-caption {
      width: 350px;
      height:80px;
      background: rgba(242, 65, 0, 0.8);
      -webkit-backdrop-filter: brightness(1.5) blur(5px); 
      backdrop-filter: brightness(1.5) blur(5px);
      position:absolute;
      top:50%;
      left:50%;
      margin-top:-50px;
      margin-left: -175px;
      line-height: 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-bottom: 0px;
      padding-top: 0px;
  }
  
  .carousel-chevron {
    font-size: 50px;
    text-align: center;
    color: #fff;
    opacity: .8;
    position: absolute;
    bottom: 0%;
    left: 0%;
    width: 100%;
    height: 15%;
    line-height: 100%;
    background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.8) 100%);
  }
  
  .carousel-info {
    font-size: 14px;
    text-align: right;
    color: #fff;
    position: absolute;
    bottom: 3px;
    right: 10px;
    width: 100%;
    line-height: 120%;
  }
  
  .carousel-sub-info {
    font-size: 10px;
    right: 1px;
    position: absolute;
  }
  
  #main-caption a {
    color: #ffffff; font-weight: 300; font-style: normal; font-size: 170%; text-decoration: none; text-shadow: none; letter-spacing: 2px;
  }
  
  #main-caption a:hover {
    color: #e0e0e0; font-weight: 300; font-style: normal; font-size: 170%; text-decoration: none; text-shadow: none; letter-spacing: 2px;
  }
  
}

@media screen and (min-width: 480px) and (max-width: 767px) {
  .carousel-caption {
      width: 300px;
      height:60px;
      background: rgba(242, 65, 0, 0.8);
      -webkit-backdrop-filter: brightness(1.5) blur(5px); 
      backdrop-filter: brightness(1.5) blur(5px);
      position:absolute;
      top:50%;
      left:50%;
      margin-top:-40px;
      margin-left: -150px;
      line-height: 20px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-bottom: 0px;
      padding-top: 0px;
  }
  
  .carousel-chevron {
    font-size: 30px;
    text-align: center;
    color: #fff;
    opacity: .8;
    position: absolute;
    bottom: 0%;
    left: 0%;
    width: 100%;
    height: 15%;
    line-height: 100%;
    background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.8) 100%);
  }
  
  .carousel-info {
    font-size: 12px;
    text-align: right;
    color: #fff;
    position: absolute;
    bottom: 5px;
    right: 12px;
    width: 100%;
    line-height: 100%;
  }
  
  .carousel-sub-info {
    font-size: 10px;
    position: absolute;
    right: 1px;
    display: none;
  }
  
  #main-caption a {
    color: #ffffff; font-weight: 300; font-style: normal; font-size: 150%; text-decoration: none; text-shadow: none;
  }
  
  #main-caption a:hover {
    color: #e0e0e0; font-weight: 300; font-style: normal; font-size: 150%; text-decoration: none; text-shadow: none;
  }
  
}

.main-collage-table > tbody > tr > td {
  border-top: none;
  padding: 3px;
}

@media screen and (min-width: 1px) and (max-width: 479px) {
  .carousel-caption {
      width: 250px;
      height: 60px;
      background: rgba(242, 65, 0, 0.8);
      -webkit-backdrop-filter: brightness(1.5) blur(5px);
      backdrop-filter: brightness(1.5) blur(5px);
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -30px;
      margin-left: -125px;
      line-height: 0px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-bottom: 0px;
      padding-top: 0px;
  }
  
  .carousel-chevron {
    font-size: 22px;
    text-align: center;
    color: #fff;
    opacity: .8;
    position: absolute;
    bottom: 0%;
    left: 0%;
    width: 100%;
    height: 15%;
    line-height: 100%;
    background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.8) 100%);
  }
  
  .carousel-info {
    font-size: 12px;
    text-align: right;
    color: #fff;
    position: absolute;
    bottom: 2px;
    right: 10px;
    width: 100%;
    line-height: 100%;
  }
  
  .carousel-sub-info {
    font-size: 10px;
    position: absolute;
    right: 1px;
    display: none;
  }
  
  #main-caption a {
    color: #ffffff; font-weight: 500; font-style: normal; font-size: 20px; text-decoration: none; text-shadow: none;
  }
  
  #main-caption a:hover {
    color: #e0e0e0; font-weight: 500; font-style: normal; font-size: 20px; text-decoration: none; text-shadow: none;
  }
  
}

.main-collage-table > tbody > tr > td {
  border-top: none;
  padding: 3px;
}

/* Media queries for guest home fonts, etc. */

#climb-peaks-div:hover {
  background-color: #3BBAF8;
}

#follow-along-div:hover {
  background-color: #3BBAF8;
}

#log-summits-div:hover {
    background-color: #379EC5;
  }

@media screen and (min-width: 1280px) {
  /***  1/2: 640  1/3: 427  1/4: 320  Midpoint: 1280  ***/
  .home-text {
    font-size: 23px;
    margin-left: 95px;
    margin-right: 95px;
    line-height: 2em;
    text-align: left;
  }
  
  .home-hero-text {
    font-size: 40px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width:60px;
    height:60px;
    border-radius:30px;
    font-size:30px;
    color:#fff;
    line-height:60px;
    text-align:center;
    background:#1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 80px;
    height: 60px;
    line-height: 60px;
    font-size: 40px;
  }
  
  #home-chevron-icon {
    font-size: 80px;
  }
  
  #home-chevron-div {
    top: -100px;
    margin-bottom: -80px;
  }
  
  #see-the-latest-btn {
    width: 427px;
    height: 85px;
    font-size: 20px;
  }
  
  #explore-the-latest {
    height: 240px;
  }
  
  #climb-peaks-div {
    height: 240px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 34px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 24px;
  }
  
  #log-summits-div {
    height: 240px;
    background-color: #148dbc;
    text-align: center;
    border-left: 2px solid #eee;
    border-right: 2px solid #eee;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 34px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 24px;
  }
  
  #follow-along-div {
    height: 240px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 34px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 24px;
  }

}

@media screen and (min-width: 1226px) and (max-width: 1279px) {
  /***  1/2: 626  1/3: 417  1/4: 313  Midpoint: 1252  ***/
  .home-text {
    font-size: 23px;
    margin-left: 95px;
    margin-right: 95px;
    line-height: 2em;
    text-align: left;
  }
  
  .home-hero-text {
    font-size: 39px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width:60px;
    height:60px;
    border-radius:30px;
    font-size:30px;
    color:#fff;
    line-height:60px;
    text-align:center;
    background:#1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 80px;
    height: 60px;
    line-height: 60px;
    font-size: 40px;
  }
  
  #home-chevron-icon {
    font-size: 80px;
  }
  
  #home-chevron-div {
    top: -100px;
    margin-bottom: -80px;
  }
  
  #see-the-latest-btn {
    width: 417px;
    height: 84px;
    font-size: 20px;
  }
  
  #explore-the-latest {
    height: 234px;
  }
  
  #climb-peaks-div {
    height: 234px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 33px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 23px;
  }
  
  #log-summits-div {
    height: 234px;
    background-color: #148dbc;
    text-align: center;
    border-left: 2px solid #eee;
    border-right: 2px solid #eee;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 33px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 23px;
  }
  
  #follow-along-div {
    height: 234px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 33px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 23px;
  }

}

@media screen and (min-width: 1173px) and (max-width: 1225px) {
  /***  1/2: 600  1/3: 400  1/4: 300  Midpoint: 1199  ***/
  .home-text {
    font-size: 20px;
    margin-left: 87px;
    margin-right: 87px;
    line-height: 2em;
    text-align: left;
  }
  
  .home-hero-text {
    font-size: 37px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    font-size: 25px;
    color: #fff;
    line-height: 50px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 70px;
    height: 50px;
    line-height: 50px;
    font-size: 30px;
  }
  
  #home-chevron-icon {
    font-size: 80px;
  }
  
  #home-chevron-div {
    top: -100px;
    margin-bottom: -80px;
  }
  
  #see-the-latest-btn {
    width: 400px;
    height: 80px;
    font-size: 20px;
  }
  
  #explore-the-latest {
    height: 225px;
  }
  
  #climb-peaks-div {
    height: 225px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 32px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 23px;
  }
  
  #log-summits-div {
    height: 225px;
    background-color: #148dbc;
    text-align: center;
    border-left: 2px solid #eee;
    border-right: 2px solid #eee;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 32px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 23px;
  }
  
  #follow-along-div {
    height: 225px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 32px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 23px;
  }

}

@media screen and (min-width: 1119px) and (max-width: 1172px) {
  /***  1/2: 573  1/3: 382  1/4: 287  Midpoint: 1146  ***/
  .home-text {
    font-size: 20px;
    margin-left: 87px;
    margin-right: 87px;
    line-height: 2em;
    text-align: left;
  }
  
  .home-hero-text {
    font-size: 36px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    font-size: 25px;
    color: #fff;
    line-height: 50px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 70px;
    height: 50px;
    line-height: 50px;
    font-size: 30px;
  }
  
  #home-chevron-icon {
    font-size: 80px;
  }
  
  #home-chevron-div {
    top: -100px;
    margin-bottom: -80px;
  }
  
  #see-the-latest-btn {
    width: 382px;
    height: 76px;
    font-size: 19px;
  }
  
  #explore-the-latest {
    height: 215px;
  }
  
  #climb-peaks-div {
    height: 215px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 30px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 22px;
  }
  
  #log-summits-div {
    height: 215px;
    background-color: #148dbc;
    text-align: center;
    border-left: 2px solid #eee;
    border-right: 2px solid #eee;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 30px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 22px;
  }
  
  #follow-along-div {
    height: 215px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 30px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 22px;
  }

}

@media screen and (min-width: 1066px) and (max-width: 1118px) {
  /***  1/2: 531  1/3: 364  1/4: 273  Midpoint: 1092  ***/
  .home-text {
    font-size: 20px;
    margin-left: 87px;
    margin-right: 87px;
    line-height: 2em;
    text-align: left;
  }
  
  .home-hero-text {
    font-size: 34px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    font-size: 25px;
    color: #fff;
    line-height: 50px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 70px;
    height: 50px;
    line-height: 50px;
    font-size: 30px;
  }
  
  #home-chevron-icon {
    font-size: 80px;
  }
  
  #home-chevron-div {
    top: -100px;
    margin-bottom: -80px;
  }
  
  #see-the-latest-btn {
    width: 364px;
    height: 74px;
    font-size: 18px;
  }
  
  #explore-the-latest {
    height: 205px;
  }
  
  #climb-peaks-div {
    height: 205px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 29px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 21px;
  }
  
  #log-summits-div {
    height: 205px;
    background-color: #148dbc;
    text-align: center;
    border-left: 2px solid #eee;
    border-right: 2px solid #eee;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 29px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 21px;
  }
  
  #follow-along-div {
    height: 205px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 29px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 21px;
  }

}

@media screen and (min-width: 1024px) and (max-width: 1065px) {
  /***  1/2: 523  1/3: 348  1/4: 261  Midpoint: 1045  ***/
  .home-text {
    font-size: 20px;
    margin-left: 87px;
    margin-right: 87px;
    line-height: 2em;
    text-align: left;
  }
  
  .home-hero-text {
    font-size: 33px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    font-size: 25px;
    color: #fff;
    line-height: 50px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 70px;
    height: 50px;
    line-height: 50px;
    font-size: 30px;
  }
  
  #home-chevron-icon {
    font-size: 80px;
  }
  
  #home-chevron-div {
    top: -100px;
    margin-bottom: -80px;
  }
  
  #see-the-latest-btn {
    width: 348px;
    height: 70px;
    font-size: 17px;
  }
  
  #explore-the-latest {
    height: 195px;
  }
  
  #climb-peaks-div {
    height: 195px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 28px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 20px;
  }
  
  #log-summits-div {
    height: 195px;
    background-color: #148dbc;
    text-align: center;
    border-left: 2px solid #eee;
    border-right: 2px solid #eee;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 28px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 20px;
  }
  
  #follow-along-div {
    height: 195px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 28px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 20px;
  }

}

@media screen and (min-width: 1013px) and (max-width: 1023px) {
  /***  1/2: 509  1/3: 339  1/4: 255  Midpoint: 1018  ***/
  .home-text {
    font-size: 20px;
    margin-left: 87px;
    margin-right: 47px;
    line-height: 2em;
    text-align: left;
  }
  
  .home-hero-text {
    font-size: 32px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    font-size: 25px;
    color: #fff;
    line-height: 50px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 70px;
    height: 50px;
    line-height: 50px;
    font-size: 30px;
  }
  
  #home-chevron-icon {
    font-size: 60px;
  }
  
  #home-chevron-div {
    top: -80px;
    margin-bottom: -60px;
  }
  
  #see-the-latest-btn {
    width: 339px;
    height: 68px;
    font-size: 17px;
  }
  
  #explore-the-latest {
    height: 190px;
  }
  
  #climb-peaks-div {
    height: 190px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 27px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 19px;
  }
  
  #log-summits-div {
    height: 190px;
    background-color: #148dbc;
    text-align: center;
    border-left: 2px solid #eee;
    border-right: 2px solid #eee;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 27px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 19px;
  }
  
  #follow-along-div {
    height: 190px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 27px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 19px;
  }

}

@media screen and (min-width: 992px) and (max-width: 1012px) {
  /***  1/2: 501  1/3: 334  1/4: 251  Midpoint: 1002  ***/
  .home-text {
    font-size: 20px;
    margin-left: 87px;
    margin-right: 47px;
    line-height: 2em;
    text-align: left;
  }
  
  .home-hero-text {
    font-size: 31px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    font-size: 25px;
    color: #fff;
    line-height: 50px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 70px;
    height: 50px;
    line-height: 50px;
    font-size: 31px;
  }
  
  #home-chevron-icon {
    font-size: 60px;
  }
  
  #home-chevron-div {
    top: -80px;
    margin-bottom: -60px;
  }
  
  #see-the-latest-btn {
    width: 334px;
    height: 67px;
    font-size: 17px;
  }
  
  #explore-the-latest {
    height: 188px;
  }
  
  #climb-peaks-div {
    height: 188px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 27px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 19px;
  }
  
  #log-summits-div {
    height: 188px;
    background-color: #148dbc;
    text-align: center;
    border-left: 2px solid #eee;
    border-right: 2px solid #eee;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 27px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 19px;
  }
  
  #follow-along-div {
    height: 188px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 27px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 19px;
  }

}

@media screen and (min-width: 960px) and (max-width: 991px) {
  /***  1/2: 488  1/3: 325  1/4: 244  Midpoint: 976  ***/
  .home-text {
    font-size: 20px;
    margin-left: 87px;
    margin-right: 47px;
    line-height: 2em;
    text-align: left;
  }
  
  .home-hero-text {
    font-size: 31px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    font-size: 25px;
    color: #fff;
    line-height: 50px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 70px;
    height: 50px;
    line-height: 50px;
    font-size: 30px;
  }
  
  #home-chevron-icon {
    font-size: 60px;
  }
  
  #home-chevron-div {
    top: -80px;
    margin-bottom: -60px;
  }
  
  #see-the-latest-btn {
    width: 325px;
    height: 65px;
    font-size: 16px;
  }
  
  #explore-the-latest {
    height: 183px;
  }
  
  #climb-peaks-div {
    height: 183px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 26px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 18px;
  }
  
  #log-summits-div {
    height: 183px;
    background-color: #148dbc;
    text-align: center;
    border-left: 2px solid #eee;
    border-right: 2px solid #eee;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 26px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 18px;
  }
  
  #follow-along-div {
    height: 183px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 26px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 18px;
  }

}

@media screen and (min-width: 906px) and (max-width: 959px) {
  /***  1/2: 466  1/3: 311  1/4: 233  Midpoint: 932  ***/
  .home-text {
    font-size: 20px;
    margin-left: 87px;
    margin-right: 47px;
    line-height: 2em;
    text-align: left;
  }
  
  .home-hero-text {
    font-size: 29px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    font-size: 25px;
    color: #fff;
    line-height: 50px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 70px;
    height: 50px;
    line-height: 50px;
    font-size: 30px;
  }
  
  #home-chevron-icon {
    font-size: 60px;
  }
  
  #home-chevron-div {
    top: -80px;
    margin-bottom: -60px;
  }
  
  #see-the-latest-btn {
    width: 311px;
    height: 62px;
    font-size: 16px;
  }
  
  #explore-the-latest {
    height: 175px;
  }
  
  #climb-peaks-div {
    height: 175px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 25px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 18px;
  }
  
  #log-summits-div {
    height: 175px;
    background-color: #148dbc;
    text-align: center;
    border-left: 2px solid #eee;
    border-right: 2px solid #eee;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 25px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 18px;
  }
  
  #follow-along-div {
    height: 175px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 25px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 18px;
  }

}

@media screen and (min-width: 853px) and (max-width: 905px) {
  /***  1/2: 440  1/3: 293  1/4: 220  Midpoint: 879  ***/
  .home-text {
    font-size: 20px;
    margin-left: 87px;
    margin-right: 47px;
    line-height: 2em;
    text-align: left;
  }
  
  .home-hero-text {
    font-size: 28px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    font-size: 25px;
    color: #fff;
    line-height: 50px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 70px;
    height: 50px;
    line-height: 50px;
    font-size: 30px;
  }
  
  #home-chevron-icon {
    font-size: 60px;
  }
  
  #home-chevron-div {
    top: -80px;
    margin-bottom: -60px;
  }
  
  #see-the-latest-btn {
    width: 293px;
    height: 59px;
    font-size: 15px;
  }
  
  #explore-the-latest {
    height: 165px;
  }
  
  #climb-peaks-div {
    height: 165px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 23px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 17px;
  }
  
  #log-summits-div {
    height: 165px;
    background-color: #148dbc;
    text-align: center;
    border-left: 2px solid #eee;
    border-right: 2px solid #eee;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 23px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 17px;
  }
  
  #follow-along-div {
    height: 165px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 23px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 17px;
  }

}

@media screen and (min-width: 800px) and (max-width: 852px) {
  /***  1/2: 413  1/3: 275  1/4: 207  Midpoint: 826  ***/
  .home-text {
    font-size: 20px;
    margin-left: 87px;
    margin-right: 47px;
    line-height: 2em;
    text-align: left;
  }
  
  .home-hero-text {
    font-size: 26px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    font-size: 25px;
    color: #fff;
    line-height: 50px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 70px;
    height: 50px;
    line-height: 50px;
    font-size: 30px;
  }
  
  #home-chevron-icon {
    font-size: 60px;
  }
  
  #home-chevron-div {
    top: -80px;
    margin-bottom: -60px;
  }
  
  #see-the-latest-btn {
    width: 275px;
    height: 55px;
    font-size: 14px;
  }
  
  #explore-the-latest {
    height: 155px;
  }
  
  #climb-peaks-div {
    height: 155px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 22px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 16px;
  }
  
  #log-summits-div {
    height: 155px;
    background-color: #148dbc;
    text-align: center;
    border-left: 2px solid #eee;
    border-right: 2px solid #eee;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 22px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 16px;
  }
  
  #follow-along-div {
    height: 155px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 22px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 16px;
  }

}

@media screen and (min-width: 768px) and (max-width: 799px) {
  /***  1/2: 392  1/3: 261  1/4: 196  Midpoint: 784  ***/
  .home-text {
    font-size: 20px;
    margin-left: 87px;
    margin-right: 47px;
    line-height: 2em;
    text-align: left;
  }
  
  .home-hero-text {
    font-size: 25px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    font-size: 25px;
    color: #fff;
    line-height: 50px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 70px;
    height: 50px;
    line-height: 50px;
    font-size: 30px;
  }  
  
  #home-chevron-icon {
    font-size: 60px;
  }
  
  #home-chevron-div {
    top: -80px;
    margin-bottom: -60px;
  }
  
  #see-the-latest-btn {
    width: 261px;
    height: 52px;
    font-size: 13px;
  }
  
  #explore-the-latest {
    height: 147px;
  }
  
  #climb-peaks-div {
    height: 147px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 21px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #log-summits-div {
    height: 147px;
    background-color: #148dbc;
    text-align: center;
    border-left: 2px solid #eee;
    border-right: 2px solid #eee;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 21px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #follow-along-div {
    height: 147px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 21px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }

}

@media screen and (min-width: 747px) and (max-width: 767px) {
  /***  1/2: 379  1/3: 252  1/4: 189  Midpoint: 757  ***/
  .home-text {
    font-size: 18px;
    margin-left: 76px;
    margin-right: 36px;
    line-height: 2em;
  }
  
  .home-hero-text {
    font-size: 24px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    font-size: 20px;
    color: #fff;
    line-height: 40px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 60px;
    height: 40px;
    line-height: 40px;
    font-size: 25px;
  }  
  
  #home-chevron-icon {
    font-size: 40px;
  }
  
  #home-chevron-div {
    top: -60px;
    margin-bottom: -40px;
  }
  
  #see-the-latest-btn {
    width: 252px;
    height: 50px;
    font-size: 13px;
  }
  
  #explore-the-latest {
    height: 142px;
  }
  
  #climb-peaks-div {
    height: 142px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 20px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 14px;
  }
  
  #log-summits-div {
    height: 142px;
    background-color: #148dbc;
    text-align: center;
    border-left: none;
    border-right: none;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 20px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 14px;
  }
  
  #follow-along-div {
    height: 142px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 20px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 14px;
  }

}

@media screen and (min-width: 693px) and (max-width: 746px) {
  /***  1/2: 360  1/3: 240  1/4: 180  Midpoint: 720  ***/
  .home-text {
    font-size: 18px;
    margin-left: 76px;
    margin-right: 36px;
    line-height: 2em;
  }
  
  .home-hero-text {
    font-size: 23px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    font-size: 20px;
    color: #fff;
    line-height: 40px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 60px;
    height: 40px;
    line-height: 40px;
    font-size: 25px;
  }    
  
  #home-chevron-icon {
    font-size: 40px;
  }
  
  #home-chevron-div {
    top: -60px;
    margin-bottom: -40px;
  }
  
  #see-the-latest-btn {
    width: 240px;
    height: 48px;
    font-size: 12px;
  }
  
  #explore-the-latest {
    height: 135px;
  }
  
  #climb-peaks-div {
    height: 135px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 19px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 14px;
  }
  
  #log-summits-div {
    height: 135px;
    background-color: #148dbc;
    text-align: center;
    border-left: none;
    border-right: none;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 19px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 14px;
  }
  
  #follow-along-div {
    height: 135px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 19px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 14px;
  }

}

@media screen and (min-width: 640px) and (max-width: 692px) {
  /***  1/2: 333  1/3: 222  1/4: 167  Midpoint: 666  ***/
  .home-text {
    font-size: 18px;
    margin-left: 76px;
    margin-right: 36px;
    line-height: 2em;
  }
  
  .home-hero-text {
    font-size: 21px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    font-size: 20px;
    color: #fff;
    line-height: 40px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 60px;
    height: 40px;
    line-height: 40px;
    font-size: 25px;
  }    
  
  #home-chevron-icon {
    font-size: 40px;
  }
  
  #home-chevron-div {
    top: -60px;
    margin-bottom: -40px;
  }
  
  #see-the-latest-btn {
    width: 240px;
    height: 48px;
    font-size: 12px;
  }
  
  #explore-the-latest {
    height: 135px;
  }
  
  #climb-peaks-div {
    height: 125px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 18px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 13px;
  }
  
  #log-summits-div {
    height: 125px;
    background-color: #148dbc;
    text-align: center;
    border-left: none;
    border-right: none;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 18px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 13px;
  }
  
  #follow-along-div {
    height: 125px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 18px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 13px;
  }

}

@media screen and (min-width: 587px) and (max-width: 639px) {
  /***  1/2: 307  1/3: 204  1/4: 153  Midpoint: 613  ***/
  .home-text {
    font-size: 18px;
    margin-left: 76px;
    margin-right: 36px;
    line-height: 2em;
  }
  
  .home-hero-text {
    font-size: 20px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    font-size: 20px;
    color: #fff;
    line-height: 40px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 60px;
    height: 40px;
    line-height: 40px;
    font-size: 25px;
  }    
  
  #home-chevron-icon {
    font-size: 40px;
  }
  
  #home-chevron-div {
    top: -60px;
    margin-bottom: -40px;
  }
  
  #see-the-latest-btn {
    width: 240px;
    height: 48px;
    font-size: 12px;
  }
  
  #explore-the-latest {
    height: 135px;
  }
  
  #climb-peaks-div {
    height: 115px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 16px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 12px;
  }
  
  #log-summits-div {
    height: 115px;
    background-color: #148dbc;
    text-align: center;
    border-left: none;
    border-right: none;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 16px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 12px;
  }
  
  #follow-along-div {
    height: 115px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 16px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 12px;
  }

}

@media screen and (min-width: 533px) and (max-width: 586px) {
  /***  1/2: 280  1/3: 187  1/4: 140  Midpoint: 560  ***/
  .home-text {
    font-size: 18px;
    margin-left: 76px;
    margin-right: 36px;
    line-height: 2em;
  }
  
  .home-hero-text {
    font-size: 20px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    font-size: 20px;
    color: #fff;
    line-height: 40px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 60px;
    height: 40px;
    line-height: 40px;
    font-size: 25px;
  }    
  
  #home-chevron-icon {
    font-size: 40px;
  }
  
  #home-chevron-div {
    top: -60px;
    margin-bottom: -40px;
  }
  
  #see-the-latest-btn {
    width: 240px;
    height: 48px;
    font-size: 12px;
  }
  
  #explore-the-latest {
    height: 135px;
  }
  
  #climb-peaks-div {
    height: 105px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }
  
  #log-summits-div {
    height: 105px;
    background-color: #148dbc;
    text-align: center;
    border-left: none;
    border-right: none;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }
  
  #follow-along-div {
    height: 105px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }

}

@media screen and (min-width: 480px) and (max-width: 532px) {
  /***  1/2: 253  1/3: 169  1/4: 127  Midpoint: 506  ***/
  .home-text {
    font-size: 18px;
    margin-left: 76px;
    margin-right: 36px;
    line-height: 2em;
  }
  
  .home-hero-text {
    font-size: 20px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    font-size: 20px;
    color: #fff;
    line-height: 40px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 60px;
    height: 40px;
    line-height: 40px;
    font-size: 25px;
  }    
  
  #home-chevron-icon {
    font-size: 40px;
  }
  
  #home-chevron-div {
    top: -60px;
    margin-bottom: -40px;
  }
  
  #see-the-latest-btn {
    width: 240px;
    height: 48px;
    font-size: 12px;
  }
  
  #explore-the-latest {
    height: 135px;
  }
  
  #climb-peaks-div {
    height: 100px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }
  
  #log-summits-div {
    height: 100px;
    background-color: #148dbc;
    text-align: center;
    border-left: none;
    border-right: none;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }
  
  #follow-along-div {
    height: 100px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }

}

@media screen and (min-width: 427px) and (max-width: 479px) {
  /***  1/2: 227  1/3: 151  1/4: 113  Midpoint: 453  ***/
  .home-text {
    font-size: 15px;
    margin-left: 60px;
    margin-right: 20px;
    line-height: 2em;
  }
  
  .home-hero-text {
    font-size: 20px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 30px;
    height: 30px;
    border-radius: 15px;
    font-size: 16px;
    color: #fff;
    line-height: 30px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 45px;
    height: 30px;
    line-height: 30px;
    font-size: 20px;
  }    
  
  #home-chevron-icon {
    font-size: 30px;
  }
  
  #home-chevron-div {
    top: -40px;
    margin-bottom: -30px;
  }
  
  #see-the-latest-btn {
    width: 240px;
    height: 48px;
    font-size: 12px;
  }
  
  #explore-the-latest {
    height: 135px;
  }
    
  #climb-peaks-div {
    height: 100px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }
  
  #log-summits-div {
    height: 100px;
    background-color: #148dbc;
    text-align: center;
    border-left: none;
    border-right: none;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }
  
  #follow-along-div {
    height: 100px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }

}

@media screen and (min-width: 373px) and (max-width: 426px) {
  /***  1/2: 200  1/3: 133  1/4: 100  Midpoint: 400  ***/
  .home-text {
    font-size: 15px;
    margin-left: 60px;
    margin-right: 20px;
    line-height: 2em;
  }
  
  .home-hero-text {
    font-size: 20px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 30px;
    height: 30px;
    border-radius: 15px;
    font-size: 16px;
    color: #fff;
    line-height: 30px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 45px;
    height: 30px;
    line-height: 30px;
    font-size: 20px;
  }      
  
  #home-chevron-icon {
    font-size: 30px;
  }
  
  #home-chevron-div {
    top: -40px;
    margin-bottom: -30px;
  }
  
  #see-the-latest-btn {
    width: 240px;
    height: 48px;
    font-size: 12px;
  }
  
  #explore-the-latest {
    height: 135px;
  }
  
  #climb-peaks-div {
    height: 100px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }
  
  #log-summits-div {
    height: 100px;
    background-color: #148dbc;
    text-align: center;
    border-left: none;
    border-right: none;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }
  
  #follow-along-div {
    height: 100px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }

}

@media screen and (min-width: 320px) and (max-width: 372px) {
  /***  1/2: 173  1/3: 115  1/4: 87  Midpoint: 346  ***/
  .home-text {
    font-size: 15px;
    margin-left: 60px;
    margin-right: 20px;
    line-height: 2em;
  }
  
  .home-hero-text {
    font-size: 20px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 30px;
    height: 30px;
    border-radius: 15px;
    font-size: 16px;
    color: #fff;
    line-height: 30px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 45px;
    height: 30px;
    line-height: 30px;
    font-size: 20px;
  }     
  
  #home-chevron-icon {
    font-size: 30px;
  }
  
  #home-chevron-div {
    top: -40px;
    margin-bottom: -30px;
  }
  
  #see-the-latest-btn {
    width: 240px;
    height: 48px;
    font-size: 12px;
  }
  
  #explore-the-latest {
    height: 135px;
  }
  
  #climb-peaks-div {
    height: 100px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }
  
  #log-summits-div {
    height: 100px;
    background-color: #148dbc;
    text-align: center;
    border-left: none;
    border-right: none;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }
  
  #follow-along-div {
    height: 100px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }

}

@media screen and (min-width: 1px) and (max-width: 319px) {
  /***  1/2: 160  1/3: 107  1/4: 80  Midpoint: 319  ***/
  .home-text {
    font-size: 15px;
    margin-left: 60px;
    margin-right: 20px;
    line-height: 2em;
  }
  
  .home-hero-text {
    font-size: 20px;
    width: 100%;
    text-align: center;
    line-height: 2em;
  }
  
  .home-bullet {
    width: 30px;
    height: 30px;
    border-radius: 15px;
    font-size: 16px;
    color: #fff;
    line-height: 30px;
    text-align: center;
    background: #1eb0f1;
    display: block;
    float: left;
  }
  
  .home-bullet-caption {
    padding-left: 45px;
    height: 30px;
    line-height: 30px;
    font-size: 20px;
  }    
  
  #home-chevron-icon {
    font-size: 30px;
  }
  
  #home-chevron-div {
    top: -40px;
    margin-bottom: -30px;
  }
  
  #see-the-latest-btn {
    width: 240px;
    height: 48px;
    font-size: 12px;
  }
  
  #explore-the-latest {
    height: 135px;
  }
  
  #climb-peaks-div {
    height: 100px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #climb-peaks-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #climb-peaks-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }
  
  #log-summits-div {
    height: 100px;
    background-color: #148dbc;
    text-align: center;
    border-left: none;
    border-right: none;
  }
  
  #log-summits-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #log-summits-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }
  
  #follow-along-div {
    height: 100px;
    background-color: #0daef8;
    text-align: center;
  }
  
  #follow-along-header {
    color: #fff;
    font-weight: 500;
    top: 20%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 15px;
  }
  
  #follow-along-stat {
    color: #fff;
    top: 30%;
    width: 100%;
    position: relative;
    float: left;
    font-size: 11px;
  }

}

/****************/
/*    FOOTER    */
/****************/

#footer {background: #222; text-align: center; color: #707070; position: absolute; left: 0; bottom: 0; width: 100%; height: 51px; line-height: 50px;}
#footer a {color: #707070; font-weight: 400; font-style: normal; font-size: 14px; margin-right: 3%; text-decoration: none;}
#footer a:hover {color: #ffffff; text-decoration: none;}
#footer i {font-size: 60px; padding: 0 10px;}
#footer p, #footer ul {font-size: 16px; letter-spacing: .15em;}
#footer ul {list-style: none;}
#footer li {display: block; padding: 0 12px; margin-bottom: 10px;}
#footer span {margin: 0 15px;}

.footer {
  text-align: left;
}

.footer-nav {
  border-radius: 0px;
  margin-top: -35px;
  margin-bottom: 0px;
  position: absolute;
  bottom: 0px;
  width: 100%;
}

.footer-nav .container .navbar {
  margin-bottom: 5px;
}

#footerNav {
  margin-top: 14px;
}

#footerNav > li {
  top: 0px;
  font-size: 14px;
}

#footerNav > li > a {
  color: #707070;
}

#footerNav > li > a:hover {
  color: #fff;
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .footer-links {
    display: block;
    text-align: center !important;
  }
  .footer {
    text-align: center;
    margin-top: 20px;
  }
  .footer-container {
    width: 100%;
  }
  #footer a {
    margin-right: 0px;
  }
}

/*****************/
/*    SPACERS    */
/*****************/

.sp-10 {height: 10px}
.sp-15 {height: 15px}
.sp-22 {height: 22px}
.sp-30 {height: 30px}
.sp-36 {height: 36px}
.sp-40 {height: 40px}
.sp-50 {height: 50px}
.sp-55 {height: 55px}
.sp-60 {height: 40px} /* tweaked here vs changing to 40px across entire site */
.sp-80 {height: 80px}
.sp-100 {height: 100px}
.sp-120 {height: 120px}

/************************************/
/*    PEAKS PAGE (list of peaks)    */
/************************************/

.modal.fade:not(.in).right .modal-dialog {
	-webkit-transform: translate3d(25%, 0, 0);
	transform: translate3d(25%, 0, 0);
}

.peak_list_cont {
    margin-bottom: 0px;
}

.peak-list-table {
  letter-spacing: initial;
}

.peak-list-table > thead {
  background-color: #333;
  font-size: 12px;
}

.peak-list-table > thead > tr > th {
  color: #ccc;
  font-weight: 300;
  vertical-align: middle;
  padding-bottom: 0px;
  border-bottom-width: 1px;
}

.peak-list-table > thead > tr > th > a {
  color: #00b1f2;
}

.peak-list-table > tbody > tr > td {
  font-size: 10px;
}

.peak-list-thumb {
  width: 65px;
}

.peak-list-search-input {
  width: 90%;
  border-radius: 0;
  background-color: #fff;
  font-weight: 400;
  font-size: 20px;
  padding: 10px;
  height: 45px;
  margin: 5%;
  margin-bottom: 0px;
  border-style: solid;
  border-color: #ccc;
  border-width: 1px;
}

.peak-list-content {
  background: none;
  background-color: #ffffff;
  margin-left: 0px;
  margin-right: 0px;
}

.peak-list-btn {
  background-color: #00b1f2;
  color: #fff;
  width: 90%;
  margin-top: 15px;
  margin-bottom: 5px;
}

.peak-list-btn:hover {
  background-color: #00bbff;
}

#elevation-slider > p > label {
  font-weight: 300;
}

#prominence-slider > p > label {
  font-weight: 300;
}

div#slider-range .ui-slider-handle, div#prominence-slider-range .ui-slider-handle {
    width: 12px;
    height: 12px;
}

.ui-slider-handle {
  cursor: pointer !important;
}

.ui-slider {
  cursor: pointer !important;
}

/* searchbox testing */
.searchbox{
    position:relative;
    top: 15px;
    min-width:20px;
    width:0%;
    height:40px;
    float:right;
    overflow:hidden;
    
    -webkit-transition: width 0.3s;
    -moz-transition: width 0.3s;
    -ms-transition: width 0.3s;
    -o-transition: width 0.3s;
    transition: width 0.3s;
}

.searchbox-input{
    top:0;
    right:0;
    border:0;
    outline:0;
    background:#fff;
    width:100%;
    height:30px;
    margin:0px 0px 0px 20px;
    padding:2px 0px 0px 10px;
    color:#333333;
}

.mobile-searchbox{
    position:relative;
    top: 0px;
    right: 5px;
    min-width:20px;
    width:0%;
    height:50px;
    float:right;
    overflow:hidden;
    
    -webkit-transition: width 0.3s;
    -moz-transition: width 0.3s;
    -ms-transition: width 0.3s;
    -o-transition: width 0.3s;
    transition: width 0.3s;
}

.mobile-searchbox-input{
    top:0;
    right:0;
    border:0;
    outline:0;
    background:#fff;
    width:100%;
    height:30px;
    margin:0px 0px 0px 20px;
    padding:2px 0px 0px 10px;
    color:#333333;
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .searchbox-input::-webkit-input-placeholder {
      color: #ccc;
      font-weight: 300;
  }
  .searchbox-input:-moz-placeholder {
      color: #ccc;
      font-weight: 300;
  }
  .searchbox-input::-moz-placeholder {
      color: #ccc;
      font-weight: 300;
  }
  .searchbox-input:-ms-input-placeholder {
      color: #ccc;
      font-weight: 300;
  }
}

@media screen and (min-width: 768px) {
  .searchbox-input::-webkit-input-placeholder {
      color: #ccc;
      font-weight: 300;
      font-size: 14px;
  }
  .searchbox-input:-moz-placeholder {
      color: #ccc;
      font-weight: 300;
      font-size: 14px;
  }
  .searchbox-input::-moz-placeholder {
      color: #ccc;
      font-weight: 300;
      font-size: 14px;
  }
  .searchbox-input:-ms-input-placeholder {
      color: #ccc;
      font-weight: 300;
      font-size: 14px;
  }
}

.searchbox-icon,
.searchbox-submit{
    width:20px;
    height:20px;
    display:block;
    position:absolute;
    top:10px;
    font-family:verdana, sans-serif;
    font-size:14px;
    right:0;
    padding:0;
    margin:0;
    border:0;
    outline:0;
    line-height:20px;
    text-align:center;
    cursor:pointer;
    color:#999;
}

.searchbox-icon:hover {
  color: #999;
}

.searchbox-icon:active {
  color: #999999;
}

.mobile-searchbox-icon {
    width:20px;
    height:20px;
    display:block;
    position:absolute;
    top:10px;
    font-family:verdana, sans-serif;
    font-size:14px;
    right:0;
    padding:0;
    margin:0;
    border:0;
    outline:0;
    line-height:20px;
    text-align:center;
    cursor:pointer;
    color:#999;
}

.mobile-searchbox-icon:hover {
  color: #999;
}

.mobile-searchbox-icon:active {
  color: #999999;
}

.searchbox-submit {
  display: none;
}

@media screen and (min-width: 768px) {
  .searchbox-open{
      width:290px;
  }
}  
@media screen and (max-width: 767px) {
  .searchbox-open{
      width:100%;
  }
}  

.searchbox-icon-open {
  margin-right: 10px;
  margin-top: 3px;
  color: #ccc;
}

.searchbox-input-open {
  /*margin:0;*/
  width: 250px;
  float: right;
}

/***********************************/
/*    PEAK PAGE (specific peak)    */
/***********************************/

.peak_map_container {
    position: relative;
    width: 100%;
    /*padding-bottom: 93.5%; /* Ratio 16:9 ( 100%/16*9 = 56.25% ) */
}
.peak_map_container .peak_map_canvas{
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: 0;
    padding: 0;
}

.youve-climbed {
  font-size: 12px;
  font-weight: 300;
  color: #00b330;
  text-shadow: 0px 0px 4px rgba(204, 255, 51, 1);
  margin-left: 55px;
}
@-webkit-keyframes markerPulse {
  from { -webkit-filter: brightness(1.2) saturate(1.5); }
  50% { -webkit-filter: brightness(0.9) saturate(1); }
  to { -webkit-filter: brightness(1.2) saturate(1.5); }
}
.marker-pulse {
  -webkit-animation-name: markerPulse;
  -webkit-animation-duration: 3s;
  -webkit-animation-iteration-count: infinite;
}

@media screen and (min-width: 1px) and (max-width: 1023px) {
  #peaks-map {
    height: 100%;
    position: absolute;
    width: 100%;
    top: 64px;
    bottom: 0px;
    left: 0px;
    margin-left: 0px !important;
    margin-top: 0px !important;
  }
  #collapse-search {
    display: none;
  }
}

@media screen and (min-width: 1024px) {
  #peaks-map {
    height: 100%;
    position: absolute;
    width: 100%;
    top: 14px;
    bottom: 0px;
    left: 240px;
    margin-left: 0px !important;
    margin-top: 0px !important;
  }
  #collapse-search {
    display: block;
  }
}

@media screen and (min-width: 1px) and (max-width: 375px) {
  .mobile-header-subtitle {
    font-size: 9px;
  }
}

@media screen and (min-width: 376px) {
  .mobile-header-subtitle {
    font-size: 9px;
  }
}  

div#bigphoto a img#peak-img {
    width: 100%;
    height: auto;
}

.peak-credit {
  position: absolute;
  right: 20px;
  bottom: 10px;
}

.peak-credit > a {
  color: white;
  font-size: 14px;
  text-shadow: 1px 1px 0 black;
  text-decoration: none;
}

.photo-info {
    text-align: center;
    color: #fff;
    position: absolute;
    bottom: 0%;
    left: 0%;
    width: 100%;
    height: 15%;
    background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.5) 100%);
}

.photo-info-title {
    text-align: center;
    color: #fff;
    position: absolute;
    top: 0%;
    left: 0%;
    width: 100%;
    height: 15%;
    background-image: linear-gradient(to top, transparent 0px, rgba(0, 0, 0, 0.5) 100%);
}

.user-photo-info {
    text-align: left;
    font-size: 10px;
    color: #fff;
    position: absolute;
    bottom: 0%;
    left: 0%;
    width: 100%;
    height: 25%;
    background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%);
}

.static-photo-info {
    text-align: left;
    font-size: 10px;
    color: #fff;
    position: absolute;
    bottom: 0%;
    left: 0%;
    width: 100%;
    height: 25%;
    background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%);
}

.peak-photo-with-caption {
  height: 40%;
}

.hero-user-photo-info {
    text-align: left;
    font-size: 10px;
    color: #fff;
    position: absolute;
    bottom: 0%;
    left: 0%;
    width: 100%;
    height: 15%;
    background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%);
}

.empty-photo-info {
    text-align: left;
    font-size: 10px;
    color: #fff;
    position: absolute;
    bottom: 0%;
    left: 0%;
    width: 100%;
    height: 25%;
    background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%);
}

.user-photo-caption {
    text-align: left;
    font-size: 10px;
    color: #fff;
    position: absolute;
    bottom: 0%;
    left: 0%;
    width: 100%;
    height: 35%;
    background: rgba(0, 0, 0, 0.5) none repeat scroll 0% 0%;
}

.user-img {
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

.award-photo-user {
  font-size: 23px;
  margin-bottom: 0px;
  color: #fff;
}

.award-photo-details {
  font-size: 10px;
}

.map-tooltip-img {
  width: 220px;
  height: 165px;
}

.map-tooltip-info {
  text-align: left;
  font-size: 10px;
  color: #fff;
  position: absolute;
  bottom: 0%;
  left: 0%;
  width: 100%;
  height: 50px;
  padding-left: 8px;
  padding-top: 2px;
  -webkit-backdrop-filter: blur(10px);
  background-color: rgba(0, 0, 0, .4);
}

.map-tooltip-peak-name {
  font-size: 12px;
  font-weight: 500;
  width: 204px;
}

.map-tooltip-peak-stats {
  font-size: 9px;
  width: 204px;
  letter-spacing: .8px;
}

.noUi-base {
    width: 206px;
    margin-left: 12px;
}

#gm-custom-mapbutton {
  width: 132px;
  margin-left: -28px;
}  

#gm-custom-mapdropdown {
  width: 132px;
  margin-left: -28px;
}  

#gm-custom-mapbutton:hover {
  background-color: rgb(235, 235, 235) !important;
  color: rgb(0, 0, 0) !important;
}

#gm-custom-mapoption-map {
  width: 132px;
  margin-left: -2px;
}

#gm-custom-mapoption-map:hover {
  background-color: rgb(235, 235, 235) !important;
  color: rgb(0, 0, 0) !important;
}

#gm-custom-mapoption-topo {
  width: 132px;
  margin-left: -2px;
}

#gm-custom-mapoption-topo:hover {
  background-color: rgb(235, 235, 235) !important;
  color: rgb(0, 0, 0) !important;
}

#gm-custom-mapoption-sat {
  width: 132px;
  margin-left: -2px;
}

#gm-custom-mapoption-sat:hover {
  background-color: rgb(235, 235, 235) !important;
  color: rgb(0, 0, 0) !important;
}

#gm-custom-mapoption-satstreets {
  width: 132px;
  margin-left: -2px;
}

#gm-custom-mapoption-satstreets:hover {
  background-color: rgb(235, 235, 235) !important;
  color: rgb(0, 0, 0) !important;
}

#gm-custom-mapoption-terrain {
  width: 132px;
  margin-left: -2px;
}

#gm-custom-mapoption-terrain:hover {
  background-color: rgb(235, 235, 235) !important;
  color: rgb(0, 0, 0) !important;
}

#gm-custom-mapoption-outdoors {
  width: 132px;
  margin-left: -2px;
}

#gm-custom-mapoption-outdoors:hover {
  background-color: rgb(235, 235, 235) !important;
  color: rgb(0, 0, 0) !important;
}

/***********************************/
/*          REGIONS PAGE           */
/***********************************/

@media screen and (min-width: 1px) and (max-width: 767px) {
  .regions-subnav-fixed {
    position: fixed;
    top: 100px;
    width: 100%;
    padding-right: 30px;
    z-index: 1002;
  }
}

@media screen and (min-width: 768px) and (max-width: 1279px) {
  .regions-subnav-fixed {
    position: fixed;
    top: 121px;
    width: 100%;
    padding-right: 30px;
    z-index: 1002;
  }
}

@media screen and (min-width: 1280px) {
  .regions-subnav-fixed {
    position: fixed;
    top: 121px;
    width: 1280px;
    z-index: 1002;
  }
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  #region-list-header-mobile {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .region-list-header-title {
    font-size: 21px;
    font-weight: 500;
  }
  .region-list-header-sort {
    font-size: 14px;
    font-weight: 300;
    color: #666;
  }
  .region-list-item {
    font-size: 12px;
  }
  #countries-list-header-mobile {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .countries-list-header-title {
    font-size: 21px;
    font-weight: 500;
  }
  .countries-list-header-sort {
    font-size: 14px;
    font-weight: 300;
    color: #666;
  }
  .countries-list-item {
    font-size: 12px;
  }
}

@media screen and (min-width: 768px) and (max-width: 1279px) {
  #region-list-header {
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .region-list-header-title {
    font-size: 17px;
    font-weight: 500;
  }
  .region-list-header-sort {
    font-size: 12px;
    font-weight: 300;
    color: #666;
  }
  .region-list-item {
    font-size: 12px;
  }
  #countries-list-header {
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .countries-list-header-title {
    font-size: 17px;
    font-weight: 500;
  }
  .countries-list-header-sort {
    font-size: 12px;
    font-weight: 300;
    color: #666;
  }
  .countries-list-item {
    font-size: 12px;
  }
}

@media screen and (min-width: 1280px) {
  #region-list-header {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .region-list-header-title {
    font-size: 21px;
    font-weight: 500;
  }
  .region-list-header-sort {
    font-size: 14px;
    font-weight: 300;
    color: #666;
  }
  .region-list-item {
    font-size: 12px;
  }
  #countries-list-header {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .countries-list-header-title {
    font-size: 21px;
    font-weight: 500;
  }
  .countries-list-header-sort {
    font-size: 14px;
    font-weight: 300;
    color: #666;
  }
  .countries-list-item {
    font-size: 12px;
  }
}

.region-list {
  font-size: 21px;
  font-weight: 500;
  color: #00b1f2;
  padding-left: 20px;
  padding-top: 5px;
}

.region-list-item {
  padding-top: 30px;
}

.countries-list {
  font-size: 21px;
  font-weight: 500;
  color: #00b1f2;
  padding-left: 20px;
  padding-top: 5px;
}

.countries-list-item {
  padding-top: 30px;
}

.summit-list-stats {
  font-size: 14px;
  color: #999;
}

.summit-list-footer {
  font-size: 14px;
  color: #00b1f2;
}

.peak-description {
  letter-spacing: 1px;
}

#continents-list-tablet {
  padding-top: 20px;
  padding-bottom: 20px;
  text-align: center;
}

.continent-link-text {
  color: #fff;
}

.continent-link-text:hover {
  color: #ccc;
}

@media screen and (min-width: 1280px) {
  .continent-link-text {
    font-size: 18px;
  }
}

@media screen and (min-width: 1216px) and (max-width: 1279px) {
  .continent-link-text {
    font-size: 17px;
  }
}

@media screen and (min-width: 1152px) and (max-width: 1215px) {
  .continent-link-text {
    font-size: 16px;
  }
}

@media screen and (min-width: 1088px) and (max-width: 1151px) {
  .continent-link-text {
    font-size: 15px;
  }
}

@media screen and (min-width: 1024px) and (max-width: 1087px) {
  .continent-link-text {
    font-size: 14px;
  }
}

@media screen and (min-width: 960px) and (max-width: 1023px) {
  .continent-link-text {
    font-size: 13px;
  }
}

@media screen and (min-width: 896px) and (max-width: 959px) {
  .continent-link-text {
    font-size: 12px;
  }
}

@media screen and (min-width: 832px) and (max-width: 895px) {
  .continent-link-text {
    font-size: 11px;
  }
}

@media screen and (min-width: 768px) and (max-width: 831px) {
  .continent-link-text {
    font-size: 10px;
  }
}

@media screen and (min-width: 1px) and (max-width: 479px) {

  .country-title {
    font-weight: 300;
    font-size: 16px;
    line-height: 1;
  }
  
  .country-info {
    font-size: 12px;
    color: #999;
    padding-top: 5px;
  }
  
  .peak-list-stats-header {
    margin-top: 10px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 0px;
  }
  
  .peak-list-stats {
    font-size: 10px;
    color: #999;
  }
  
  .peak-list-highlights {
    font-size: 10px;
    color: #000000;
    line-height: 16px;
    margin-top: 0px;
    margin-right: 6px;
  }
  
  .peak-list-peak-title {
    margin-top: 10px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 0px;
    color: #333;
  }
  
  .peak-list-peak-subtitle {
    font-size: 11px;
    color: #aaa;
    font-weight: 300;
  }
  
  .peak-list-peak-title-margin {
    margin-top: 0px;
  }
  
  .peak-list-peak-subtitle-margin {
    margin-top: 0px;
  }
  .region-info-section-header {
    font-size: 14px;
  }
  .challenge-list-challenge-title {
    font-size: 14px;
    color: #333;
  }
  
}

@media screen and (min-width: 480px) and (max-width: 767px) {

  .country-title {
    font-weight: 300;
    font-size: 24px;
  }
  
  .country-info {
    font-size: 15px;
    color: #999;
  }
  
  .peak-list-stats-header {
    margin-top: 10px;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 0px;
  }
  
  .peak-list-stats {
    font-size: 12px;
    color: #999;
  }
  
  .peak-list-highlights {
    font-size: 12px;
    color: #000000;
    line-height: 20px;
    margin-top: 0px;
    margin-right: 8px;
  }
  
  .peak-list-peak-title {
    margin-top: 10px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 0px;
    color: #333;
  }
  
  .peak-list-peak-subtitle {
    font-size: 11px;
    color: #aaa;
    font-weight: 300;
  }
  
  .peak-list-peak-title-margin {
    margin-top: 0px;
  }
  
  .peak-list-peak-subtitle-margin {
    margin-top: 0px;
  }
  .region-info-section-header {
    font-size: 14px;
  }
  .challenge-list-challenge-title {
    font-size: 14px;
    color: #333;
  }
  
}

@media screen and (min-width: 768px) and (max-width: 1023px) {

  .country-title {
    font-weight: 500;
    font-size: 26px;
  }
  
  .country-info {
    font-size: 18px;
    color: #999;
  }
  
  .peak-list-stats-header {
    margin-top: 10px;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 0px;
  }
  
  .peak-list-stats {
    font-size: 12px;
    color: #999;
  }
  
  .peak-list-highlights {
    font-size: 12px;
    color: #000000;
    line-height: 20px;
    margin-top: 0px;
    margin-right: 8px;
  }
  
  .peak-list-peak-title {
    margin-top: 10px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 0px;
    color: #333;
  }
  
  .peak-list-peak-subtitle {
    font-size: 11px;
    color: #aaa;
    font-weight: 300;
  }
  
  .peak-list-peak-title-margin {
    margin-top: 0px;
  }
  
  .peak-list-peak-subtitle-margin {
    margin-top: 0px;
  }
  .region-info-section-header {
    font-size: 14px;
  }
  .challenge-list-challenge-title {
    font-size: 16px;
    color: #333;
  }
  
}

@media screen and (min-width: 1024px) {

  .country-title {
    font-weight: 500;
    font-size: 44px;
  }
  
  .country-info {
    font-size: 21px;
    color: #999;
  }
  
  .peak-list-stats-header {
    margin-top: 10px;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 0px;
  }
  
  .peak-list-stats {
    font-size: 14px;
    color: #999;
  }
  
  .peak-list-highlights {
    font-size: 14px;
    color: #000000;
    line-height: 26px;
    margin-top: 0px;
    margin-right: 10px;
    margin-left: -5px;
  }
  
  .peak-list-peak-title {
    margin-top: 0px;
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 0px;
    color: #333;
  }
  
  .peak-list-peak-subtitle {
    font-size: 14px;
    color: #aaa;
  }
  
  .peak-list-peak-title-margin {
    margin-top: 0px;
  }
  
  .peak-list-peak-subtitle-margin {
    margin-top: 10px;
  }
  .region-info-section-header {
    font-size: 18px;
  }
  .challenge-list-challenge-title {
    font-size: 18px;
    color: #333;
  }
  
}

.ui-datepicker-title {
  width: 345px;
  background-color: #00b1f2;
  cursor: pointer;
  font-weight: 500;
}
.ui-datepicker-next {
  display: none;
}
.ui-datepicker-prev {
  display: none;
}


.ui-state-default.ui-state-active {
  color: #fff;
  background-color: #f24100;
}

td a.ui-state-default {
    color: #666;
}

td a.ui-state-default:hover {
    color: #fff;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
    border: none !important;
    text-align: center;
}


.ui-widget.ui-widget-content {
    border: none !important;
}
.ui-datepicker-today > a {
  font-weight: 700 !important;
  color: #666 !important;
  background-color: #fcf3bd !important;
}
.ui-datepicker table {
  font-size: 16px;
  background-color: #f2f2f2;
}
.ui-datepicker {
  width: 345px;
  background-color: #f2f2f2;
}   
.ui-widget-header {
  border: none;
  background: #fff;  
}  
.ui-datepicker-month, .ui-datepicker-year {
    width: 49%;
    height: 40px;
    background-color: #00b1f2;
    color: #fff;
    border: none;
    text-indent: 60px;
    -moz-appearance: none;
    -webkit-appearance:none;
    background: url(https://peakery-static.s3-us-west-1.amazonaws.com/img/datepicker-caret-down.png) no-repeat right;
    background-position-y: -5px;
    background-position-x: 90px;
    background-size: 50px 50px;
    cursor: pointer;
}
.ui-datepicker-month {
  border-right: 1px solid #fff;
  border-radius: 0px;
}
.ui-datepicker-header {
  background-color: #00b1f2;
  display: none;
}
.ui-datepicker .ui-datepicker-title {
    margin: 0px;
}
.ui-datepicker-month > option {
  background: #c0c0c0;
}
.ui-datepicker-year > option {
  background: #c0c0c0;
}

#prominence-slider > p > label {
    font-weight: 300;
}

#prominence-slider {
    padding-top: 16px;
    margin-left: 8px;
}

#prominence-slider p {
    margin-bottom: 10px;
    display: block;
}

div#prominence-slider-range {
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    height: 2px;
    overflow: visible;
    background-color: #ccc;
}

div#prominence-slider-range .ui-slider-range {
    background-color: #00a2de;
}

div#prominence-slider-range .ui-slider-handle {
    display: block;
    width: 12px;
    height: 12px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
    background-color: #00b2f2;
    background-image: none;
}

#elevation-slider > p > label {
    font-weight: 300;
}

#elevation-slider {
    padding-top: 16px;
    margin-left: 8px;
}

#elevation-slider p {
    margin-bottom: 10px;
    display: block;
}

div#slider-range {
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    height: 2px;
    overflow: visible;
    background-color: #ccc;
}

div#slider-range .ui-slider-range {
    background-color: #00a2de;
}

div#slider-range .ui-slider-handle {
    display: block;
    width: 12px;
    height: 12px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
    background-color: #00b2f2;
    background-image: none;
}

#summits-slider > p > label {
    font-weight: 300;
}

#summits-slider {
    padding-top: 16px;
    margin-left: 8px;
}

#summits-slider p {
    margin-bottom: 10px;
    display: block;
}

div#summits-slider-range {
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    height: 2px;
    overflow: visible;
    background-color: #ccc;
}

div#summits-slider-range .ui-slider-range {
    background-color: #00a2de;
}

div#summits-slider-range .ui-slider-handle {
    display: block;
    width: 12px;
    height: 12px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
    background-color: #00b2f2;
    background-image: none;
}

#difficulty-slider > p > label {
    font-weight: 300;
}

#difficulty-slider {
    padding-top: 16px;
    margin-left: 8px;
    margin-bottom: 15px
}

#difficulty-slider p {
    margin-bottom: 10px;
    display: block;
}

div#difficulty-slider-range {
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    height: 2px;
    overflow: visible;
    background-color: #ccc;
}

div#difficulty-slider-range .ui-slider-range {
    background-color: #00a2de;
}

div#difficulty-slider-range .ui-slider-handle {
    display: block;
    width: 12px;
    height: 12px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
    background-color: #00b2f2;
    background-image: none;
}

/***********************************/
/*          PROFILE PAGE           */
/***********************************/

.user-challenge-header {
  font-size: 21px;
  font-weight: 500;
  margin-bottom: 20px;
}

.user-challenge-show {
  text-align: left;
  margin-left: 15px;
}

.user-challenge-details-container {
    display: none;
    width: 100%;
}

.user-challenge-details {
    display: inline-block;
    margin-left: 0px;
    margin-top: 0px;
    margin-bottom: 0px;
    width: 100%;
}

@media screen and (min-width: 1025px) {
  .user-challenge-photo {
      width: 80px;
      display: inline-block;
      cursor: pointer;
  }
  
  .user-challenge-photo-img {
      width: 80px;
      height: 58px;
  }
}

@media screen and (min-width: 769px) and (max-width: 1024px) {
  .user-challenge-photo {
      width: 85.33px;
      display: inline-block;
      cursor: pointer;
  }
  
  .user-challenge-photo-img {
      width: 85.33px;
      height: 62px;
  }
}

@media screen and (min-width: 376px) and (max-width: 768px) {
  .user-challenge-photo {
      width: 96px;
      display: inline-block;
      cursor: pointer;
  }
  
  .user-challenge-photo-img {
      width: 96px;
      height: 70px;
  }
}

@media screen and (min-width: 1px) and (max-width: 375px) {
  .user-challenge-photo {
      width: 75px;
      display: inline-block;
      cursor: pointer;
  }
  
  .user-challenge-photo-img {
      width: 75px;
      height: 54px;
  }
}

.user-challenge-photo-unsummited {
  opacity: .1;
}

.user-challenge-season-table {
  margin-bottom: 0px;
}

.user-challenge-season-table > thead > tr > th {
  background-color: #333;
  color: #fff;
}

.user-challenge-season-table > tbody > tr > td {
  vertical-align: middle;
}

.user-challenge-peak-name {
  color: #00b1f2;
  font-size: 11px;
  letter-spacing: .03125em;
  padding: 0px !important;
}

.season-peak-name {
  font-size: 11px;
  letter-spacing: .03125em;
}

.month-peak-name {
  font-size: 11px;
  letter-spacing: .03125em;
}

.user-challenge-peak-elevation {
  color: #a5a5a5;
  font-size: 11px;
  letter-spacing: .03125em;
  width: 130px !important;
}

@media screen and (min-width: 1px) and (max-width: 1023px) {
  .user-challenge-peak-season-on {
    color: #fff;
    background-color: #00b330;
    font-size: 11px;
    letter-spacing: .03125em;
    width: 100px !important;
    text-align: center;
  }
  
  .user-challenge-peak-season-off {
    width: 100px !important;
  }
}  

@media screen and (min-width: 1024px) {
  .user-challenge-peak-season-on {
    color: #fff;
    background-color: #00b330;
    font-size: 11px;
    letter-spacing: .03125em;
    width: 150px !important;
    text-align: center;
  }
  
  .user-challenge-peak-season-off {
    width: 150px !important;
  }
}  

.user-challenge-peak-month-on {
  color: #fff;
  background-color: #00b330;
  font-size: 11px;
  letter-spacing: .03125em;
  width: 70px;
  text-align: center;
}

.user-challenge-peak-month-off {
  width: 70px;
}

.unfollowButton {
  background-color: #00b330;
  border-color: #00b330;
}

.unfollowButtonHoverProfile {
  background-color: #f24100;
  border-color: #f24100;
}

#edit_profile_form .about_me {
    height: 240px;
}

.summit_badges li .thumb {
  border-top-left-radius: 75px;
  border-top-right-radius: 75px;
  box-shadow: none;
}

/***********************************/
/*          MEMBERS PAGE           */
/***********************************/

.member-filter-button, .badge-filter-button, .year-filter-button, .region-filter-button {
  font-size: 16px;
  color: #00B1F2;
  padding: 10px 5px;
  border-color: #fff;
  text-align: left;
  margin-left: 2px;
}

.member-filter-button:focus, .member-filter-button:active:focus, .member-filter-button.active:focus, .member-filter-button.focus, .member-filter-button:active.focus, .member-filter-button.active.focus {
  background-color: #fff;
  border-color: #fff;
  color: #00B1F2;
  outline: none;
}

.member-filter-button:hover {
  background-color: #fff;
  border-color: #fff;
  color: #33c9ff;
}

.route-filter-button {
  font-size: 16px;
  color: #00B1F2;
  padding: 10px 5px;
  border-color: #fff;
  text-align: left;
  margin-left: 2px;
}

.region-searchbox {
  float: right;
  height: 45px;
  left: 0px;
  font-size: 18px;
  line-height: 40px;
  padding-top: 1px;
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .region-searchbox {
    top: 13px;
  }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  .region-searchbox {
    top: 13px;
  }
}

@media screen and (min-width: 1024px) {
  .region-searchbox {
    top: 4px;
  }
}

.region-searchbox-input {
  padding-left: 5px;
}

.member-searchbox {
  float: right;
  height: 34px;
  top: 6px;
  left: 15px;
  font-size: 16px;
  line-height: 34px;
  padding-top: 0px;
}

.member-searchbox-input {
  padding-left: 10px;
  margin-right: 10px;
  width: 250px;
  color: #000;
}

#active-member-count {
  color: #F24100;
  font-weight: 500;
}

/***************************************/
/*          MEMBER HOME PAGE           */
/***************************************/

.bagger {
  white-space: nowrap;
}

@media screen and (min-width: 1024px) {
  .summits-filter-button {
    margin-top: 8px;
  }
  .member-filter-button {
    margin-top: 10px;
  }
  .year-filter-button {
    margin-top: 8px;
  }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  .summits-filter-button {
    margin-top: 11px;
  }
  .member-filter-button {
    margin-top: 11px;
  }
  .year-filter-button {
    margin-top: 11px;
  }  
}

@media screen and (min-width: 400px) and (max-width: 767px) {
  .summits-filter-button {
    margin-top: 15px;
  }
  .member-filter-button {
    margin-top: 15px;
  }
  .year-filter-button {
    margin-top: 15px;
  }  
}

@media screen and (min-width: 1px) and (max-width: 399px) {
  .summits-filter-button, .member-filter-button {
    margin-top: 15px;
    margin-left: 0px !important;
  }  
}

#region-title-data {
  display: inline-block;
  overflow: hidden;
}

#region-title {
  display: inline-block !important;
}

@media screen and (min-width: 768px) {
  #region-title:after {
    content: " ";
    display: inline-block;
    padding-left: 5px;
    vertical-align: middle;
    font-size: 14px;
    margin-bottom: 2px;
  }
  #region-filter-button {
    
  }
  #region-title {
    width: 100%;
  }
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  #region-title:after {
    content: " ";
    display: inline-block;
    padding-left: 5px;
    vertical-align: middle;
    font-size: 12px;
    margin-bottom: 3px;
  }
  #region-filter-button {
    width: 100%;
  }
  #region-title {
    width: 85%;
  }
}

.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
}
  
.summits-filter-button {
  font-size: 16px;
  color: #00B1F2;
  padding: 10px 5px;
  border-color: #fff;
  text-align: left;
  margin-left: 2px;
}

div#breadcrumbs ul li {
  font-size: 16px;
}  

.summits-filter-button:hover, .summits-filter-button:active, .summits-filter-button:focus, .summits-filter-button:visited {
  background-color: #fff;
  border-color: #fff;
}


.qq-upload-button-hover {
  background-color: #33c9ff !important;
}

.hover-photos {
  cursor: pointer;
  aspect-ratio:4/3;
}


.hover-photos:hover {
  -webkit-filter: brightness(1.1) saturate(1.5);
  -webkit-transition: opacity .25s ease-in-out;
}

.hover-photos-hover {
  -webkit-filter: brightness(1.1) saturate(1.5);
  -webkit-transition: opacity .25s ease-in-out;
}


.hover-minicard {
  cursor: pointer;
}

.hover-minicard:hover {
  -webkit-filter: brightness(1.1) saturate(1.5);
  -webkit-transition: opacity .25s ease-in-out;
}


.hover-minicard-hover {
  -webkit-filter: brightness(1.1) saturate(1.5);
  -webkit-transition: opacity .25s ease-in-out;
}

.hover-row:hover {
  -webkit-filter: brightness(1.1) saturate(1.5);
  -webkit-transition: opacity .25s ease-in-out;
}

.hover-row > div {
  background-color: #fff;
}

.hover-row:hover > div {
  background-color: #fde1d6;
}

.hover-cell {
  background-color: #fff;
}

.hover-cell:hover {
  background-color: #fde1d6;
  -webkit-filter: brightness(1.1) saturate(1.5);
  -webkit-transition: opacity .25s ease-in-out;
}

.table-hover-cell:hover {
  background-color: #fde1d6;
  -webkit-filter: brightness(1.1) saturate(1.5);
  -webkit-transition: opacity .25s ease-in-out;
}


.hero-photos {
  height: 480px;
}

@media screen and (min-width: 1280px) {
  .photo-caption-peakname {
    width: 45%;
  }
  .photo-caption-username {
    width: 45%;
  }
}

@media screen and (min-width: 1px) and (max-width: 1279px) {
  .photo-caption-peakname {
    width: 90%;
  }
  .photo-caption-username {
    width: 0%;
  }
}

@media screen and (min-width: 480px) {
  .hero-photo-caption-peakname {
    width: 45%;
  }
  .hero-photo-caption-username {
    width: 45%;
  }
}

@media screen and (min-width: 1px) and (max-width: 479px) {
  .hero-photo-caption-peakname {
    width: 90%;
  }
  .hero-photo-caption-username {
    width: 0%;
  }
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .photo-caption-peakname-only {
    width: 90%;
    font-size: 10px;
  }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  .photo-caption-peakname-only {
    width: 90%;
    font-size: 12px;
  }
}

@media screen and (min-width: 1024px) {
  .photo-caption-peakname-only {
    width: 90%;
    font-size: 14px;
  }
}



@media screen and (min-width: 768px) and (max-width: 1023px) {
  #see-worldwide {
    margin-left: 3px !important;
    margin-top: 1px;
  }
}  

@media screen and (min-width: 1px) and (max-width: 767px) {
  #see-worldwide {
    margin-left: 0px !important;
    margin-top: 0px;
  }
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  #sq1 {
    width: 97%;
    border: none;
  }
  #region-searchbox-icon {
    right: 30px;
    margin-top: -36px;
    display: block;
    position: absolute;
  }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  #sq1 {
    width: 98%;
  }
  #region-searchbox-icon {
    right: 30px;
    margin-top: -36px;
    display: block;
    position: absolute;
  }
}

@media screen and (min-width: 1px) and (max-width: 1023px) {
  #rq1, #mmq1 {
    width: 97%;
    border: none;
  }
}

@media screen and (min-width: 1024px) {
  #sq1, #rq1, #mmq1 {
    width: 98%;
  }
  #region-searchbox-icon {
    right: 30px;
    margin-top: -30px;
    display: block;
    position: absolute;
  }
}

#mobile-member-searchbox-icon {
  right: 30px;
  margin-top: -36px;
  display: block;
  position: absolute;
}

@media screen and (min-width: 1px) and (max-width: 767px) {
  .mobile-section-title {
    font-size: 14px;
    margin-top: 1px;
  }
}

.btn-see-full-log:hover {
  background-color: #fff8e4;
}

/***************************************/
/*          PEAK PHOTO STYLES          */
/***************************************/

@media (width >= 1280px) {
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
   aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 240px;
  }
  #slideshow1, #slideshow2 {
    aspect-ratio:4/3;
  }
  #home-slideshow {
    height: 640px;
  }
  .carousel-info {
    padding-top: 0px;
    height: 10%;
  }
  #home-slideshow img {
    width:1340px;
    height:700px;
    margin-left:-670px;
    margin-top:-350px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 640px !important;
    height: 480px !important;
    min-height: 480px !important;
    margin-left: -320px !important;
    margin-top: -240px !important;
  } 
}

@media (1226px <= width <= 1279px) {
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 235px;
  }
  #slideshow1, #slideshow2 {
    aspect-ratio:4/3;
  }
  #home-slideshow {
    height: 626px;
  }
  .carousel-info {
    padding-top: 0px;
    height: 10%;
  }
  #home-slideshow img {
    width:1340px;
    height:700px;
    margin-left:-670px;
    margin-top:-350px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 626px !important;
    height: 470px !important;
    min-height: 470px !important;
    margin-left: -313px !important;
    margin-top: -235px !important;
  }
}

@media (1173px <= width <= 1225px) {
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 225px;
  }
  #slideshow1, #slideshow2 {
    aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 600px;
  }
  .carousel-info {
    padding-top: 0px;
    height: 10%;
  }
  #home-slideshow img {
    width:1290px;
    height:675px;
    margin-left:-645px;
    margin-top:-337px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 600px !important;
    height: 450px !important;
    min-height: 450px !important;
    margin-left: -300px !important;
    margin-top: -225px !important;
  }
}

@media screen and (min-width: 1119px) and (max-width: 1172px) {
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
   aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 215px;
  }
  #slideshow1, #slideshow2 {
    aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 572px;
  }
  .carousel-info {
    padding-top: 0px;
    height: 10%;
  }
  #home-slideshow img {
    width:1290px;
    height:675px;
    margin-left:-645px;
    margin-top:-337px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 572px !important;
    height: 430px !important;
    min-height: 430px !important;
    margin-left: -286px !important;
    margin-top: -215px !important;
  }
}

@media screen and (min-width: 1066px) and (max-width: 1118px) {
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 205px;
  }
  #slideshow1, #slideshow2 {
    aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 550px;
  }
  .carousel-info {
    padding-top: 0px;
    height: 10%;
  }
  #home-slideshow img {
    width:1240px;
    height:650px;
    margin-left:-620px;
    margin-top:-325px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 550px !important;
    height: 410px !important;
    min-height: 410px !important;
    margin-left: -275px !important;
    margin-top: -205px !important;
  }
}

@media screen and (min-width: 1024px) and (max-width: 1065px) {
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
   aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 196px;
  }
  #slideshow1, #slideshow2 {
  aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 550px;
  }
  .carousel-info {
    padding-top: 0px;
    height: 10%;
  }
  #home-slideshow img {
    width:1240px;
    height:650px;
    margin-left:-620px;
    margin-top:-325px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 520px !important;
    height: 390px !important;
    min-height: 390px !important;
    margin-left: -260px !important;
    margin-top: -195px !important;
  }
}

@media screen and (min-width: 1013px) and (max-width: 1023px) {
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 255px;
  }
  #slideshow1, #slideshow2 {
   aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 550px;
  }
  .carousel-info {
    padding-top: 0px;
    height: 8%;
  }
  #home-slideshow img {
    width:1240px;
    height:650px;
    margin-left:-620px;
    margin-top:-325px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 1024px !important;
    height: 768px !important;
    min-height: 768px !important;
    margin-left: -512px !important;
    margin-top: -384px !important;
  } 
}

@media screen and (min-width: 992px) and (max-width: 1012px) {
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 250px;
  }
  #slideshow1, #slideshow2 {
  aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 550px;
  }
  .carousel-info {
    padding-top: 0px;
    height: 8%;
  }
  #home-slideshow img {
    width:1240px;
    height:650px;
    margin-left:-620px;
    margin-top:-325px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 1012px !important;
    height: 760px !important;
    min-height: 760px !important;
    margin-left: -506px !important;
    margin-top: -380px !important;
  } 
}

@media screen and (min-width: 960px) and (max-width: 991px) {
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 244px;
  }
  #slideshow1, #slideshow2 {
    aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 550px;
  }
  .carousel-info {
    padding-top: 0px;
    height: 8%;
  }
  #home-slideshow img {
    width:1240px;
    height:650px;
    margin-left:-620px;
    margin-top:-325px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 992px !important;
    height: 744px !important;
    min-height: 744px !important;
    margin-left: -496px !important;
    margin-top: -372px !important;
  } 
}

@media screen and (min-width: 906px) and (max-width: 959px) {
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    /* height: 233px; */
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 233px;
  }
  #slideshow1, #slideshow2 {
    aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 550px;
  }
  .carousel-info {
    padding-top: 0px;
    height: 8%;
  }
  #home-slideshow img {
    width:1240px;
    height:650px;
    margin-left:-620px;
    margin-top:-325px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 960px !important;
    height: 720px !important;
    min-height: 720px !important;
    margin-left: -480px !important;
    margin-top: -360px !important;
  } 
}

@media screen and (min-width: 853px) and (max-width: 905px) {
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 220px;
  }
  #slideshow1, #slideshow2 {
    aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 550px;
  }
  .carousel-info {
    padding-top: 0px;
    height: 9%;
  }
  #home-slideshow img {
    width:1240px;
    height:650px;
    margin-left:-620px;
    margin-top:-325px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 906px !important;
    height: 682px !important;
    min-height: 682px !important;
    margin-left: -453px !important;
    margin-top: -341px !important;
  } 
}

@media screen and (min-width: 800px) and (max-width: 852px) {
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 207px;
  }
  #slideshow1, #slideshow2 {
   aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 550px;
  }
  .carousel-info {
    padding-top: 0px;
    height: 9%;
  }
  #home-slideshow img {
    width:1240px;
    height:650px;
    margin-left:-620px;
    margin-top:-325px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 852px !important;
    height: 640px !important;
    min-height: 640px !important;
    margin-left: -426px !important;
    margin-top: -320px !important;
  } 
}

@media screen and (min-width: 768px) and (max-width: 799px) {
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 196px;
  }
  #slideshow1, #slideshow2 {
   aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 550px;
  }
  .carousel-info {
    padding-top: 0px;
    height: 10%;
  }
  #home-slideshow img {
    width:1240px;
    height:650px;
    margin-left:-620px;
    margin-top:-325px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 800px !important;
    height: 602px !important;
    min-height: 602px !important;
    margin-left: -400px !important;
    margin-top: -301px !important;
  } 
}

@media screen and (min-width: 747px) and (max-width: 767px) {
  /***  1/2: 379  1/3: 252  1/4: 189  Midpoint: 757  ***/
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 284px;
  }
  .leftthird-responsive {
    height: 189px;
  }
  #slideshow1, #slideshow2 {
   aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 379px;
  }
  .carousel-info {
    padding-top: 5px;
    height: 7%;
  }
  #home-slideshow img {
    width:990px;
    height:518px;
    margin-left:-495px;
    margin-top:-259px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 768px !important;
    height: 576px !important;
    min-height: 576px !important;
    margin-left: -384px !important;
    margin-top: -288px !important;
  } 
}

@media screen and (min-width: 693px) and (max-width: 746px) {
  /***  1/2: 360  1/3: 240  1/4: 180  Midpoint: 720  ***/
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 270px;
  }
  .leftthird-responsive {
    height: 180px;
  }
  #slideshow1, #slideshow2 {
    aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 360px;
  }
  .carousel-info {
    padding-top: 5px;
    height: 8%;
  }
  #home-slideshow img {
    width:890px;
    height:466px;
    margin-left:-445px;
    margin-top:-233px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 746px !important;
    height: 560px !important;
    min-height: 560px !important;
    margin-left: -373px !important;
    margin-top: -280px !important;
  } 
}

@media screen and (min-width: 640px) and (max-width: 692px) {
  /***  1/2: 333  1/3: 222  1/4: 167  Midpoint: 666  ***/
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 250px;
  }
  #slideshow1, #slideshow2 {
    aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 350px;
  }
  .carousel-info {
    padding-top: 5px;
    height: 9%;
  }
  #home-slideshow img {
    width:890px;
    height:466px;
    margin-left:-445px;
    margin-top:-233px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 692px !important;
    height: 520px !important;
    min-height: 520px !important;
    margin-left: -346px !important;
    margin-top: -260px !important;
  } 
}

@media screen and (min-width: 587px) and (max-width: 639px) {
  /***  1/2: 307  1/3: 204  1/4: 153  Midpoint: 613  ***/
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 230px;
  }
  .leftthird-responsive {
    height: 153px;
  }
  #slideshow1, #slideshow2 {
   aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 350px;
  }
  .carousel-info {
    padding-top: 5px;
    height: 9%;
  }
  #home-slideshow img {
    width:790px;
    height:414px;
    margin-left:-395px;
    margin-top:-212px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 640px !important;
    height: 482px !important;
    min-height: 482px !important;
    margin-left: -320px !important;
    margin-top: -241px !important;
  } 
}

@media screen and (min-width: 533px) and (max-width: 586px) {
  /***  1/2: 280  1/3: 187  1/4: 140  Midpoint: 560  ***/
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 210px;
  }
  .leftthird-responsive {
    height: 141px;
  }
  #slideshow1, #slideshow2 {
   aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 350px;
  }
  .carousel-info {
    padding-top: 5px;
    height: 10%;
  }
  #home-slideshow img {
    width:790px;
    height:414px;
    margin-left:-395px;
    margin-top:-212px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 586px !important;
    height: 440px !important;
    min-height: 440px !important;
    margin-left: -293px !important;
    margin-top: -220px !important;
  } 
}

@media screen and (min-width: 480px) and (max-width: 532px) {
  /***  1/2: 253  1/3: 169  1/4: 127  Midpoint: 506  ***/
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 190px;
  }
  .leftthird-responsive {
    height: 127px;
  }
  #slideshow1, #slideshow2 {
   aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 350px;
  }
  .carousel-info {
    padding-top: 5px;
    height: 11%;
  }
  #home-slideshow img {
    width:690px;
    height:360px;
    margin-left:-345px;
    margin-top:-180px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 532px !important;
    height: 400px !important;
    min-height: 400px !important;
    margin-left: -266px !important;
    margin-top: -200px !important;
  } 
}

@media screen and (min-width: 427px) and (max-width: 479px) {
  /***  1/2: 227  1/3: 151  1/4: 113  Midpoint: 453  ***/
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 170px;
  }
  .leftthird-responsive {
    height: 114px;
  }
  #slideshow1, #slideshow2 {
    aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 350px;
  }
  .carousel-info {
    padding-top: 5px;
    height: 10%;
  }
  #home-slideshow img {
    width:690px;
    height:360px;
    margin-left:-345px;
    margin-top:-180px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 480px !important;
    height: 360px !important;
    min-height: 360px !important;
    margin-left: -240px !important;
    margin-top: -180px !important;
  } 
}

@media screen and (min-width: 373px) and (max-width: 426px) {
  /***  1/2: 200  1/3: 133  1/4: 100  Midpoint: 400  ***/
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 150px;
  }
  .leftthird-responsive {
    height: 100px;
  }
  #slideshow1, #slideshow2 {
    aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 350px;
  }
  .carousel-info {
    padding-top: 5px;
    height: 10%;
  }
  #home-slideshow img {
    width:690px;
    height:360px;
    margin-left:-345px;
    margin-top:-180px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 426px !important;
    height: 320px !important;
    min-height: 320px !important;
    margin-left: -213px !important;
    margin-top: -160px !important;
  } 
}

@media screen and (min-width: 320px) and (max-width: 372px) {
  /***  1/2: 173  1/3: 115  1/4: 87  Midpoint: 346  ***/
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 130px;
  }
  .leftthird-responsive {
    height: 86px;
  }
  #slideshow1, #slideshow2 {
    aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 350px;
  }
  .carousel-info {
    padding-top: 5px;
    height: 10%;
  }
  #home-slideshow img {
    width:690px;
    height:360px;
    margin-left:-345px;
    margin-top:-180px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 372px !important;
    height: 280px !important;
    min-height: 280px !important;
    margin-left: -186px !important;
    margin-top: -140px !important;
  } 
}

@media screen and (min-width: 1px) and (max-width: 319px) {
  /***  1/2: 160  1/3: 107  1/4: 80  Midpoint: 319  ***/
  .peakimg-responsive, .qq-upload-button, #slideshow3, #slideshow4, #slideshow5, #slideshow6, #slideshow7, #slideshow8, #slideshow9, #slideshow10, #slideshow11 {
    aspect-ratio: 4/3;
  }
  .peakimgdata-responsive {
    min-height: 120px;
  }
  .leftthird-responsive {
    height: 80px;
  }
  #slideshow1, #slideshow2 {
    aspect-ratio: 4/3;
  }
  #home-slideshow {
    height: 350px;
  }
  .carousel-info {
    padding-top: 5px;
    height: 10%;
  }
  #home-slideshow img {
    width:690px;
    height:360px;
    margin-left:-345px;
    margin-top:-180px;
  }
  .challenge-hero-photo {
    transform: none !important;
    width: 320px !important;
    height: 240px !important;
    min-height: 240px !important;
    margin-left: -160px !important;
    margin-top: -120px !important;
  } 
}

/***************************************/
/*         GUEST HOME COLLAGE          */
/***************************************/

/* slideshow1 */
#home-slideshow {
    position:relative;
    overflow:hidden;
}

#home-slideshow img {
    position:absolute;
    top:50%;
    left:50%;
    opacity:0;
    -webkit-transition-property: opacity, -webkit-transform;
    -webkit-transition-duration: 3s, 10s;
       -moz-transition-property: opacity, -moz-transform;
       -moz-transition-duration: 3s, 10s;
        -ms-transition-property: opacity, -ms-transform;
        -ms-transition-duration: 3s, 10s;
         -o-transition-property: opacity, -o-transform;
         -o-transition-duration: 3s, 10s;
            transition-property: opacity, transform;
            transition-duration: 3s, 10s;
}

#home-slideshow img  {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}

#home-slideshow :nth-child(2n+1) {
    -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#home-slideshow :nth-child(3n+1) {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}
#home-slideshow :nth-child(4n+1) {
  -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#home-slideshow .fx:first-child + img ~ img  {
    z-index:-1;
}

#home-slideshow .fx {
    opacity:1;
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}

/***************************************/
/*         TOP PHOTOS COLLAGE          */
/***************************************/

/* slideshow1 */
#slideshow1 {
    overflow:hidden;
    border-bottom-left-radius:12px;
}

.challenge-slideshow {
    border-bottom-left-radius: 0px !important;
}

@media screen and (min-width: 1px) and (max-width: 319px) {

  #slideshow1 img {
      position:absolute;
      width:570px;
      min-height:380px;
      top:50%;
      left:50%;
      margin-left:-285px;
      margin-top:-190px;
      opacity:0;
      -webkit-transition-property: opacity, -webkit-transform;
      -webkit-transition-duration: 3s, 10s;
         -moz-transition-property: opacity, -moz-transform;
         -moz-transition-duration: 3s, 10s;
          -ms-transition-property: opacity, -ms-transform;
          -ms-transition-duration: 3s, 10s;
           -o-transition-property: opacity, -o-transform;
           -o-transition-duration: 3s, 10s;
              transition-property: opacity, transform;
              transition-duration: 3s, 10s;
  }
  
}

@media screen and (min-width: 320px) and (max-width: 768px) {
  
  #slideshow1 img {
      position:absolute;
      width:768px;
      min-height:480px;
      top:50%;
      left:50%;
      margin-left:-382px;
      margin-top:-240px;
      opacity:0;
      -webkit-transition-property: opacity, -webkit-transform;
      -webkit-transition-duration: 3s, 10s;
         -moz-transition-property: opacity, -moz-transform;
         -moz-transition-duration: 3s, 10s;
          -ms-transition-property: opacity, -ms-transform;
          -ms-transition-duration: 3s, 10s;
           -o-transition-property: opacity, -o-transform;
           -o-transition-duration: 3s, 10s;
              transition-property: opacity, transform;
              transition-duration: 3s, 10s;
  }
  
}

@media screen and (min-width: 769px) {
  
  #slideshow1 img {
      position:absolute;
      width:1020px;
      min-height:680px;
      top:50%;
      left:50%;
      margin-left:-510px;
      margin-top:-340px;
      opacity:0;
      -webkit-transition-property: opacity, -webkit-transform;
      -webkit-transition-duration: 3s, 10s;
         -moz-transition-property: opacity, -moz-transform;
         -moz-transition-duration: 3s, 10s;
          -ms-transition-property: opacity, -ms-transform;
          -ms-transition-duration: 3s, 10s;
           -o-transition-property: opacity, -o-transform;
           -o-transition-duration: 3s, 10s;
              transition-property: opacity, transform;
              transition-duration: 3s, 10s;
  }
  
}


#slideshow1 img  {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}

#slideshow1 :nth-child(2n+1) {
    -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow1 :nth-child(3n+1) {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}
#slideshow1 :nth-child(4n+1) {
  -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow1 .fx:first-child + img ~ img  {
    z-index:-1;
}

#slideshow1 .fx {
    opacity:1;
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}

/* slideshow2 */
#slideshow2 {
    position:relative;
    overflow:hidden;
    border-bottom-right-radius:12px;
}

@media screen and (min-width: 1px) and (max-width: 319px) {

  #slideshow2 img {
      position:absolute;
      width:570px;
      min-height:380px;
      top:50%;
      left:50%;
      margin-left:-285px;
      margin-top:-190px;
      opacity:0;
      -webkit-transition-property: opacity, -webkit-transform;
      -webkit-transition-duration: 3s, 10s;
         -moz-transition-property: opacity, -moz-transform;
         -moz-transition-duration: 3s, 10s;
          -ms-transition-property: opacity, -ms-transform;
          -ms-transition-duration: 3s, 10s;
           -o-transition-property: opacity, -o-transform;
           -o-transition-duration: 3s, 10s;
              transition-property: opacity, transform;
              transition-duration: 3s, 10s;
  }
  
}

@media screen and (min-width: 320px) and (max-width: 768px) {
  
  #slideshow2 img {
      position:absolute;
      width:768px;
      min-height:480px;
      top:50%;
      left:50%;
      margin-left:-382px;
      margin-top:-240px;
      opacity:0;
      -webkit-transition-property: opacity, -webkit-transform;
      -webkit-transition-duration: 3s, 10s;
         -moz-transition-property: opacity, -moz-transform;
         -moz-transition-duration: 3s, 10s;
          -ms-transition-property: opacity, -ms-transform;
          -ms-transition-duration: 3s, 10s;
           -o-transition-property: opacity, -o-transform;
           -o-transition-duration: 3s, 10s;
              transition-property: opacity, transform;
              transition-duration: 3s, 10s;
  }
  
}

@media screen and (min-width: 769px) {
  
  #slideshow2 img {
      position:absolute;
      width:1020px;
      min-height:680px;
      top:50%;
      left:50%;
      margin-left:-510px;
      margin-top:-340px;
      opacity:0;
      -webkit-transition-property: opacity, -webkit-transform;
      -webkit-transition-duration: 3s, 10s;
         -moz-transition-property: opacity, -moz-transform;
         -moz-transition-duration: 3s, 10s;
          -ms-transition-property: opacity, -ms-transform;
          -ms-transition-duration: 3s, 10s;
           -o-transition-property: opacity, -o-transform;
           -o-transition-duration: 3s, 10s;
              transition-property: opacity, transform;
              transition-duration: 3s, 10s;
  }
  
}

#slideshow2 img  {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}

#slideshow2 :nth-child(2n+1) {
    -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow2 :nth-child(3n+1) {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}
#slideshow2 :nth-child(4n+1) {
  -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow2 .fx:first-child + img ~ img  {
    z-index:-1;
}

#slideshow2 .fx {
    opacity:1;
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}

/* slideshow3 */
#slideshow3 {
    position:relative;
    /*width:800px;*/
    overflow:hidden;
}

#slideshow3 img {
    position:absolute;
    /*width:480px;
    min-height:320px;
    top:50%;
    left:50%;
    margin-left:-240px;*/
    width:380px;
    min-height:285px;
    top:50%;
    left:50%;
    margin-left:-190px;
    opacity:0;
    -webkit-transition-property: opacity, -webkit-transform;
    -webkit-transition-duration: 3s, 10s;
       -moz-transition-property: opacity, -moz-transform;
       -moz-transition-duration: 3s, 10s;
        -ms-transition-property: opacity, -ms-transform;
        -ms-transition-duration: 3s, 10s;
         -o-transition-property: opacity, -o-transform;
         -o-transition-duration: 3s, 10s;
            transition-property: opacity, transform;
            transition-duration: 3s, 10s;
}

#slideshow3 img  {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}

#slideshow3 :nth-child(2n+1) {
    -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow3 :nth-child(3n+1) {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}
#slideshow3 :nth-child(4n+1) {
  -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow3 .fx:first-child + img ~ img  {
    z-index:-1;
}

#slideshow3 .fx {
    opacity:1;
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}

/* slideshow4 */
#slideshow4 {
    position:relative;
    /*width:800px;*/
    overflow:hidden;
}

#slideshow4 img {
    position:absolute;
    /*width:480px;
    min-height:320px;
    top:50%;
    left:50%;
    margin-left:-240px;*/
    width:380px;
    min-height:285px;
    top:50%;
    left:50%;
    margin-left:-190px;
    opacity:0;
    -webkit-transition-property: opacity, -webkit-transform;
    -webkit-transition-duration: 3s, 10s;
       -moz-transition-property: opacity, -moz-transform;
       -moz-transition-duration: 3s, 10s;
        -ms-transition-property: opacity, -ms-transform;
        -ms-transition-duration: 3s, 10s;
         -o-transition-property: opacity, -o-transform;
         -o-transition-duration: 3s, 10s;
            transition-property: opacity, transform;
            transition-duration: 3s, 10s;
}

#slideshow4 img  {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}

#slideshow4 :nth-child(2n+1) {
    -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow4 :nth-child(3n+1) {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}
#slideshow4 :nth-child(4n+1) {
  -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow4 .fx:first-child + img ~ img  {
    z-index:-1;
}

#slideshow4 .fx {
    opacity:1;
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}

/* slideshow5 */
#slideshow5 {
    position:relative;
    /*width:800px;*/
    overflow:hidden;
}

#slideshow5 img {
    position:absolute;
    /*width:480px;
    min-height:320px;
    top:50%;
    left:50%;
    margin-left:-240px;*/
    width:380px;
    min-height:285px;
    top:50%;
    left:50%;
    margin-left:-190px;
    opacity:0;
    -webkit-transition-property: opacity, -webkit-transform;
    -webkit-transition-duration: 3s, 10s;
       -moz-transition-property: opacity, -moz-transform;
       -moz-transition-duration: 3s, 10s;
        -ms-transition-property: opacity, -ms-transform;
        -ms-transition-duration: 3s, 10s;
         -o-transition-property: opacity, -o-transform;
         -o-transition-duration: 3s, 10s;
            transition-property: opacity, transform;
            transition-duration: 3s, 10s;
}

#slideshow5 img  {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}

#slideshow5 :nth-child(2n+1) {
    -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow5 :nth-child(3n+1) {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}
#slideshow5 :nth-child(4n+1) {
  -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow5 .fx:first-child + img ~ img  {
    z-index:-1;
}

#slideshow5 .fx {
    opacity:1;
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}

/* slideshow6 */
#slideshow6 {
    position:relative;
    /*width:800px;*/
    overflow:hidden;
}

#slideshow6 img {
    position:absolute;
    /*width:480px;
    min-height:320px;
    top:50%;
    left:50%;
    margin-left:-240px;*/
    width:380px;
    min-height:285px;
    top:50%;
    left:50%;
    margin-left:-190px;
    opacity:0;
    -webkit-transition-property: opacity, -webkit-transform;
    -webkit-transition-duration: 3s, 10s;
       -moz-transition-property: opacity, -moz-transform;
       -moz-transition-duration: 3s, 10s;
        -ms-transition-property: opacity, -ms-transform;
        -ms-transition-duration: 3s, 10s;
         -o-transition-property: opacity, -o-transform;
         -o-transition-duration: 3s, 10s;
            transition-property: opacity, transform;
            transition-duration: 3s, 10s;
}

#slideshow6 img  {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}

#slideshow6 :nth-child(2n+1) {
    -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow6 :nth-child(3n+1) {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}
#slideshow6 :nth-child(4n+1) {
  -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow6 .fx:first-child + img ~ img  {
    z-index:-1;
}

#slideshow6 .fx {
    opacity:1;
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}

/* slideshow7 */
#slideshow7 {
    position:relative;
    /*width:800px;*/
    overflow:hidden;
}

#slideshow7 img {
    position:absolute;
    /*width:480px;
    min-height:320px;
    top:50%;
    left:50%;
    margin-left:-240px;*/
    width:380px;
    min-height:285px;
    top:50%;
    left:50%;
    margin-left:-190px;
    opacity:0;
    -webkit-transition-property: opacity, -webkit-transform;
    -webkit-transition-duration: 3s, 10s;
       -moz-transition-property: opacity, -moz-transform;
       -moz-transition-duration: 3s, 10s;
        -ms-transition-property: opacity, -ms-transform;
        -ms-transition-duration: 3s, 10s;
         -o-transition-property: opacity, -o-transform;
         -o-transition-duration: 3s, 10s;
            transition-property: opacity, transform;
            transition-duration: 3s, 10s;
}

#slideshow7 img  {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}

#slideshow7 :nth-child(2n+1) {
    -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow7 :nth-child(3n+1) {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}
#slideshow7 :nth-child(4n+1) {
  -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow7 .fx:first-child + img ~ img  {
    z-index:-1;
}

#slideshow7 .fx {
    opacity:1;
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}

/* slideshow8 */
#slideshow8 {
    position:relative;
    /*width:800px;*/
    overflow:hidden;
}

#slideshow8 img {
    position:absolute;
    /*width:480px;
    min-height:320px;
    top:50%;
    left:50%;
    margin-left:-240px;*/
    width:380px;
    min-height:285px;
    top:50%;
    left:50%;
    margin-left:-190px;
    opacity:0;
    -webkit-transition-property: opacity, -webkit-transform;
    -webkit-transition-duration: 3s, 10s;
       -moz-transition-property: opacity, -moz-transform;
       -moz-transition-duration: 3s, 10s;
        -ms-transition-property: opacity, -ms-transform;
        -ms-transition-duration: 3s, 10s;
         -o-transition-property: opacity, -o-transform;
         -o-transition-duration: 3s, 10s;
            transition-property: opacity, transform;
            transition-duration: 3s, 10s;
}

#slideshow8 img  {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}

#slideshow8 :nth-child(2n+1) {
    -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow8 :nth-child(3n+1) {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}
#slideshow8 :nth-child(4n+1) {
  -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow8 .fx:first-child + img ~ img  {
    z-index:-1;
}

#slideshow8 .fx {
    opacity:1;
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}

/* slideshow9 */
#slideshow9 {
    position:relative;
    /*width:800px;*/
    overflow:hidden;
}

#slideshow9 img {
    position:absolute;
    /*width:480px;
    min-height:320px;
    top:50%;
    left:50%;
    margin-left:-240px;*/
    width:380px;
    min-height:285px;
    top:50%;
    left:50%;
    margin-left:-190px;
    opacity:0;
    -webkit-transition-property: opacity, -webkit-transform;
    -webkit-transition-duration: 3s, 10s;
       -moz-transition-property: opacity, -moz-transform;
       -moz-transition-duration: 3s, 10s;
        -ms-transition-property: opacity, -ms-transform;
        -ms-transition-duration: 3s, 10s;
         -o-transition-property: opacity, -o-transform;
         -o-transition-duration: 3s, 10s;
            transition-property: opacity, transform;
            transition-duration: 3s, 10s;
}

#slideshow9 img  {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}

#slideshow9 :nth-child(2n+1) {
    -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow9 :nth-child(3n+1) {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}
#slideshow9 :nth-child(4n+1) {
  -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow9 .fx:first-child + img ~ img  {
    z-index:-1;
}

#slideshow9 .fx {
    opacity:1;
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}

/* slideshow10 */
#slideshow10 {
    position:relative;
    /*width:800px;*/
    overflow:hidden;
}

#slideshow10 img {
    position:absolute;
    /*width:480px;
    min-height:320px;
    top:50%;
    left:50%;
    margin-left:-240px;*/
    width:380px;
    min-height:285px;
    top:50%;
    left:50%;
    margin-left:-190px;
    opacity:0;
    -webkit-transition-property: opacity, -webkit-transform;
    -webkit-transition-duration: 3s, 10s;
       -moz-transition-property: opacity, -moz-transform;
       -moz-transition-duration: 3s, 10s;
        -ms-transition-property: opacity, -ms-transform;
        -ms-transition-duration: 3s, 10s;
         -o-transition-property: opacity, -o-transform;
         -o-transition-duration: 3s, 10s;
            transition-property: opacity, transform;
            transition-duration: 3s, 10s;
}

#slideshow10 img  {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}

#slideshow10 :nth-child(2n+1) {
    -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow10 :nth-child(3n+1) {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}
#slideshow10 :nth-child(4n+1) {
  -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow10 .fx:first-child + img ~ img  {
    z-index:-1;
}

#slideshow10 .fx {
    opacity:1;
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}

/* slideshow11 */
#slideshow11 {
    position:relative;
    /*width:800px;*/
    overflow:hidden;
}

#slideshow11 img {
    position:absolute;
    /*width:480px;
    min-height:320px;
    top:50%;
    left:50%;
    margin-left:-240px;*/
    width:380px;
    min-height:285px;
    top:50%;
    left:50%;
    margin-left:-190px;
    opacity:0;
    -webkit-transition-property: opacity, -webkit-transform;
    -webkit-transition-duration: 3s, 10s;
       -moz-transition-property: opacity, -moz-transform;
       -moz-transition-duration: 3s, 10s;
        -ms-transition-property: opacity, -ms-transform;
        -ms-transition-duration: 3s, 10s;
         -o-transition-property: opacity, -o-transform;
         -o-transition-duration: 3s, 10s;
            transition-property: opacity, transform;
            transition-duration: 3s, 10s;
}

#slideshow11 img  {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}

#slideshow11 :nth-child(2n+1) {
    -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow11 :nth-child(3n+1) {
    -webkit-transform-origin: top left;
       -moz-transform-origin: top left;
        -ms-transform-origin: top left;
         -o-transform-origin: top left;
            transform-origin: top left;
}
#slideshow11 :nth-child(4n+1) {
  -webkit-transform-origin: top right;
     -moz-transform-origin: top right;
      -ms-transform-origin: top right;
       -o-transform-origin: top right;
          transform-origin: top right;
}

#slideshow11 .fx:first-child + img ~ img  {
    z-index:-1;
}

#slideshow11 .fx {
    opacity:1;
    -webkit-transform: scale(1.1);
       -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
}

@media screen and (min-width: 1024px) {

  #slideshow3 img {
    margin-top:-120px;
  }
  #slideshow4 img {
    margin-top:-120px;
  }
  #slideshow5 img {
    margin-top:-120px;
  }
  #slideshow6 img {
    margin-top:-120px;
  }
  #slideshow7 img {
    margin-top:-120px;
  }
  #slideshow8 img {
    margin-top:-120px;
  }
  #slideshow9 img {
    margin-top:-120px;
  }
  #slideshow10 img {
    margin-top:-120px;
  }
  #slideshow11 img {
    margin-top:-120px;
  }
  
}

@media screen and (min-width: 768px) and (max-width: 1023px) {

  #slideshow3 img {
    margin-top:-130px;
  }
  #slideshow4 img {
    margin-top:-130px;
  }
  #slideshow5 img {
    margin-top:-130px;
  }
  #slideshow6 img {
    margin-top:-130px;
  }
  #slideshow7 img {
    margin-top:-130px;
  }
  #slideshow8 img {
    margin-top:-130px;
  }
  #slideshow9 img {
    margin-top:-130px;
  }
  #slideshow10 img {
    margin-top:-130px;
  }
  #slideshow11 img {
    margin-top:-130px;
  }
  
}

@media screen and (min-width: 480px) and (max-width: 767px) {

  #slideshow3 img {
    margin-top:-145px;
  }
  #slideshow4 img {
    margin-top:-145px;
  }
  #slideshow5 img {
    margin-top:-145px;
  }
  #slideshow6 img {
    margin-top:-145px;
  }
  #slideshow7 img {
    margin-top:-145px;
  }
  #slideshow8 img {
    margin-top:-145px;
  }
  #slideshow9 img {
    margin-top:-145px;
  }
  #slideshow10 img {
    margin-top:-145px;
  }
  #slideshow11 img {
    margin-top:-145px;
  }
  
}

@media screen and (min-width: 1px) and (max-width: 479px) {

  #slideshow3 img {
    margin-top:-130px;
  }
  #slideshow4 img {
    margin-top:-130px;
  }
  #slideshow5 img {
    margin-top:-130px;
  }
  #slideshow6 img {
    margin-top:-130px;
  }
  #slideshow7 img {
    margin-top:-130px;
  }
  #slideshow8 img {
    margin-top:-130px;
  }
  #slideshow9 img {
    margin-top:-130px;
  }
  #slideshow10 img {
    margin-top:-130px;
  }
  #slideshow11 img {
    margin-top:-130px;
  }
  
}

body.drawer-open {
    overflow: hidden;
}

#content-body {
    margin-top: 70px;
}

.fixed-page-header {
    position: absolute;
    top: 50px;
    width: 100%;
    max-width: 100%;
    margin-left: -15px;
    margin-right: 0px;
}

.main-header-row {
    position: absolute;
    top: 50px;
    width: 100%;
    max-width: 100%;
    margin-left: -15px;
    margin-right: 0px;
}


input[type=text], input[type=search], input[type=url], input[type=email], input[type=password], .btn, textarea {
    -webkit-border-radius: 12px;
    border-radius: 12px;
}


{% if IS_MOBILE_APP_ACCESS == 'True' %}
            .navbar-inverse, .dark-background-row, #main {background-image: none; background-color: #333;}
    {% if MOBILE_APP_BACK == 'True' %}
            .mobile-logo-title {left: 50px;}
            {% else %}
            .mobile-logo-title {left: 10px;}
    {% endif %}
            {% else %}
            .mobile-logo-title {left: 50px;}
{% endif %}


            #main, .dark-background-row, #content-holder {
                background-image: none !important;
                background-color: #000 !important;
            }




            body {
                background-image: none !important;
                background-color: #000 !important;
            }


.modal-content {
    border-radius:12px;
}

.modal-body {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    background-color: initial;
    background-image: linear-gradient(180deg, #DDDDDD 0%, #F0F0F0 100%);
}

.modal-title {
    text-align: center;
}

.modal-close {
    line-height: 40px;
}

.signUpWithEmail form span.a {
    background: initial;
}

.signUpWithEmail form span.a input {
    background: none !important;
    background: #fff !important;
    padding-left: 40px;
    padding-right: 40px;
    border-radius: 12px;
}

.signUpWithEmail form .btnWrapper input.btn {
    font-size: 18px;
    width: 285px;
}

.signUpWithEmail form p.terms {
    color: #777;
    font-size: 14px;
}

.accounts-sign-up-modal > .modal-dialog > .modal-content > .modal-body {
    background-image: linear-gradient(180deg, #DDDDDD 52%, #F0F0F0 100%);
}




/* mobile cards */

            @media (767px >= width) {
            
                body {
                    letter-spacing: 0px !important;
                }
                .map-card-mobile-0 {
                    aspect-ratio:8/3 !important
                }
                .map-card-mobile-1 {
                    aspect-ratio:4/3 !important
                }
                .map-card-mobile-2 {
                        aspect-ratio:8/3;
                        display:block !important
                }
                .map-card-mobile-3 {
                    aspect-ratio:4/3 !important
                }
                .map-card-mobile-4 {
                    aspect-ratio:8/3 !important
                }
                .card-mobile-aspect-ratio-12-3 {
                    aspect-ratio:12/3 !important
                }
                .card-mobile-aspect-ratio-12-4 {
                    aspect-ratio:12/4 !important
                }
                #region-searchbox-text {
                    font-size: 18px;
                }
            }

/* tablet cards */
            @media (768px <= width <= 1023px)  {

                .map-card-tablet-0 {
                    aspect-ratio:12/3 !important
                }
                .map-card-tablet-1 {
                    aspect-ratio:8/3 !important
                }
                .map-card-tablet-2 {
                    aspect-ratio:4/3 !important
                }
                .map-card-tablet-3 {
                    aspect-ratio:12/3 !important;
                    display:block !important;
                    width:100% !important
                }
                .map-card-tablet-4 {
                    aspect-ratio:12/3 !important
                }
                #region-searchbox-text {
                    font-size: 18px;
                }
            }

/* desktop cards */
            @media (1024px <= width) {
                .container, .subnav, .main-header-row, .sub-header-row {
                    max-width: calc(100vw - 40px)
                }
                .regions-subnav-fixed {
                    width: 100%
                }
                .peakinfoimg-responsive {
                  aspect-ratio: 4/3
                }
                #slideshow1, #slideshow2 {
                   aspect-ratio:4/3;
                   position:static;
                }

                #slideshow1 {
                    border-bottom-left-radius: 12px;
                }

                 #slideshow2 {
                    border-bottom-right-radius: 12px;
                }

                .challenge-slideshow {
                     border-bottom-left-radius: 0px !important;
                }

                #home-slideshow {
                    height: 940px;
                }
                #home-slideshow img {
                    width:1940px;
                    height:1016px;
                    margin-left:-970px;
                    margin-top:-508px;
                }
                .challenge-hero-photo {
                    transform: none !important;
                    width: 940px !important;
                    height: 707px !important;
                    min-height: 707px !important;
                    margin-left: -470px !important;
                    margin-top: -353px !important;
                }
                .map-card-web-0 {
                    aspect-ratio:16/3 !important
                }
                .map-card-web-1 {
                    aspect-ratio:12/3 !important
                }
                .map-card-web-2 {
                    aspect-ratio:8/3 !important
                }
                .map-card-web-3 {
                    aspect-ratio:4/3 !important
                }
                .map-card-web-4 {
                    aspect-ratio:16/3 !important
                }
                .photo-card-web-3 {
                    aspect-ratio:4/3 !important
                }
                .box-blc-web {
                    border-bottom-left-radius:8px !important
                }
                .box-brc-web {
                    border-bottom-right-radius:8px !important
                }
                #region-pushdown {
                    padding-top:2px;
                }
            }




/*************************** NAVBAR - note different break points than rest of site, refactor? seems like a ton of duplicate code here, keep cleaning up sep 17 ******************/


/***************/
/* DESKTOP NAV */
/***************/

    /* general nav appearance */






    #navbar {
        cursor: default !important;
        font-weight: 400;
        font-size: 14px;
        color: #666;
        height: 50px !important;
        min-height: 100%;
    }


    .navbar {
         border: none !important;
    }

    .navbar-fixed-top {
     height: 50px;
    }

    .nav-hidden {
        display: none !important;
    }

    #navbar:hover, .navbar-header:hover {
        cursor: pointer;
    }

    .fa-icon-rotate {
        /* FF3.5+ */
        -moz-transform: rotate(-90.0deg);
        /* Opera 10.5 */
        -o-transform: rotate(-90.0deg);
        /* Saf3.1+, Chrome */
        -webkit-transform: rotate(-90.0deg);
        /* IE8 */
        -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0.083)";
        /* Standard */
        transform: rotate(-90.0deg);
    }


    #navbar a:hover {
        text-decoration: none;
    }

    .navbar-li-link {
        cursor: pointer;
    }

    .navbar-link-hover {
        color: #fff !important;
    }


    .nav-justified > li {
        width: auto;
    }
    .nav-justified > li > a {
        color: #ccc;
        letter-spacing: 0.5px;
        text-align: center;
    }

    .navbar-inverse {
        border: none;
        background-color: #000;
    }

    .navbar-inverse .navbar-nav > li > a {
        color: #707070;
    }

    .navbar-inverse .navbar-nav > li > a:hover {
        color: #ffffff;
    }

    .nav > li > a:hover, .nav > li > a:focus {
        text-decoration: none;
        background-color: transparent;
    }

    .navbar-fixed-top .navbar-collapse, .navbar-fixed-bottom .navbar-collapse {
        max-height: 430px;
    }

    .nav .open > a, .nav .open > a:hover, .nav .open > a:focus {
        background-color: transparent;
    }

    .navbar-secondary {
        color: #F24100 !important;
    }

    .navbar-secondary:hover {
        color: #ffffff !important;
    }

    .navbar-secondary:active {
        color: #00B1F2 !important;
    }

    .navbar-primary > i, .slideout-link-div > i, .nav-member-more-link > i, .nav-member-profile-link > i, .nav-member-map-link > i, .nav-member-badges-link > i, .nav-member-summits-link > i, .nav-member-challenges-link > i, .nav-member-photos-link > i, .nav-member-settings-link > i, .nav-member-admin-link > i, .nav-member-logout-link > i {
        margin-right: 5px;
    }

/* clean this up */
 #navbar-login-link {
                    border: none;
                    background: rgba(0,177,242,0.80);
                    border-radius: 12px;
                    font-size: 14px;
                    margin-top: 16px;
                    color: #fff;
                    letter-spacing: 0.42px;
                    filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                    -webkit-filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                    text-align: center;
                    padding: 11px 24px;
                }

                 #navbar-link-login {
                    position:absolute;
                    right:300px;
                    top: 23px;
                    transition: 0.2s ease-in-out;
                }

                #navbar-link-login:hover {
                    -webkit-transform: scale(1.1);
                    transform: scale(1.1);
                    }

                #navbar-join-link {
                    padding: 11px 24px;
                    border: none;
                    background: rgba(242,65,0,0.80);
                    border-radius: 12px;
                    font-size: 14px;
                    right: 32%;
                    left: inherit !important;
                    line-height: 20px;
                    color: #fff !important;
                    letter-spacing: 0.42px;
                    filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                    -webkit-filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                }

                 #navbar-join-link:hover {
                    background: #F24100;
            }

                #navbar-link-join {
                    position:absolute;
                    right:410px;
                    top: 23px;
                    transition: 0.2s ease-in-out;
                }

                #navbar-link-join:hover {
                    position:absolute;
                    right:410px;
                    top: 23px;
                    -webkit-transform: scale(1.1);
                    transform: scale(1.1);
                }

                .log-climb-dropdown {
                    right: 8px !important;
                    top: 70px;
                    width: 257px;
                }
                #navbar-you-link {
                    right: 280px;
                }
                .nav-you-dropdown {
                    right: 151px;
                }




/* PEAKERY LOGO */

    #main-nav-logo {
        filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
        -webkit-filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
    }

    .navbar-brand {
        color: #ccc !important;
    }

    .navbar-brand:hover {
        color: #fff !important;
    }

    .navbar-brand-latest {
        float: left;
        font-size: 18px;
        line-height: 20px;
        height: 50px;
        color: #ccc !important;
    }

    .navbar-brand-latest:hover {
        color: #fff !important;
    }

    #nav-logo-tagline {
        font-size: 14px;
        font-weight: 400;
    }

            #navbar-home-link {
                position: absolute;
                left: 17%;
                top: 24px;
                transition: 0.2s ease-in-out;

            }

            #navbar-home-link:hover {
                color: #fff;
                -webkit-transform: scale(1.4);
                transform: scale(1.4);
            }

             #navbar-map-link {
                    position: absolute;
                    left: 25%;
                    top: 23px;
                    transition: 0.2s ease-in-out;
             }

            #navbar-map-link:hover {
                color: #fff;
                -webkit-transform: scale(1.4);
                transform: scale(1.4);
            }


            #navbar-peaks-link {
                top: 23px;
                transition: 0.2s ease-in-out;
                left: 33%;
                position: absolute;
            }

            #navbar-peaks-link:hover {
                color: #fff;
                -webkit-transform: scale(1.4);
                transform: scale(1.4);
            }

            #navbar-challenges-link {
                position: absolute;
                left: 41%;
                top: 23px;
                transition: 0.2s ease-in-out;
            }

            #navbar-challenges-link:hover {
                color: #fff;
                -webkit-transform: scale(1.4);
                transform: scale(1.4);
            }

            #nav-more-dropdown-link {
                position: absolute;
                left: 47%;
                border: none;
                background: transparent;
                border-radius: 4px;
                font-size: 14px;
                letter-spacing: 0.42px;
                color: #ddd;
                top: 8px;
                width: 65px;
            }


            #nav-more-dropdown-link:hover {
                color: #fff;
                transform: scale(1.4);
            }

            /* more dropdown */
            .nav-more-dropdown {
                position: absolute;
                left: calc(47% - 5px);
                top: 68px !important;
                width: 180px;
                text-align: center;
                border-radius: 0;
                background-color: #333;
                transition: right 0.3s ease-in-out 0s;
                -webkit-transition: right 0.3s ease-in-out 0s;
                -moz-transition: right 0.3s ease-in-out 0s;
                -o-transition: right 0.3s ease-in-out 0s;
                background-color:#333;
                border: none;
                background: #383838;
                box-shadow: 0 4px 10px 0 #000000;
            }

            .nav-more-dropdown-tab {
                height: 70px;
                position: absolute;
                top: -70px;
                background: #333;
                box-shadow: 0 -8px 10px 0 #000000;
                width: 90px;
            }

            .nav-more-dropdown-icon-div {
                float: left;
                width: 25px;
                padding-top: 2px;
                margin-right: 5px;
            }

            .nav-more-dropdown-label-div {
                float: left;
            }

            .nav-more-dropdown > li {
                height: 50px;
                background-image: linear-gradient(-180deg, #383838 0%, #333333 100%);
            }

            .nav-more-dropdown > li > a {
                color: #ccc;
                letter-spacing: 0.5px;
                text-align: left;
                padding-top: 16px;
            }

            .nav-more-dropdown > li > a:hover {
                color: #ffffff;
                background-color: transparent;
            }

    /* login link */
            #navbar-login-link {
                line-height: 20px;
            }

            #navbar-login-link:hover {
                color: #fff;
            }



    /* login modal */
            .nav-login-input {
                width: 90% !important;
                margin-bottom: 15px;
                margin-left: 20px;
                border-radius: 0;
                background-color: #fff;
                box-shadow: none;
            }

            .nav-login-submit {
                width: 90%;
                height: 50px;
                margin-bottom: 15px;
            }

            .nav-login-forgot {
                font-size: 10px;
            }





    /* join modal */
            .signUpWithEmail form span.a input {
                width: 100%;
                border: 1px solid #ccc;
                height: 50px;
            }

            .signUpWithEmail form span.a {
                width: 100%;
                margin-bottom: 15px;
            }

            .signUpWithEmail {
                padding: 5px 15px;
            }

    /* alerts nav link */
            #nav-alerts {
                background: rgba(0,177,242,0.80);
                border: #00B1F2;
                border-radius: 4px;
                font-size: 14px;
                color: #FFFFFF;
                letter-spacing: 0.42px;
                width: 50px;
                height: 34px;
                padding: 0px;
                color: #fff;
                padding-top: 7px;
                right: 5px;
                filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                -webkit-filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                z-index: 99999;
            }

            #nav-alerts:hover {
                background: #00B1F2;
            }

            .nav-alerts-hover {
                background: #00B1F2 !important;
            }


            .news_icon_text {
                font-size: 14px;
                color: #FFFFFF;
                letter-spacing: 0.42px;
                float: right;
                margin-left: -10px;
                margin-top: -1px;
                margin-right: 1px;
                background: transparent;
            }

            #nav-alerts-bell {
                font-size: 18px;
            }

            #mobile-nav-alerts-bell {
                font-size: 14px;
            }

            .nav-member-news-count {
                z-index: 1001;
                position: relative;
            }

            .nav-alerts-bell-div.bell-with-alerts {
                width: 50%;
                float: left;
                margin-left: 3px;
            }

            .nav-alerts-bell-div.bell-without-alerts {
                width: 100%;
                float: left;
                margin-left: 0px;
            }

            .nav-alerts-count-div.bell-with-alerts {
                float: left;
                width: 50%;
                text-align: center;
                margin-left: -5px;
                margin-right: 0px;
            }

            .mobile-nav-alerts-bell-div.bell-with-alerts {
                width: 50%;
                float: left;
                margin-left: 3px;
            }

            .mobile-nav-alerts-bell-div.bell-without-alerts {
                width: 100%;
                float: left;
                margin-left: 0px;
            }

            .mobile-nav-alerts-count-div.bell-with-alerts {
                float: left;
                width: 50%;
                text-align: center;
                margin-left: -5px;
                margin-right: 0px;
            }

    /* alerts dropdown */
            #nav-alerts-dropdown-div {
                text-align: center;
                border-radius: 0;
                background-color: #fff;
                transition: right 0.3s ease-in-out 0s;
                -webkit-transition: right 0.3s ease-in-out 0s;
                -moz-transition: right 0.3s ease-in-out 0s;
                -o-transition: right 0.3s ease-in-out 0s;
                border: none;
                box-shadow: 0 4px 10px 0 #000000;
                z-index: 9999;
            }

            .nav-alerts-dropdown-tab {
                width: 80px;
                height: 50px;
                position: absolute;
                top: -50px;
                right: 0px;
                background: #fff;
                box-shadow: 0 -8px 10px 0 #000000;
            }

            .nav-alerts-dropdown > li > a {
                color: #ccc;
                letter-spacing: 0.5px;
                text-align: center;
                margin-top: 10px;
                margin-bottom: 10px;
            }

            .nav-alerts-dropdown > li > a:hover {
                color: #ffffff;
                background-color: transparent;
            }

            .nav-alerts-loading {
                text-align: center;
            }

            .nav-alerts-link {
                margin: 0px !important;
                padding: 0px !important;
            }

            .nav-alerts-li:hover {
                background-image: none;
                background-color: #fde1d6;
                -webkit-filter: brightness(1.1) saturate(1.5);
                -webkit-transition: opacity .25s ease-in-out;
            }

            .nav-alerts-li-div {
                width: 100%;
                text-align: left;
                display: table;
            }

            .nav-alerts-li {
                padding: 10px;
                background-image: linear-gradient(-180deg, #FFFFFF 0%, #F6F6F6 100%);
            }

            .nav-alerts-li-left {
                display: table-cell;
                width: 40px;
                vertical-align: top;
            }

            .nav-alerts-li-middle {
                float: left;
                font-size: 12px;
                color: #444444;
                letter-spacing: 0.2px;
                display: inline;
                vertical-align: middle;
                padding-left: 10px;
                padding-right: 10px;
                line-height: 18px;
            }

            .nav-alerts-li-middle > .nav-alerts-link {
                color: #444444;
            }

            .nav-alerts-li-wide-middle {
                font-size: 12px;
                color: #444444;
                letter-spacing: 0.2px;
                display: table-cell;
                vertical-align: middle;
                padding-left: 10px;
                padding-right: 10px;
                line-height: 18px;
            }

            .nav-alerts-li-right {
                display: table-cell;
                width: 40px;
            }

            .nav-alerts-li-wide-right {
                display: table-cell;
                width: 140px;
                text-align: right;
                vertical-align: top;
            }

            .nav-alerts-dropdown {
                padding: 0px;
            }

            /* you nav link */
            #navbar-you-link {
                z-index: 99999;
            }

            #navbar-you-avatar {
                filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                -webkit-filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                height: 34px;
                width: 34px;
                border-radius: 4px;
            }

    /* you dropdown */
            .nav-you-dropdown {
                text-align: center;
                border-radius: 0;
                background-color: #333;
                transition: right 0.3s ease-in-out 0s;
                -webkit-transition: right 0.3s ease-in-out 0s;
                -moz-transition: right 0.3s ease-in-out 0s;
                -o-transition: right 0.3s ease-in-out 0s;
                background-color:#333;
                border: none;
                background: #383838;
                box-shadow: 0 4px 10px 0 #000000;
                padding-top: 0px;
                padding-bottom: 0px;
            }

            .nav-you-dropdown-tab {
                height: 50px;
                position: absolute;
                top: -50px;
                left: 0px;
                background: #383838;
                box-shadow: 0 -8px 10px 0 #000000;
            }

            .nav-you-dropdown-icon-div {
                float: left;
                width: 25px;
                padding-top: 2px;
                margin-right: 5px;
            }

            .nav-you-dropdown-label-div {
                float: left;
            }

            .nav-you-dropdown > li {
                height: 50px;
                background-image: linear-gradient(-180deg, #383838 0%, #333333 100%);
            }

            .nav-you-dropdown > li > a {
                color: #ccc;
                letter-spacing: 0.5px;
                text-align: left;
                padding-top: 16px;
            }

            .nav-you-dropdown > li > a:hover {
                color: #ffffff;
                background-color: transparent;
            }

            /* log climb nav button */
            #nav-log-your-climb {
                position: absolute;
                top: 14px;
                width: 120px;
                border-radius: 12px;
                height: 40px;
                padding-top: 10px;
                padding-left:10px;
                border: none;
                background: rgba(242,65,0,0.80);
                font-size: 14px;
                color: #fff;
                letter-spacing: 0.42px;
                filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                -webkit-filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                right: 310px;
                }


                #nav-log-your-climb:hover {
                background: #F24100;
                }

            #fake-log-your-climb {
                border: none;
                background: rgba(242,65,0,0.80);
                border-radius: 4px;
                font-size: 14px;
                color: #fff;
                letter-spacing: 0.42px;
                filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                -webkit-filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
            }




            #fake-log-your-climb:hover {
                background: #F24100;
            }



    /* log climb dropdown */
    .log-climb-dropdown {
        text-align: center;
        border-radius: 0;
        background-color: #333;
        transition: right 0.3s ease-in-out 0s;
        -webkit-transition: right 0.3s ease-in-out 0s;
        -moz-transition: right 0.3s ease-in-out 0s;
        -o-transition: right 0.3s ease-in-out 0s;
        background-color:#333;
        border: none;
        background: #383838;
        box-shadow: 0 4px 10px 0 #000000;
        padding-top: 0px;
        padding-bottom: 0px;
    }

    .log-climb-dropdown-tab {
        height: 50px;
        width: 140px;
        position: absolute;
        top: -50px;
        left: 0px;
        background: #383838;
        box-shadow: 0 -8px 10px 0 #000000;
    }

    .log-climb-dropdown-icon-div {
        float: left;
        width: 25px;
        padding-top: 2px;
        margin-right: 5px;
    }

    .log-climb-dropdown-label-div {
        float: left;
    }


    .log-climb-dropdown-this-peak-div {
        text-align: left;
    }

    .log-climb-dropdown > li {
        height: 50px;
        background-image: linear-gradient(-180deg, #383838 0%, #333333 100%);
    }

    .log-climb-dropdown > li > a {
        color: #ccc;
        letter-spacing: 0.5px;
        text-align: center;
        padding-top: 16px;
    }

    .log-climb-dropdown > li > a:hover {
        color: #ffffff;
        background-color: transparent;
    }

    #gpx-upload-button {
        background-color: transparent !important;
    }

    #gpx-upload-button:hover {
        color: #fff !important;
    }

    /* peak search input */
    .navbar-search {
        text-align: right !important;
    }

    .peak-search-input-desktop {
        border-color: #444 !important;
        background: #444 !important;
        box-shadow: none !important;
        opacity: 0.95 !important;
        font-size: 14px !important;
        color: #333 !important;
        letter-spacing: 0.42px !important;
        border-radius: 12px !important;
        width: 260px;
        height: 41px;
    }

    .peak-search-input-desktop-li {
            position: fixed;
            right: 20px;
            top: 15px;
            z-index: 999999;
            }


    .peak-search-input-desktop:hover, .peak-search-input-desktop:focus {
        opacity: 0.95 !important;
        background: #FFFFFF !important;
        box-shadow: none !important;
    }

    .peak-search-input-desktop::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
        color: #999;
        opacity: 1; /* Firefox */
    }

    .peak-search-input-desktop:-ms-input-placeholder { /* Internet Explorer 10-11 */
        color: #999;
    }

    .peak-search-input-desktop::-ms-input-placeholder { /* Microsoft Edge */
        color: #999;
    }



    /* Auto Complete Styles */
    .autocomplete-suggestions { border: 1px solid #999; background: #FFF; overflow: auto; z-index: 99999 !important; }
    .autocomplete-suggestion { padding: 10px 10px; white-space: nowrap; overflow: hidden; cursor: pointer;}
    .autocomplete-no-suggestion { padding: 10px 10px; white-space: nowrap; overflow: hidden;}
    .autocomplete-selected { background: #F0F0F0; }
    .autocomplete-suggestions strong { font-weight: 700; color: #00b1f2; }
    .autocomplete-group { padding: 2px 5px; }
    .autocomplete-group strong { display: block; border-bottom: 1px solid #000; }

    @media (min-width: 768px) {
      .autocomplete-suggestions { max-height: 315px !important; width: 320px; margin-left: -120px !important; }
    }

    /* BELL */
    @-webkit-keyframes ring {
      0% {
        -webkit-transform: rotate(-15deg);
        transform: rotate(-15deg);
      }

      2% {
        -webkit-transform: rotate(15deg);
        transform: rotate(15deg);
      }

      4% {
        -webkit-transform: rotate(-18deg);
        transform: rotate(-18deg);
      }

      6% {
        -webkit-transform: rotate(18deg);
        transform: rotate(18deg);
      }

      8% {
        -webkit-transform: rotate(-22deg);
        transform: rotate(-22deg);
      }

      10% {
        -webkit-transform: rotate(22deg);
        transform: rotate(22deg);
      }

      12% {
        -webkit-transform: rotate(-18deg);
        transform: rotate(-18deg);
      }

      14% {
        -webkit-transform: rotate(18deg);
        transform: rotate(18deg);
      }

      16% {
        -webkit-transform: rotate(-12deg);
        transform: rotate(-12deg);
      }

      18% {
        -webkit-transform: rotate(12deg);
        transform: rotate(12deg);
      }

      20% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
      }
    }

    @keyframes ring {
      0% {
        -webkit-transform: rotate(-15deg);
        -ms-transform: rotate(-15deg);
        transform: rotate(-15deg);
      }

      2% {
        -webkit-transform: rotate(15deg);
        -ms-transform: rotate(15deg);
        transform: rotate(15deg);
      }

      4% {
        -webkit-transform: rotate(-18deg);
        -ms-transform: rotate(-18deg);
        transform: rotate(-18deg);
      }

      6% {
        -webkit-transform: rotate(18deg);
        -ms-transform: rotate(18deg);
        transform: rotate(18deg);
      }

      8% {
        -webkit-transform: rotate(-22deg);
        -ms-transform: rotate(-22deg);
        transform: rotate(-22deg);
      }

      10% {
        -webkit-transform: rotate(22deg);
        -ms-transform: rotate(22deg);
        transform: rotate(22deg);
      }

      12% {
        -webkit-transform: rotate(-18deg);
        -ms-transform: rotate(-18deg);
        transform: rotate(-18deg);
      }

      14% {
        -webkit-transform: rotate(18deg);
        -ms-transform: rotate(18deg);
        transform: rotate(18deg);
      }

      16% {
        -webkit-transform: rotate(-12deg);
        -ms-transform: rotate(-12deg);
        transform: rotate(-12deg);
      }

      18% {
        -webkit-transform: rotate(12deg);
        -ms-transform: rotate(12deg);
        transform: rotate(12deg);
      }

      20% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }
    }

        .faa-ring.animated,
        .faa-ring.animated-hover:hover,
        .faa-parent.animated-hover:hover > .faa-ring {
              -webkit-animation: ring 5s ease infinite;
              animation: ring 5s ease infinite;
              transform-origin-x: 50%;
              transform-origin-y: 0px;
              transform-origin-z: initial;
        }

            .navbar-fixed-top {
                height: 70px;
            }

            #main-nav-logo {
                width: 35px;
                height: 33px;
                margin-left: 5px;
                margin-top: 5px;
                display: inline-block;
                vertical-align: middle;
            }

            #main-nav-title {
                font-size: 24px;
                color: #fff;
                line-height: 16px;
                display: inline-block;
                vertical-align: middle;
                margin-left: 4px;
                margin-top: 2px;
                font-weight: 700;
                letter-spacing:-1.2px;
                transform: scaleY(1.1);
            }

            .main-header-row {
                top: 70px;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
            }

            #content-body {
                margin-top: 90px;
            }

            .navbar-header {
                max-width: inherit;
                position: absolute;
                left: 15px;
            }

            .nav-icon-selected {
                color: #F24100;
            }

            .nav-icon-unselected {
                color: #ddd;
            }

            .log-climb-dropdown {
                margin-top: 0px !important;
                left: auto !important;
            }

            .log-climb-dropdown-tab {
                height: 70px;
                top: -70px;
            }




            #navbar-login-link:hover {
                background: #00B1F2;
                -webkit-transform: scale(1.3);
                transform: scale(1.3);
                transition: 0.2s ease;
            }



            .peak-search-input-desktop:focus, .peak-search-input-desktop:hover {
                border-radius: 12px !important;
            }


            .autocomplete-suggestion {
                padding-top: 10px !important;
                padding-bottom: 10px !important;
                padding-left: 10px !important;
            }

            .autocomplete-suggestions {
                width: 570px;
            }



            #navbar-you-avatar {
                width: 40px;
                height: 40px;
                border-radius: 10px;
            }

            #navbar-you-link {
                top: 30px;
                position: absolute;
            }

            #navbar-link-you {
                position: absolute;
                right: 0px !important;
                top: 0px !important;
            }

            .navbar-log-climb-li {
                position: absolute;
                right: 0px !important;
                top: 0px;
                float: right !important;
                right: 12px;
            }

            .nav-you-dropdown {
                display: block;
                left: auto !important;
                margin-top: 70px !important;
            }

            .nav-you-dropdown-tab {
                height: 70px;
                top: -70px;
                width: 64px !important;
            }

            #nav-alerts {
                height: 40px;
                border-radius: 12px;
                padding-top: 10px;
                position: initial;
            }

            #navbar-alerts {
                top: 14px !important;
                position: absolute;
                right: 532px !important;
            }

            #nav-alerts-dropdown-div {
                margin-top: 15px !important;
            }

            .nav-alerts-dropdown-tab {
                display: none;
            }


                .log-climb-dropdown {
                    right: 162px !important;
                    top: 70px;
                    width: 280px;
                }
                #navbar-you-link {
                    right: 463px;
                }


                #navbar-login-link {
                    border: none;
                    background: rgba(0,177,242,0.80);
                    border-radius: 12px;
                    font-size: 14px;
                    margin-top: 16px;
                    color: #fff;
                    letter-spacing: 0.42px;
                    filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                    -webkit-filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                    text-align: center;
                    padding: 12px 24px;
                }

                 #navbar-link-login {
                    position:absolute;
                    right:300px;
                    top: 23px;
                }


                /* logo and brand */
                .navbar-brand {
                    margin-right: 0px;
                }

                .navbar-brand-latest {
                    padding-left: 42px;
                    padding-right: 15px;
                    padding-top: 15px;
                    padding-bottom: 15px;
                }

                #nav-logo-tagline {
                    margin-top: 2px;
                    margin-left: -14px;
                }

                /* peaks */
                #nav-peaks-dropdown-link {
                    position: absolute;
                    left: 72px;
                    top: 8px;
                    width: 65px;
                    height: 34px;
                    padding-top: 7px;
                    padding-bottom: 0px;
                    padding-left: 0px;
                    padding-right: 0px;
                }

                .nav-peaks-dropdown {
                    left: 59px !important;
                    top: 48px !important;
                    width: 205px;
                }

                .nav-peaks-dropdown-tab {
                    width: 91px;
                }





                /* login */

                /* alerts */
                .navbar-alerts-li {
                    float: right !important;
                    right: 23px !important;
                    top: 20px !important;
                }

                #nav-alerts {
                    top: -14px;
                }

                #nav-alerts-dropdown-div {
                    left: -534px !important;
                    width: 596px !important;
                    margin-top: -6px !important;
                }

                .nav-alerts-dropdown-tab {
                    width: 80px;
                }

                #nav-alerts-ul {
                    width: 596px !important;
                }

                #nav-alerts-container {
                    width: 596px !important;
                }

                .nav-alerts-li-middle {
                    width: 395px;
                    max-width: 395px;
                    white-space: initial;
                }

                .nav-alerts-li-wide-middle {
                    width: 476px;
                    max-width: 476px;
                    white-space: initial;
                }

                /* you */
                #navbar-you-link {
                    padding: 0px;
                    margin-left: 42px;
                }

                #navbar-you-avatar {
                    margin-top: -14px;
                }

                .navbar-you-li {
                    float: right !important;
                    right: 41px !important;
                    top: 21px !important;
                }

                .nav-you-dropdown {
                    width: 180px;
                    margin-top: 70px;
                    right: 335px;
                }

                .nav-you-dropdown-tab {
                    width: 64px;
                }


                .log-climb-dropdown {
                    width: 280px;
                    margin-top: 8px !important;
                }

                .log-climb-dropdown-this-peak-div {
                    width: 206px;
                }

                .qq-uploader-selector {
                    margin-inline: auto;
                    background-color: rgba(250, 250, 250, 0) !important;
                }

                #gpx-upload-button {
                    width: 344px;
                    padding-left: 18px;
                }



/* nav - extra breakpoint since icons get crammed */

            @media (1025px <= width <= 1279px)  {
                .peak-search-input-desktop-li {
                    position: absolute;
                    right: 20px;
                    top: 15px !important;
                    z-index: 999999;
                }
                .search-input-size {
                    width: 99px;
                }

                #navbar-link-join {
                    right:250px;
                }

                #navbar-link-login {
                    right:140px;
                }
                #main-nav-title {
                    display: none;
                }
                #nav-log-your-climb {
                    right: 138px;
                }
                #navbar-you-link {
                    right: 281px;
                }
                .nav-you-dropdown {
                    right: 153px;
                }
                .log-climb-dropdown {
                    right: -10px !important;
                }
                #navbar-alerts {
                    right: 340px !important;
                }
            }


/* nav - tablet */
            @media (768px <= width <= 1024px)  {

                .peak-search-input-desktop-li {
                    position: absolute;
                    right: 0px;
                    top: 15px !important;
                    z-index: 999999;
                }

                .search-input-size {
                    width: 99px;
                }

                #navbar-link-join {
                    right:250px;
                }

                #navbar-link-login {
                    right:140px;
                }
                #main-nav-title {
                    display: none;
                }
                #nav-log-your-climb {
                    right: 108px;
                }
                .log-climb-dropdown {
                    right: -40px !important;
                    margin-top: 0px !important;
                }


                #navbar-you-link {
                    right: 241px;
                }

                #navbar-alerts {
                    right: 290px !important;
                }

                    .navbar-brand {
                        margin-right: 0px;
                    }

                    .navbar-brand-latest {
                        padding-left: 20px;
                        padding-right: 15px;
                        padding-top: 15px;
                        padding-bottom: 15px;
                    }

                    #nav-logo-tagline {
                        margin-top: 2px;
                        margin-left: -16px;
                    }

                    #navbar-home-link {
                        left:12%;
                    }

                    #navbar-map-link {
                        left: 21%;
                    }

                    #navbar-peaks-link {
                        left: 30%;
                    }

                    /* challenges */
                    #navbar-challenges-link {
                        left: 39%;
                    }

                    /* more */
                    #nav-more-dropdown-link {
                        left: 46%;
                    }

                    .nav-more-dropdown {
                        left: 46% !important;
                        top: 48px !important;
                        width: 180px;
                    }

                    .nav-more-dropdown-tab {
                        width: 80px;
                    }


                    #navbar-link-join {
                        right:230px;
                    }

                    #navbar-link-login {
                        right:120px;
                    }

                    /* alerts */
                    .navbar-alerts-li {
                        position: absolute !important;
                        right: 374px !important;
                        top: 7px !important;
                    }

                    .news_icon_text {
                        margin-top: -3px !important;
                    }

                    #nav-alerts-dropdown-div {
                        left: -345px !important;
                        width: 400px !important;
                        margin-top: 7px !important;
                    }

                    .nav-alerts-dropdown-tab {
                        width: 68px;
                    }

                    #nav-alerts-ul {
                        width: 400px !important;
                    }

                    #nav-alerts-container {
                        width: 400px !important;
                    }

                    .nav-alerts-li-middle {
                        width: 199px;
                        max-width: 199px;
                        white-space: initial;
                    }

                    .nav-alerts-li-wide-middle {
                        width: 280px;
                        max-width: 280px;
                        white-space: initial;
                    }

                    #nav-alerts {
                        top: -1px;
                    }

                    /* you */
                    #navbar-you-link {
                        padding: 0px;
                        margin-left: 25px;
                    }

                    #navbar-you-avatar {
                        margin-top: 1px;
                    }

                    .navbar-you-li {
                        position: absolute !important;
                        right: 331px !important;
                        top: 7px !important;
                    }

                    .nav-you-dropdown {
                        right: 113px;
                        width: 180px;
                        margin-top: 85px !important;
                    }

                    .nav-you-dropdown-tab {
                        width: 50px;
                    }

                    #navbar-link-you {
                        top: -15px !important;
                    }


                    .qq-uploader-selector {
                        width: 568px;
                        cursor: pointer;
                        background-color: #e2e2e2;
                    }

                    #gpx-upload-button {
                        width: 313px;
                        padding-left: 18px;
                        left: -125px;
                    }

                    /* search */
                    .search-input-size {
                        width: 99px;
                    }

                    .peak-search-input-desktop-li {
                        position: absolute;
                        right: 0px;
                        top: 15px !important;
                        z-index: 999999;
                    }

                    #navbar, .dropdown-menu {
                        font-size: 14px;
                    }

                    #main-nav-title {
                        display: none;
                    }

            }


/* nav - mobile */
            @media (767px >= width) {

                /* alerts */
                .nav-alerts-li-middle {
                    white-space: initial;
                }

                .nav-alerts-li-wide-middle {
                    white-space: initial;
                }
                /* mobile title */
                .mobile-logo-title {
                    right: 120px;
                }

                #navbar-alerts {
                    display: none;
                }
               .nav-justified > li > a {
                    color: #ccc;
                    letter-spacing: 0.5px;
                    text-align: left;
                }
                .autocomplete-suggestions {
                    width: calc(100% - 40px);
                }
            }


.mobile-logo-tagline {
                font-size: 18px;
                color: #FFFFFF;
                line-height: 16px;
                display: inline-block;
                vertical-align: middle;
                font-weight: 500;
                position: absolute;
                margin-top: 17px !important;
                left: 20px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                -o-text-overflow: ellipsis;
        }

            #mobile-nav-logo {
                width: 30px;
                height: 28px;
                margin-left: 5px;
                margin-top: 6px;
                filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                -webkit-filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
            }



/*************************/
/* MOBILE MENU - slideout*/
/*************************/

        .mobile-logo {
            position: fixed;
            top: 0px;
            left: 0px;
            z-index: 3;
        }



        .drawer-nav {
            overflow-y: scroll; /* has to be scroll, not auto */
            -webkit-overflow-scrolling: touch;
        }

        .drawer-menu {
            background-color:#333;
        }

        .drawer-nav {
            background-color:#333;
        }

        .slideout-links {
            color: #ccc;
            margin-left: 20px;
            font-size: 18px;
            font-weight: 300;
        }

        .slideout-links-li {
            height: 60px;
            background-image: linear-gradient(-180deg, #383838 0%, #333333 100%);
            padding-left: 23px;
            padding-bottom: 0px !important;
            padding-top: 20px !important;
            margin-bottom: 0px !important;
        }

        .slideout-links-icon-div {
            float: left;
            width: 25px;
            text-align: center;
            padding-top: 0px;
            margin-right: 10px;
        }

        .slideout-links-label-div {
            float: left;
            margin-top: -4px;
            font-weight: 500;
            font-size: 16px;
        }

        .slideout-toggle {
            color: #ccc;
            margin-left: 20px;
            font-size: 18px;
            font-weight: 300;
        }

        .slideout-modal-links {
            color: #ccc;
            margin-left: 20px;
            font-size: 18px;
            font-weight: 300;
        }

        .slideout-link-div {
            margin-left: 20px;
            margin-right: 20px;
            border-bottom: solid .5px #555;
            padding-bottom: 25px;
        }

        .slideout-sublinks {
            color: #ccc;
            margin-left: 20px;
            font-size: 14px;
            font-weight: 300;
        }

        .slideout-li {
            margin-bottom: 10px;
            padding-top: 10px;
            padding-bottom: 10px;
        }

        .slideout-separator {
            height: 1px;
            border-top: solid 1px #333;
        }

        .slideout-accordion-inner {
            padding-left: 1em;
            overflow: hidden;
            display: none;
        }

        #slideout-nav-join-login-li {
            margin-left: 20px;
            margin-right: 20px;
            margin-bottom: 60px;
        }

        #slideout-nav-log-climb-alerts-li {
            margin-left: 20px;
            margin-right: 20px;
            margin-bottom: 60px;
        }

        #slideout-nav-peak-search-li {
            margin-bottom: 55px;
            padding-top: 0px;
        }

        #slideout-nav-latest-li {
            margin-left: 10px;
        }

        #slideout-nav-peaks-li {
            margin-left: 10px;
        }

        #slideout-nav-challenges-li {
            margin-left: 10px;
        }

        #slideout-nav-you-li {
            margin-left: 10px;
        }

        #slideout-nav-more-li {
            margin-left: 10px;
        }

        .slideout-accordion-inner > li {
            margin-top: 20px;
        }

        #slideout-nav-join-btn {
            width: 100%;
            background: rgba(242,65,0,0.80);
            border: none;
            border-radius: 6px;
            font-size: 16px;
            color: #FFFFFF;
            letter-spacing: 0.48px;
            text-align: center;
        }

        #slideout-nav-login-btn {
            width: 100%;
            background: rgba(0,177,242,0.80);
            border: none;
            border-radius: 6px;
            font-size: 16px;
            color: #FFFFFF;
            letter-spacing: 0.48px;
            text-align: center;
        }

        #slideout-nav-log-climb-btn {
            width: 100%;
            background: rgba(242,65,0,0.80);
            border: none;
            border-radius: 6px;
            font-size: 16px;
            color: #FFFFFF;
            letter-spacing: 0.48px;
            text-align: center;
        }

        #slideout-nav-log-a-climb-btn {
            width: 100%;
            height: 50px;
            background: rgba(242,65,0,0.80);
            border: none;
            border-radius: 12px;
            font-size: 16px;
            color: #FFFFFF;
            letter-spacing: 0.48px;
            text-align: center;
        }

        #slideout-nav-alerts-btn {
            width: 100%;
            height: 50px;
            background: rgba(0,177,242,0.80);
            border: none;
            border-radius: 12px;
            font-size: 18px;
            color: #FFFFFF;
            letter-spacing: 0.48px;
            text-align: center;
        }

        .drawer--right.drawer-open .drawer-nav {
            width: 90%;
        }

        #mobile-searchbox-icon {
            cursor: pointer;
        }

        .peak-search-input-mobile {
            width: 100%;
            border: 1px solid #333;
            height: 55px;
            opacity: 0.95 !important;
            background: #ddd !important;
            box-shadow: inset 0 1px 3px 0 rgba(0,0,0,0.50), inset 0 1px 0 0 rgba(0,0,0,0.50) !important;
            border-radius: 12px !important;
            opacity: 0.95 !important;
            font-size: 16px !important;
            color: #333 !important;
            letter-spacing: 0.42px !important;
            padding: 8px 15px;
        }

        .peak-search-input-mobile:hover {
            opacity: 0.95 !important;
            background: #FFFFFF !important;
            box-shadow: inset 0 1px 3px 0 rgba(0,0,0,0.50), inset 0 1px 0 0 rgba(0,0,0,0.50) !important;
            border-radius: 6px !important;
        }

        .peak-search-input-mobile::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
            color: #BBB;
            opacity: 1; /* Firefox */
        }

        .peak-search-input-mobile:-ms-input-placeholder { /* Internet Explorer 10-11 */
            color: #BBB;
        }

        .peak-search-input-mobile::-ms-input-placeholder { /* Microsoft Edge */
            color: #BBB;
        }

        #mobile-nav-alerts {
                background: rgba(0,177,242,0.80);
                border: #00B1F2;
                border-radius: 4px;
                font-size: 14px;
                color: #FFFFFF;
                letter-spacing: 0.42px;
                width: 42px;
                height: 32px;
                padding: 0px;
                color: #fff;
                padding-top: 7px;
                right: 5px;
                filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                -webkit-filter: drop-shadow(0px 2px 4px rgba(0,0,0,1));
                z-index: 99999;
                margin-top: 9px;
        }

        #mobile-nav-alerts:hover {
            background: #00B1F2;
        }

        #mobile-nav-alerts:active {
            top: 0px;
            position: initial;
        }

        #mobile-nav-alerts-count {
            border: none;
            padding: 0px;
            margin-top: 0px;
            letter-spacing: 0em;
        }

        #mobile-nav-alerts-bell {
            margin-top: 2px;
            margin-right: 2px;
        }



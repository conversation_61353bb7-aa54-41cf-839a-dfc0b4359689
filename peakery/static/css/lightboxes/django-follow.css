/*
    ** Peakery follow lightboxes */

.djangoFollow {

}
.djangoFollow h3 {
    font-size: 22px;
}
.djangoFollow h3 span {
    font-size: 24px;
    font-weight: bold;
}
ul.followersList {
    display: block;
    width: 350px;
    margin:  5px auto;
}
ul.followersList li {
    display: block;
    list-style: none;
    margin: 5px 0px;
    width: 350px;
    overflow: hidden;
    background: #FFF;
}
ul.followersList li span.followerWrapper {
    display: block;
    padding:  7px 5px;
}
ul.followersList li span.followerWrapper .leftCol {
    width: 65px;
    height: 65px;
    margin-right: 10px;
    float: left;
    display: inline-block;
}
ul.followersList li span.followerWrapper .rightCol {
    display: inline-block;
    width: 265px;
}
ul.followersList li span.followerWrapper .rightCol h3 {
    display: block;
    margin-bottom: 0px;
    font-size: 14px;
    font-weight: bold;
    color: #333;
}
ul.followersList li span.followerWrapper .rightCol p {
    display: block;
    font-size: 11px;
    font-weight: normal;
    color: #444;
    margin-bottom: 0px;
}
.djangoFollow a.seeMoreButton {
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: #444;
    text-align: center;
    text-decoration: none;
    margin:  15px 0px 2px;
}
.djangoFollow a.seeMoreButton:hover {
    text-decoration: underline;
}

/*
    Follow & Unfollow buttons
*/
a.peakeryFollowButton {
    color: white;
	display: inline-block;
	font-weight: 700;
	padding: 5px 15px;
	text-decoration: none;
    box-shadow: 0px 1px 0px rgba(0,0,0,0.2);
	-moz-box-shadow: 0px 1px 0px rgba(0,0,0,0.2);
	-webkit-box-shadow: 0px 1px 0px rgba(0,0,0,0.2);
}
a.followButton {
    border: 0px solid #CCC;
    background: #6dbf39; /* Old browsers */
    background: -moz-linear-gradient(top, #6dbf39 0%, #447710 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#6dbf39), color-stop(100%,#447710)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #6dbf39 0%,#447710 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #6dbf39 0%,#447710 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #6dbf39 0%,#447710 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#6dbf39', endColorstr='#447710',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #6dbf39 0%,#447710 100%); /* W3C */
}
a.followButton:hover {
    background: #447710; /* Old browsers */
    background: -moz-linear-gradient(top, #447710 0%, #6dbf39 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#447710), color-stop(100%,#6dbf39)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #447710 0%,#6dbf39 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #447710 0%,#6dbf39 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #447710 0%,#6dbf39 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#447710', endColorstr='#6dbf39',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #447710 0%,#6dbf39 100%); /* W3C */
}
a.unfollowButton {
    border: 0px solid #CCC;
    background: #497fc9; /* Old browsers */
    background: -moz-linear-gradient(top, #497fc9 0%, #284776 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#497fc9), color-stop(100%,#284776)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #497fc9 0%,#284776 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #497fc9 0%,#284776 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #497fc9 0%,#284776 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#497fc9', endColorstr='#284776',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #497fc9 0%,#284776 100%); /* W3C */
}
a.unfollowButton:hover {
    background: #284776; /* Old browsers */
    background: -moz-linear-gradient(top, #284776 0%, #497fc9 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#284776), color-stop(100%,#497fc9)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #284776 0%,#497fc9 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #284776 0%,#497fc9 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #284776 0%,#497fc9 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#284776', endColorstr='#497fc9',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #284776 0%,#497fc9 100%); /* W3C */
}
a.unfollowButtonHover {
    padding: 5px 45px !important;
    background: #a90329; /* Old browsers */
    background: -moz-linear-gradient(top, #a90329 0%, #8f0222 44%, #6d0019 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#a90329), color-stop(44%,#8f0222), color-stop(100%,#6d0019)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #a90329 0%,#8f0222 44%,#6d0019 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #a90329 0%,#8f0222 44%,#6d0019 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #a90329 0%,#8f0222 44%,#6d0019 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#a90329', endColorstr='#6d0019',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #a90329 0%,#8f0222 44%,#6d0019 100%); /* W3C */
}
a.unfollowButtonHoverProfile {
    padding: 8px 45px !important;
    background: #a90329; /* Old browsers */
    background: -moz-linear-gradient(top, #a90329 0%, #8f0222 44%, #6d0019 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#a90329), color-stop(44%,#8f0222), color-stop(100%,#6d0019)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #a90329 0%,#8f0222 44%,#6d0019 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #a90329 0%,#8f0222 44%,#6d0019 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #a90329 0%,#8f0222 44%,#6d0019 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#a90329', endColorstr='#6d0019',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #a90329 0%,#8f0222 44%,#6d0019 100%); /* W3C */
}
from peakery.items.models import Item, ItemCountry, ItemRegion
from peakery.cities.models import Country, Region
from peakery.importer import importer


def fix_item_regions():
    items_to_fix = Item.objects.filter(country__name='Nicaragua')
    print("Fixing...")
    for item in items_to_fix:
        print("Fixing Item: %s" % item.name)
        fix_item_from_coordinates(item)


def fix_item_from_coordinates(item):
    new_region = importer.resolve_region_from_geonames(item.lat, item.long)
    print("Get all regions asociated with item")
    region = item.region.all()
    print("obtained region associated with the item :%s and new: %s" % (region, new_region))
    if new_region and region:
        old_region = item.region.all()[0]
        if old_region != new_region:
            print("Potential Wrong Code: Old: %s  New: %s" % (old_region.name, new_region.name))
            region_to_unlink = ItemRegion.objects.filter(region=old_region, item=item)
            country_to_unlink = ItemCountry.objects.filter(country=old_region.country, item=item)
            if region_to_unlink:
                region_to_unlink.delete()
            if country_to_unlink:
                country_to_unlink.delete()
            item_region = ItemRegion(item=item, region=new_region)
            item_region.save()
            item_country = ItemCountry(item=item, country=new_region.country)
            item_country.save()
            print("Fixed %s at %s" % (item_region.region.name, item_country.country.name))
        else:
            print("The Code match with GeoLocation Services")
    elif new_region:
        print("Found new_region")
    elif new_region and not region:
        ItemRegion(item=item, region=new_region).save()
        ItemCountry(item=item, country=new_region.country).save()
        print("AddRegion for %s" % (ItemRegion))
    print("=" * 80)


def fix_region_item(item, region):
    item_region = ItemRegion(item=item, region=region)
    item_region.save()
    item_country = ItemCountry(item=item, country=region.country)
    item_country.save()
    print("Fixed with GeoNames : %s " % (item_region))


def fix_country_item(item, country):
    country_to_unlink = ItemCountry.objects.filter(item=item)
    country_to_unlink.delete()
    item_country = ItemCountry(item=item, country=country)
    item_country.save()
    region_to_unlink = ItemRegion.objects.filter(item=item)
    region_to_unlink.delete()
    print("Fix Item Country with GeoNames: %s" % (item_country))


def fix_item_without_country(item):
    pass
    try:

        location = importer.resolve_region_or_country_from_geonames(item.lat, item.long)
        print("location :%s - :%s :%s" % (location, isinstance(location, Region), isinstance(location, Country)))
        if isinstance(location, Region):
            print("Using Region")
            fix_region_item(item, location)
        elif isinstance(location, Country):
            fix_country_item(item, location)
        else:
            region = importer.resolve_region_from_name(item.txt_location)
            if region:
                fix_region_item(item, region)
            else:
                print("Item: %s Can't be fix using WS" % (item))
    except:
        pass


def fix_items_without_country():
    items_to_fix = Item.objects.filter(has_country=False)
    print("Fixing...")
    for item in items_to_fix:
        print("Fixing Item: %s" % item.name)
        fix_item_without_country(item)
        print("=" * 80)


def fix_nicaragua_last_items():
    items_to_fix = Item.objects.filter(country__name='Nicaragua', txt_location__contains='China')
    print("Fixing...")
    for item in items_to_fix:
        print("Fixing Item: %s" % item.name)
        fix_item_without_country(item)
        print("=" * 80)


def fix_item_without_region(item=None):
    if item is not None:
        items = [item]
    else:
        items = Item.objects.all()
    for item in items:
        if item.regions_count() == 0 and item.has_region == 1:
            print("Fixing ItemRegion Count: %s" % (item))
            item.has_region = False
            item.save()


def fix_general_regions():
    regions = Region.objects.filter(name__icontains='(general)')
    for region in regions:
        print("Fixing Region: :%s" % (region))
        for item in region.region_items.all():
            print("=" * 80)
            print("Fixing Item: %s" % (item))
            itemRegion = ItemRegion.objects.filter(item=item, region=region)
            if itemRegion.count() > 1:
                itemRegion = itemRegion[0]
            if not item.has_country:
                print("Fixing Item: %s with Country: %s" % (item, item.country))
                ItemCountry(item=item, country=itemRegion.region.country).save()
            itemRegion.delete()
            fix_item_without_region(item)

    # regions.delete()
    print("Finish")


if __name__ == '__main__':
    fix_item_regions()

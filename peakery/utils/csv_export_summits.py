from django.utils.encoding import smart_str
import csv
c = 0
with open('summit_logs.csv', 'wb') as csvfile:
  w = csv.writer(csvfile)
  for s in SummitLog.objects.all():
    d = []
    d.append(s.pk)
    d.append(smart_str(s.item.name))
    d.append(smart_str(s.user.username))
    d.append(s.created)
    d.append(s.date if s.date_entered else '')
    d.append(len(s.log) if s.log else '')
    d.append(s.summit_photos.count())
    d.append(s.summit_photos.exclude(caption__isnull=True).count())
    d.append(s.companions.count())
    d.append(s.route_up.pk if s.route_up else '')
    d.append(s.route_down.pk if s.route_down else '')
    print d
    w.writerow(d)
    c+=1
    print c
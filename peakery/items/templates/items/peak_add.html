{% extends "base_no_header_footer.html" %}
{% load static %}
{% load avatar_tags %}
{% load item_tags %}
{% load cache %}
{% load humanize %}

{% block title %}Add a missing peak to peakery{% endblock %}
{% block titlemeta %}Add a missing peak to peakery{% endblock %}
{% block description %}Add a missing peak to peakery{% endblock %}

{% block mapbox %}
{% include "mapbox/mapbox.html" %}
{% endblock %}

{% block body_form %}<form id="peakedit_form" method="POST" action="/peaks/add/">{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row" style="border-bottom: none;">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 form-header-bar">
            <div class="hidden-xs form-header-bar-img-div"></div>
            <span style="font-size: 24px; font-weight: 600; color: #fff;">
            <div id="breadcrumbs" style="margin-left: 15px;">
                <div class="pull-right cancel-x-button-div">
                    <a class="cancel-x-button" href="/{{ peak.slug_new_text }}"><i class="fa fa-times"></i></a>
                </div>
                <div class="pull-right save-changes-button-div">
                    <button class="btn btn-secondary set2 input save-changes-button" id="edit_peak" disabled>Submit <span class="hidden-xs">peak</span></button>
                </div>
                <div>
                    <div class="form-header-title ellipsis">Add a missing peak<span class="hidden-xs"> to peakery</span></div>
                </div>
            </div>
            </span>
        </div>
    </div>
{% endblock %}

{% block content %}

<style>


    body.modal-open {
        overflow: visible;
    }

    .mapboxgl-ctrl-geocoder--icon-search {
        display: none;
    }

    .mapboxgl-ctrl-geocoder {
        background-color: #fff;
        border-radius: 12px;
        border: 1px solid #CCC;
        z-index: 9;
        font-size: 14px;
        padding: 2px;
        box-shadow:none;
        width: 50%;
        min-width: 340px;
        float: left;
        margin-right: 20px;
    }

    .mapboxgl-ctrl-geocoder--input {
        width: 50%;
        min-width: 340px;
        height: 46px;
        border: none !important;
    }

    .mapboxgl-ctrl-geocoder--input:focus {
        outline: none !important;
    }

    .mapboxgl-ctrl-geocoder--input::placeholder {
        font-size: 16px !important;
    }

    .mapboxgl-ctrl-geocoder .suggestions {
        width: 100% !important;
    }

    .mapboxgl-ctrl-geocoder .suggestions > li {
        width: 100% !important;
        font-size:16px !important;
        padding-bottom: 8px !important;
        font-weight:500;
    }

    .mapboxgl-ctrl-geocoder--pin-right > * {
        top: 14px !important;
    }

    .mapboxgl-ctrl-geocoder--pin-right > button {
        background-color: transparent;
    }

    .mapboxgl-ctrl-geocoder--powered-by {
        display: none !important;
    }



    #gm-custom-mapregiondropdown::-webkit-scrollbar-track
    {
      -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
      border-radius: 10px;
      background-color: #F5F5F5;
    }

    #gm-custom-mapregiondropdown::-webkit-scrollbar
    {
      width: 6px;
      background-color: #F5F5F5;
    }

    #gm-custom-mapregiondropdown::-webkit-scrollbar-thumb
    {
      border-radius: 10px;
      -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
      background-color: #555;
    }

    .ajax-link {
        color: #aaa;
    }
    .ajax-link:hover {
        color: #bbb;
    }

   @media screen and (max-width: 767px) and (min-width: 1px) {
        #content-body {
           margin-top: 0px;
           padding-bottom: 0px;
        }
        html, body {
            letter-spacing: .03125em;
        }
        .content-pane {
           margin-top: 20px;
        }
        #edit_user_mobile {
            font-size: 14px;
        }
        ul#user-files textarea {
            font-size: 14px;
        }
        .row-full-width .col-xs-12 {
            padding-left: 10px;
            padding-right: 10px;
        }
        .field-title {
            font-size: 16px;
            font-weight: 700;
        }
        .form-header-title {
            font-size: 14px;
            font-weight: 500;
        }
        input {
            font-size: 16px;
            font-weight: 300;
        }
        form textarea {
            font-size: 16px;
            font-weight: 300;
        }
        .route-info-section {
            margin-top: 20px;
        }
        .field-title-spacer {
            height: 0px;
        }
        .header-peak-name {
            color: #fff;
        }
        .header-peak {
            font-size: 18px;
            font-weight: 500;
        }
        .close-route-edit {
            margin-right: 0px;
        }
        .header-help {
            color: #999;
            font-size: 11px;
            font-weight: 300;
            margin-left: 10px;
        }
        .field-title {
            font-size: 16px;
            font-weight: 700;
        }
        .summitlog-section {
            margin-top: 20px;
        }
        #edit_peak {
            width: 100px;
        }
        ::-webkit-input-placeholder { font-size: 16px; }
        ::-moz-placeholder { font-size: 16px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 16px; } /* ie */
        input:-moz-placeholder { font-size: 16px; }
        input { font-size: 16px; }

        #gm-custom-mapregiondropdown {
            right: -315px;
           top: 30px;
           max-height: 480px;
       }
    }
    @media screen and (min-width: 768px) {

        #content-body {
           margin-top: 30px;
        }
        .content-pane {
           margin-top: 0px;
        }
        input {
            font-size: 18px;
            font-weight: 300;
        }
        form textarea {
            font-size: 18px;
            font-weight: 300;
        }
        .route-info-section {
            margin-top: 60px;
        }
        .field-title-spacer {
            height: 10px;
        }
        .save-changes-div {
            float: right;
        }
        .summitlog-section {
            margin-top: 30px;
        }

        #gm-custom-mapregiondropdown {
            right: -531px;
            top: 42px;
           max-height: 430px;
       }
    }
    @media screen and (min-width: 768px) and (max-width: 1023px) {
        #edit_peak {
            width: 180px;
        }
        ul#peakroute-files textarea {
            font-size: 16px;
        }
        .header-peak-name {
            color: #fff;
        }
        .header-peak {
            font-size: 18px;
            font-weight: 500;
        }
        .close-route-edit {
            margin-right: 0px;
        }
        .header-help {
            color: #999;
            font-size: 12px;
            font-weight: 300;
            margin-left: 10px;
        }
        .field-title {
            font-size: 18px;
            font-weight: 700;
        }
        .form-header-title {
            font-size: 18px;
            font-weight: 500;
        }
        ::-webkit-input-placeholder { font-size: 16px; }
        ::-moz-placeholder { font-size: 16px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 16px; } /* ie */
        input:-moz-placeholder { font-size: 16px; }
        input { font-size: 16px; }
    }
    @media screen and (min-width: 1024px) {
        #edit_peak {
            width: 210px;
        }
        ul#user-files textarea {
            font-size: 18px;
        }
        .header-peak-name {
            color: #f24100;
        }
        .header-peak {
            font-size: 20px;
            font-weight: 300;
        }
        .close-route-edit {
            margin-right: 20px;
        }
        .header-help {
            color: #999;
            font-size: 14px;
            font-weight: 300;
            margin-left: 10px;
        }
        .field-title {
            font-size: 21px;
            font-weight: 700;
        }
        .form-header-title {
            font-size: 20px;
            font-weight: 300;
        }
        #gm-custom-mapunits {
            right: 142px;
        }
        ::-webkit-input-placeholder { font-size: 18px; }
        ::-moz-placeholder { font-size: 18px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 18px; } /* ie */
        input:-moz-placeholder { font-size: 18px; }
        input { font-size: 18px; }
    }
    @media screen and (min-width: 1px) and (max-width: 1279px) {
         .no-header-container {
           padding-left: 0px;
        }
    }
    ::-webkit-input-placeholder { font-weight: 300; }
    ::-moz-placeholder { font-weight: 300; } /* firefox 19+ */
    :-ms-input-placeholder { font-weight: 300; } /* ie */
    input:-moz-placeholder { font-weight: 300; }
    input { font-weight: 300; }

    @-webkit-keyframes markerPulse {
      from { -webkit-filter: brightness(1.2) saturate(1.5); }
      50% { -webkit-filter: brightness(0.9) saturate(1); }
      to { -webkit-filter: brightness(1.2) saturate(1.5); }
    }
    .marker-pulse {
      -webkit-animation-name: markerPulse;
      -webkit-animation-duration: 3s;
      -webkit-animation-iteration-count: infinite;
    }
    .gm-style-mtc {
        opacity: .8;
    }

    form input[type='text'], form input[type='number'] {
        border-radius: 8px;
    }

    #content-holder, body {
        background-image: none !important;
        background-color: #f6f6f6;
    }

    .map-tooltip-info {
        background-color: rgba(0,0,0,.6);
        -webkit-backdrop-filter: blur(0px) !important;
    }

    .marker_icon:hover, .marker_icon_red:hover, .marker_icon_green:hover, .marker_icon_redgreen:hover, .marker_icon_yellow:hover, .marker_icon_purple:hover, .marker_icon_peak:hover, .marker-icon-hover {
        background-image: url('{% static 'img/<EMAIL>' %}') !important;
        height: 28px;
        width: 28px;
        -webkit-animation-name: markerPulse;
        -webkit-animation-duration: 3s;
        -webkit-animation-iteration-count: infinite;
    }

    @-webkit-keyframes markerPulse {
        from { -webkit-filter: brightness(1.2) saturate(1.5); }
        50% { -webkit-filter: brightness(0.9) saturate(1); }
        to { -webkit-filter: brightness(1.2) saturate(1.5); }
    }

    @media screen and (min-width: 769px) {
        .noUi-horizontal .noUi-handle {
            width: 35px;
            height: 35px;
            left: -24px;
            top: -17px;
            background-color: transparent;
            background-size: contain;
        }
    }

    #gm-custom-mapfilter, #gm-custom-maplayers, #gm-custom-maplegend {
        -moz-box-shadow: 0 0 2px rgba(0,0,0,.1);
        -webkit-box-shadow: 0 0 2px rgba(0,0,0,.1);
        box-shadow: 0 0 0 2px rgba(0,0,0,.1);
    }

    #gm-custom-mapdropdown, #gm-custom-mapbutton {
        opacity: 1;
        webkit-backdrop-filter: blur(5px);
        backdrop-filter: blur(5px);
    }

    #gm-custom-mapunits:hover {
        background-color: transparent !important;
    }

    #gm-custom-mapbutton {
        border: 2px solid rgba(0,0,0,0.15);
    }

    #gm-custom-mapdropdown {
        box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 8px 0px;
    }

    #gm-custom-mapbutton {
        width: 180px;
        margin-left: 90px;
        border-top-right-radius: 8px;
        border-top-left-radius: 8px;
        border-bottom-right-radius: 8px;
        border-bottom-left-radius: 8px;
    }

    #gm-custom-mapbutton:hover {
        border-bottom-right-radius: 0px;
        border-bottom-left-radius: 0px;
    }

    #gm-custom-mapdropdown {
        width: 179px;
        margin-left: 93px;
        border-bottom-right-radius: 8px;
        border-bottom-left-radius: 8px;
    }

    #gm-custom-mapoption-terrain, #gm-custom-mapoption-natatl, #gm-custom-mapoption-outdoors, #gm-custom-mapoption-streets, #gm-custom-mapoption-topo, #gm-custom-mapoption-satstreets, #gm-custom-mapoption-sat {
        width: 176px;
        margin-left: -2px;
        border-bottom: solid 1px #f2f2f2;
    }

    .gm-custom-mapoption-region {
        width: 260px;
        margin-left: -1px;
        border-bottom: solid 1px #f2f2f2;
    }

    #gm-custom-mapoption-3d {
        width: 176px;
        margin-left: -2px;
        border-bottom: solid 1px #f2f2f2;
    }

    #gm-custom-mapoption-3d {
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
    }

    #gm-custom-mapoption-streets:hover {
        background-color: rgb(235, 235, 235) !important;
        color: rgb(0, 0, 0) !important;
    }

    .gm-custom-mapoption-region:hover {
        background-color: #ebebeb!important;
        color: #000!important;
    }

    /*tooltip animations*/
    .scale-in-tl{
        -webkit-animation: scale-in-tl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-tl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-tl {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-in-tl {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
    }

    .scale-out-tl {
        -webkit-animation: scale-out-tl .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-tl .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-tl {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-out-tl {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
    }

    .scale-in-tm{
        -webkit-animation: scale-in-tm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-tm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-tm {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-in-tm {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
    }

    .scale-out-tm {
        -webkit-animation: scale-out-tm .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-tm .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-tm {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-out-tm {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
    }

    .scale-in-tr{
        -webkit-animation: scale-in-tr .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-tr .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-tr {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-in-tr {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
    }

    .scale-out-tr {
        -webkit-animation: scale-out-tr .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-tr .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-tr {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-out-tr {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
    }

    /*bottom*/
    .scale-in-bl{
        -webkit-animation: scale-in-bl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-bl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-bl {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-in-bl {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
    }

    .scale-out-bl {
        -webkit-animation: scale-out-bl .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-bl .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-bl {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-out-bl {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
    }

    .scale-in-bm{
        -webkit-animation: scale-in-bm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-bm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-bm {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-in-bm {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
    }

    .scale-out-bm {
        -webkit-animation: scale-out-bm .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-bm .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-bm {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-out-bm {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
    }

    .scale-in-br{
        -webkit-animation: scale-in-br .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-br .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-br {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-in-br {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
    }

    .scale-out-br {
        -webkit-animation: scale-out-br .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-br .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-br {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-out-br {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
    }

</style>

    <div class="container" style="padding-left: 0px; padding-right: 0px;">

        <div class="content-pane" style="background-color: #f6f6f6; padding-top: 40px;">

            <input type="hidden" name="item_id" value="">
            <input type="hidden" name="location_name" id="location_name" value="">
            <input type="hidden" name="location_city" id="location_city" value="">
            <input type="hidden" name="location_state" id="location_state" value="">
            <input type="hidden" name="location_country" id="location_country" value="">
            <input type="hidden" name="location_area_1" id="location_area_1" value="">
            <input type="hidden" name="location_area_2" id="location_area_2" value="">

            <div class="row">

                <div class="col-md-4">

                    <div class="row row-full-width" style="padding-bottom: 20px;">
                        <div class="col-md-12">
                            <span class="field-title">Peak name</span>
                            <div class="field-title-spacer"></div>
                            <fieldset class="peakName">
                                <input value="{{ peak.name }}" type="text" name="peak-name" id="peak-name" style="width: 50%; min-width: 340px; padding: 10px;" placeholder="leave blank if no official name..."></input>
                            </fieldset>
                        </div>
                    </div>

                    <div class="row row-full-width summitlog-section"  style="padding-bottom: 20px;">
                        <div class="col-md-12">
                            <span class="field-title">Elevation</span>
                            <div class="field-title-spacer"></div>
                            <fieldset>
                                <div>
                                    <input value="{% if peak.elevation %}{{ peak.elevation|floatformat }}{% endif %}" type="number" name="peak-elevation" id="peak-elevation" style="width: 125px; padding: 10px;" placeholder="in feet...">
                                    <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                        <label class="btn active">
                                        <input type="radio" name="peak-elevation-units" id="peak-elevation-ft" value="ft" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  ft</span>
                                        </label>
                                        <label class="btn" style="margin-left: 5px;">
                                        <input type="radio" name="peak-elevation-units" id="peak-elevation-m" value="m"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> m</span>
                                        </label>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                    <div class="row row-full-width summitlog-section" style="padding-bottom: 20px;">
                        <div class="col-md-12">
                            <span class="field-title">Prominence<span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">optional &bull; <a style="cursor: pointer;" data-toggle="modal" data-target="#about-prominence">about</a></span></span>
                            <div class="field-title-spacer"></div>
                            <fieldset>
                                <div>
                                    <input value="{% if peak.prominence or peak.prominence == 0 %}{{ peak.prominence|floatformat }}{% endif %}" type="number" name="peak-prominence" id="peak-prominence" style="width: 125px; padding: 10px;" placeholder="in feet...">
                                    <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                        <label class="btn active">
                                        <input type="radio" name="peak-prominence-units" id="peak-prominence-ft" value="ft" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  ft</span>
                                        </label>
                                        <label class="btn" style="margin-left: 5px;">
                                        <input type="radio" name="peak-prominence-units" id="peak-prominence-m" value="m"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> m</span>
                                        </label>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                    <div class="row row-full-width summitlog-section" style="padding-bottom: 20px;">
                        <div class="col-md-12">
                            <span class="field-title">Range<span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">optional</span></span>
                            <div class="field-title-spacer"></div>
                            <fieldset class="peakRange">
                                <input value="{% if peak.range %}{{ peak.range }}{% endif %}" type="text" name="peak-range" id="peak-range" style="width: 50%; min-width: 340px; padding: 10px;" placeholder="enter mountain range name..."></input>
                            </fieldset>
                        </div>
                    </div>

                </div>

                <div class="col-md-8">

                    <div class="row row-full-width summitlog-section" style="margin-top: 0px; padding-bottom: 20px;">
                        <div class="col-md-12">
                            <span class="field-title">Summit location<span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;"><span class="hidden-xs">adjust map so the <img class="marker-pulse" style="margin-bottom: 5px;" src="{% static 'img/<EMAIL>' %}"> is on the summit</span><span class="hidden-sm hidden-md hidden-lg">put the <img style="margin-bottom: 5px;" src="{% static 'img/<EMAIL>' %}"> on the summit</span></span></span>
                            <div class="field-title-spacer"></div>



<div>
                        <div id="geocoder" class="geocoder"></div>

                            <div style="float:left; color: #999; font-size: 14px; padding:10px;">ex: Seattle or 47.601822, -122.332312</div>

                        </div>
                        </div>
                    </div>

                    <div class="row row-full-width" style="padding-bottom: 20px;">
                        <div class="col-md-12">
                            <div id="peak-map-col" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover;">
                                <div id="map-canvas" style="width: 100%; height: 100%;">
                                    <div id="gm-custom-mapunits" class="gmnoprint gm-style-mtc" style="display: none; margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 100px; top: 0px;">
                                        <div id="gm-custom-mapunitsbutton" draggable="false" title="Change map units" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: center; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                                            <span id="gm-custom-mapunitsbutton-label-feet" class="gm-custom-mapunits-selected">Feet</span> | <span id="gm-custom-mapunitsbutton-label-meters" class="gm-custom-mapunits-unselected">Meters</span>
                                        </div>
                                    </div>
                                    <div id="gm-custom-map3d" class="gmnoprint gm-style-mtc" style="display: none; margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; right: 0px; background-color: #fff; opacity: 1;">
                                        <a id="peak-search-3d" style="cursor: pointer;">3D</a>
                                    </div>
                                    <div id="gm-custom-maplegend" class="gmnoprint gm-style-mtc" style="display: none; margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; right: 0px; background-color: #fff; opacity: 1;">
                                        <a id="peak-search-legend" style="cursor: pointer;"><i style="margin-left: 5px;" class="fas fa-eye"></i></a>
                                    </div>
                                    <div id="gm-custom-mapfilter" class="gmnoprint gm-style-mtc hidden-lg hidden-md" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; top: 5px; right: 0px; background-color: #fff; opacity: 1;">
                                        <a id="peak-search-mobile" style="cursor: pointer;"><i class="fa fa-filter"></i></a>
                                    </div>
                                    <div id="gm-custom-maplayers" class="gmnoprint gm-style-mtc hidden-lg hidden-md" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; top: 53px; right: 0px; background-color: #fff; opacity: 1;">
                                        <a id="peak-search-layers" style="cursor: pointer;"><i class="fas fa-layer-group"></i></a>
                                    </div>
                                    <div id="gm-custom-maptype" class="gmnoprint" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 180px; right: 50%; top: 0px;">
                                        <div id="gm-custom-mapbutton" class="hidden-xs hidden-sm" draggable="false" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: left; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); height: 54px; padding: 8px; -webkit-background-clip: padding-box; background-clip: padding-box; font-weight: 500;">
                                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>
                                        </div>
                                    </div>
                                    <div id="gm-custom-mapdropdown-container" class="gmnoprint" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; right: 50%; margin-right: 191px; top: 0px;">
                                        <div id="gm-custom-mapdropdown" style="z-index: 10; padding-left: 2px; padding-right: 2px; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; position: absolute; top: 50px; left: 0px; right: 0px; text-align: left; display: none;">
                                            <div id="gm-custom-mapoption-terrain" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>
                                            </div>
                                            <div id="gm-custom-mapoption-natatl" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural Atlas <span style="font-size: 10px;">(US)</span></div>
                                            </div>
                                            <div id="gm-custom-mapoption-outdoors" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">OpenTopoMap</div>
                                            </div>
                                            <div id="gm-custom-mapoption-streets" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets</div>
                                            </div>
                                            <div id="gm-custom-mapoption-topo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500; width: 177px; border-right: 1px solid #aaa;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Government Topo <span style="font-size: 10px;">&nbsp;<i class="fa fa-caret-right" aria-hidden="true"></i></span></div>
                                            </div>
                                            <div id="gm-custom-mapoption-satstreets" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite</div>
                                            </div>
                                            <div id="gm-custom-mapoption-sat" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite Topo</div>
                                            </div>
                                        </div>
                                        <div id="gm-custom-mapregiondropdown" style="z-index: 10; padding-left: 0px; padding-right: 2px; position: absolute; text-align: left; overflow: hidden auto; display: none;">
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-argentina-ign-50k" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ar.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Argentina (IGN - 50K)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-argentina-ign-100k" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ar.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Argentina (IGN - 100K)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-nsw" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - NSW (SIX)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-qld" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - QLD (QTopo)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-sa" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - SA</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-ts" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - TAS (LIST)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-vic" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - VIC (VicMap)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-austria-bergfex" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/at.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Austria (BergFex)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-austria-bev" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/at.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Austria (BEV)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-belgium-ngi" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/be.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Belgium (NGI)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-brazil" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/br.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Brazil (IBGE)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-canada-caltopo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ca.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada (NRCAN)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-canada-on" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ca.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada - ON (OBM)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-canada-qc" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ca.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada - QC (MERN)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-croatia-dgu" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/hr.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Croatia (DGU)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-czechia-cuzk" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/cz.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Czechia (ČÚZK)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-finland-nls" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/fi.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Finland (NLS)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-france-ign" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/fr.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">France (IGN)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-germany-oa" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/de.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Germany (OutdoorActive)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-hongkong-landsd" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/hk.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Hong Kong (LandsD)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-iceland-caltopo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/is.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Iceland (CalTopo)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-iceland-new" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/is.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Iceland (Landmælingar)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-israel-hikingosm" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/il.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Israel (Hiking OSM)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-japan-gsi" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/jp.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Japan (GSI)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-luxembourg" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/lu.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Luxembourg (ACT)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-mexico-inegi" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/mx.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Mexico (INEGI)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-newzealand-linz" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/nz.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">New Zealand (LINZ)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-new" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/no.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway (Kartvertek)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-kartverket" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/no.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway (Old Kartvertek)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-janmayen" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/no.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway - Jan Mayen (NPI)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-svalbard" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/no.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway - Svalbard (NPI)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-philippines-namria" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ph.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Philippines (NAMRIA)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-poland-geoportal" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/pl.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Poland (Geoportal)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-slovakia-dgu" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/sk.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Slovakia (DGU)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-slovenia-prostor" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/si.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Slovenia (ProStor)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-spain-ign" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/es.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Spain (IGN)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-spain-cataluna" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/es.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Spain - Cataluña (ICGC)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-sweden-sgu" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/se.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Sweden (SGU)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-switzerland-swisstopo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ch.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Switzerland (swisstopo)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-taiwan-nlsc" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/tw.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Taiwan (NLSC)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-uk-os" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/gb.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">United Kingdom (OS)</div>
                                            </div>
                                            <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-us-caltopo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/us.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">United States (USGS)</div>
                                            </div>
                                        </div>
                                        <div id="gm-custom-maplegend-dropdown" style="z-index: 10; border: 2px solid rgba(0,0,0,0.15); position: absolute; width: 270px; top: 52px; left: 40px; right: 0px; text-align: left; border-radius: 8px; display: none;">
                                            <div id="gm-custom-maplegend-highest" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500; border-top-left-radius: 8px; border-top-right-radius: 8px;">
                                                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Highest peak on map</div>
                                            </div>
                                            <div id="gm-custom-maplegend-yoursummits" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Climbed by you<div style="float: right; display: none;"><label class="switch"><input id="map-summits-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                                            </div>
                                            <div id="gm-custom-maplegend-yourattempts" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Attempted by you<div style="float: right; display: none;"><label class="switch"><input id="map-attempts-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                                            </div>
                                            <div id="gm-custom-maplegend-unclimbed" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Unclimbed by you<div style="float: right; display: none;"><label class="switch"><input id="map-unclimbed-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                                            </div>
                                            <div id="gm-custom-maplegend-yourkings" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your King of the Mountains<div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}"><label class="switch"><input id="map-kom-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                                            </div>
                                            <div id="gm-custom-maplegend-yourstewards" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your Summit Stewards<div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}"><label class="switch"><input id="map-stewards-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                                            </div>
                                            <div id="gm-custom-maplegend-yourfirstascents" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your First Ascents<div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}"><label class="switch"><input id="map-firstascents-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                                            </div>
                                            <div id="gm-custom-maplegend-classics" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Classic peaks<div style="float: right;"><label class="switch"><input id="map-classics-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                                            </div>
                                            <div id="gm-custom-maplegend-challenges" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Peak Challenges<div style="float: right;"><label class="switch"><input id="map-challenges-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                                            </div>
                                            <div id="gm-custom-maplegend-photos" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;">
                                                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Photos<div style="float: right;"><label class="switch"><input id="map-photos-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="message_map_div" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px; display: none;">
                                        <div id="message_map" draggable="false" style="direction: ltr; overflow: hidden; text-align: left; position: relative; color: rgb(86, 86, 86); font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; border-bottom-left-radius: 2px; border-top-left-radius: 2px; border-bottom-right-radius: 2px; border-top-right-radius: 2px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">

                                        </div>
                                    </div>

                                </div>
                                <div id="marker-tooltip" data-url="" data-index="" style="z-index: 2; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; cursor: pointer; opacity: 0; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>
                                <div id="center-peak-marker" style="height: 21px; width: 21px; position: absolute; top: 100px; left: 100px;"><img class="marker-pulse" src="{% static 'img/<EMAIL>' %}" style="width: 21px; height: 21px;"></div>
                                <input type="hidden" name="peak-lat" id="peak-lat" value="">
                                <input type="hidden" name="peak-lng" id="peak-lng" value="">
                            </div>
                        </div>
                    </div>

                </div>

            </div>

        </div>

    </div>

<div class="about-prominence-modal modal fade" id="about-prominence" tabindex="-1" role="dialog" aria-labelledby="about-prominence-label" style="display: none;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
              <h4 class="modal-title" id="about-prominence-label">A note on <span style="color: #f24100;">prominence</span></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12" style="margin-bottom: 20px; font-size: 16px; line-height: 32px;">
                        <p style="line-height: 32px; margin-bottom: 30px;">A peak’s prominence, also known as topographic prominence or relative height, is a measure of how distinct a peak is from other peaks. It’s defined as the vertical distance between a peak and the lowest contour line surrounding that peak and no higher peak. Prominence is a popular metric for peaks for two reasons: 1) it’s objective and relatively easy to calculate, and 2) higher prominence peaks are more likely to be interesting with higher independence vs. peaks with lower prominence.</p>
                        <p style="line-height: 32px;">Note that prominence is not the same thing as a peak’s vertical drop, which is usually extremely difficult to calculate because a peak’s base elevation can be highly subjective. Instead, peakery shows vertical gain for specific routes up peaks.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="thanks-modal modal fade" id="thanks-modal" tabindex="-1" role="dialog" aria-labelledby="thanks-modal-label" style="display: none;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
              <h4 class="modal-title" id="thanks-modal-label">Thanks!</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12" style="margin-bottom: 20px; font-size: 16px; line-height: 32px;">
                        <p style="line-height: 32px; margin-bottom: 30px;">Thanks for submitting this peak to peakery.</p>
                        <p style="line-height: 32px; margin-bottom: 30px;">We’ll review and let you know when it’s approved.</p>
                        <div style="text-align: center;"><button type="button" class="btn btn-secondary" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">OK</span></button></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="error-modal modal fade" id="error-modal" tabindex="-1" role="dialog" aria-labelledby="error-modal-label" style="display: none;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
              <h4 class="modal-title" id="error-modal-label">Error!</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12" style="margin-bottom: 20px; font-size: 16px; line-height: 32px;">
                        <p style="line-height: 32px; margin-bottom: 30px;">Looks like peakery already has that peak. Please try again.</p>
                        <div style="text-align: center;"><button type="button" class="btn btn-secondary" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">OK</span></button></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style type="text/css">
    .ui-autocomplete {
        max-height: 100px;
        overflow-y: auto;
        /* prevent horizontal scrollbar */
        overflow-x: hidden;
        /* add padding to account for vertical scrollbar */
        padding-right: 20px;
    }
        /* IE 6 doesn't support max-height
       * we use height instead, but this forces the menu to always be this tall
       */
    * html .ui-autocomplete {
        height: 100px;
    }
</style>

<script type="text/javascript">

var map;
var viewer;
var topo;
var outdoors;
var center = null;
var map_bounds;
var init = false;
var photos_displayed = 0;
var photos_page = 1;
var photos = [];
var map_adjusted = false;
var pageX, pageY, mapX, mapY;
var iconstyle;

var markersArray = [];

function hideMapTooltip() {
    if ($('#marker-tooltip').hasClass('scale-in-tl')) {
        $('#marker-tooltip').addClass('scale-out-tl').removeClass('scale-in-tl');
    } else if ($('#marker-tooltip').hasClass('scale-in-tm')) {
        $('#marker-tooltip').addClass('scale-out-tm').removeClass('scale-in-tm');
    } else if ($('#marker-tooltip').hasClass('scale-in-tr')) {
        $('#marker-tooltip').addClass('scale-out-tr').removeClass('scale-in-tr');
    } else if ($('#marker-tooltip').hasClass('scale-in-bl')) {
        $('#marker-tooltip').addClass('scale-out-bl').removeClass('scale-in-bl');
    } else if ($('#marker-tooltip').hasClass('scale-in-bm')) {
        $('#marker-tooltip').addClass('scale-out-bm').removeClass('scale-in-bm');
    } else if ($('#marker-tooltip').hasClass('scale-in-br')) {
        $('#marker-tooltip').addClass('scale-out-br').removeClass('scale-in-br');
    }
}

function loadPeaks(peak_id) {

    //if (!init) {
    if (1 == 1) {

        //get map bounds
        var bounds = map.getBounds();

        var counter = 0;
        var strTemp = '';
        var LatLngList = [];

        var params = '';
        params = params + '&q=';
        params = params + '&n=';
        params = params + '&elev_min=0';
        params = params + '&elev_max=29500';
        params = params + '&prom_min=0';
        params = params + '&prom_max=29500';
        params = params + '&summits_min=0';
        params = params + '&summits_max=500';
        params = params + '&difficulty_min=1';
        params = params + '&difficulty_max=5';
        params = params + '&lat=';
        params = params + '&lng=';
        params = params + '&bounds=' + bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng;

        //update hidden parameters
        map_bounds = bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng;

        if (params.length > 0) params = '?' + params.slice(-1 * (params.length - 1));
        var byRegion = false;
        var totalPeaks = 0;
        $.getJSON('{% url "peaks_map" %}' + params, function (data) {
            $.each(data, function (key, val) {
                var currentRequest = true;
                if (key == 'parameters') {
                    $.each(val, function (parameterkey, parameterval) {
                        if (parameterval.bounds != map_bounds) currentRequest = false;
                    });
                }

                if (!currentRequest) {
                    return false;
                }

                if (key == 'peaks') {

                    var havePeaks = false;

                    $.each(val, function (peakkey, peakval) {

                        if (!havePeaks) {

                            //first time through, delete highest peak marker and remove any markers not on map
                            deletehighest();
                            //delete markers out of margins
                            delete_old_markers(val);

                        }

                        havePeaks = true;

                        //build country string
                        var country = '';
                        $.each( peakval.country, function( countrykey, countryval ) {
                            country = country + '<div><a href="' + countryval.country_slug + '/">' + countryval.country_name + '</a></div>';
                        });
                        if (country == '') {
                            country = 'Unknown Country';
                        }

                        //build region string
                        var region = '';
                        var mobile_region = '';
                        var region_bull_class = 'hidden-lg hidden-md hidden-sm hidden-xs';
                        $.each( peakval.region, function( regionkey, regionval ) {
                            region_bull_class = '';
                            region = region + '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.region_name + ', ' + regionval.country_name + '</a></div>';
                            mobile_region = '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.country_name + '</a></div>';
                        });
                        if (region == '') {
                            region = country;
                            mobile_region = country;
                        }

                        //build challenges string
                        var challenges = '';
                        var challenge_count = peakval.challenge_count;
                        if (challenge_count > 0) {
                            challenges = ' &bull; <span>' + challenge_count + ' <i class="fa fa-trophy"></i></span>';
                        }

                        //buld distance string
                        var distance = '';

                        //build summits string
                        var summits, tooltip_your_summits;
                        if (peakval.summit_count > 0) {
                            summits = '<a href="/' + peakval.slug + '/summits/">' + peakval.summit_count + '</a>';
                        } else {
                            summits = '<a href="/' + peakval.slug + '/summits/">0</a><br /><a href="/' + peakval.slug + '/">Be the first!</a>';
                        }
                        if (peakval.your_summits > 0) {
                            summits = summits + '<br /><span style="color: #3fc663; font-weight: 500;">' + peakval.your_summits + 'x by you</span>';
                            tooltip_your_summits = '&nbsp;<span style="color: #3fc663; font-weight: 500;">(You ' + peakval.your_summits + 'x)</span>';
                        } else {
                            tooltip_your_summits = '';
                        }

                        //tooltip vars
                        var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left, tooltip_total_width, tooltip_total_height;

                        //show tooltip badges?
                        var showClassic = 'display: none;';
                        var showChallenges = 'display: none;';
                        var showYourSummits = 'display: none;';
                        if (peakval.is_classic == 'True') {
                            showClassic = '';
                        }
                        if (peakval.challenge_count > 0) {
                            showChallenges = '';
                        }
                        if (peakval.your_summits > 0) {
                            showYourSummits = '';
                        }

                        peaklist_html = '';

                        //show feet or meters?
                        var showFeet = 'display: none;';
                        var showMeters = 'display: none;';
                        if ($('#bt_showinmeters').hasClass('meters')) {
                            showMeters = '';
                        } else {
                            showFeet = '';
                        }

                        //build tooltip string
                        if (peakval.thumbnail_url.indexOf('default.png.350x245_q95_crop.jpg') < 0) {
                            tooltip_html = '<img class="map-tooltip-img hover-photos-hover" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px;" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-peak-badges" style="font-weight:500; top: 115px; width: 220px; position: absolute; background-color: transparent; background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%); padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; bottom: -35px; height: 100px; background-color: transparent !important;"><div style="margin-left: -8px; margin-top: 43px; height: 55px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 210px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; padding-left: 10px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div>';
                            tooltip_width = 220;
                            tooltip_height = 165;
                            tooltip_total_width = 250;
                            tooltip_total_height = 230;
                            peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-photos" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: -60px;"><img class="map-tooltip-img hover-photos-hover" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; position: relative;" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-peak-badges" style="font-weight:500; top: -52px; width: 220px; position: relative; background-color: transparent; background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%); padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; position: relative; left: inherit; top: -65px; width: 220px; height: 100px; background-color: transparent !important;"><div class="peak-listitem-footer" style="margin-left: -8px; margin-top: 43px; height: 55px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff;"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 210px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; padding-left: 10px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></div></a>';
                        } else {
                            if (peakval.is_classic == 'True' || peakval.challenge_count > 0 || peakval.your_summits > 0) {
                                tooltip_html = '<div class="map-tooltip-peak-badges" style="font-weight:500; top: 4px; z-index: 2; width: 220px; position: absolute; background-color: transparent; padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="height: 75px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="margin-top: 25px; color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div>';
                                tooltip_width = 220;
                                tooltip_height = 75;
                                tooltip_total_width = 250;
                                tooltip_total_height = 105;
                                peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-photos" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: 5px;"><div class="map-tooltip-peak-badges" style="font-weight:500; top: 4px; z-index: 2; width: 220px; position: relative; background-color: transparent; padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info peak-listitem-footer" style="height: 80px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; position: relative;"><div class="map-tooltip-peak-name ellipsis" style="margin-top: 25px; color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></a>';
                            } else {
                                tooltip_html = '<div class="map-tooltip-info" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div>';
                                tooltip_width = 220;
                                tooltip_height = 50;
                                tooltip_total_width = 250;
                                tooltip_total_height = 80;
                                peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-photos" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: 5px;"><div class="map-tooltip-info peak-listitem-footer" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; position: relative; height: 54px;"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></a>';
                            }
                        }

                        var tooltip_url = '/' + peakval.slug;

                        var latLng = new mapboxgl.LngLat(peakval.lng, peakval.lat);

                        if (counter == 0) {
                            //highest peak gets red icon
                            iconstyle = 'marker_icon_red';
                        } else if (peakval.your_summits > 0) {
                            //if you have summited then green icon
                            iconstyle = 'marker_icon_green';
                        } else if (peakval.your_attempts > 0) {
                            //if you have attempted then yellow icon
                            iconstyle = 'marker_icon_yellow';
                        } else {
                            iconstyle = 'marker_icon';
                        }

                        var is_draggable = false;

                        //check if already exist so don't put again
                        var exists = false;
                        for (i = markersArray.length-1; i>=0; i--){
                            if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng)){
                                exists = true ;
                                //if the highest is in the actual viewport, not as the highest, delete it
                                if (iconstyle=='marker_icon_red' || iconstyle=='marker_icon_redgreen' || iconstyle=='marker_icon_green') {
                                    markersArray[i].remove();
                                    markersArray.splice(i,1);
                                    exists = false;
                                }
                            }
                        }

                        //if we are only showing one peak_id, assume other peaks already exist so don't show them
                        if (peak_id == null) {
                            //do nothing for now
                        } else {
                            if (peakval.id != peak_id) {
                                //exists = true;
                            }
                        }

                        if (!exists) {
                            var latLng = [peakval.lng, peakval.lat];
                            //add marker
                            //create an HTML element for the marker
                            var el = document.createElement('div');
                            el.className = iconstyle;

                            el.addEventListener('click', function(e) {
                                if (isTouchDevice()) {
                                    hideMapTooltip();

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();
                                    var showClass = 'scale-in-';
                                    var hideClass = 'scale-out-';

                                    var markerXOffset = document.getElementById('map-canvas').getBoundingClientRect().x;
                                    var markerX = this.getBoundingClientRect().x + 14 - markerXOffset;

                                    var markerYOffset = document.getElementById('map-canvas').getBoundingClientRect().y;
                                    var markerY = this.getBoundingClientRect().y + 14 - markerYOffset;

                                    if (markerY < (bottom / 2)) {
                                        marker_top = markerY;
                                        showClass = showClass + 't';
                                        hideClass = hideClass + 't';
                                    } else {
                                        marker_top = markerY - tooltip_total_height - 15;
                                        showClass = showClass + 'b';
                                        hideClass = hideClass + 'b';
                                    }

                                    if (markerX < (right / 3)) {
                                        marker_left = markerX;
                                        showClass = showClass + 'l';
                                        hideClass = hideClass + 'l';
                                    } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                        marker_left = markerX - (tooltip_total_width / 2);
                                        showClass = showClass + 'm';
                                        hideClass = hideClass + 'm';
                                    } else {
                                        marker_left = markerX - tooltip_total_width;
                                        showClass = showClass + 'r';
                                        hideClass = hideClass + 'r';
                                    }

                                    $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    });
                                    $('#marker-tooltip').removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                    $('#marker-tooltip').addClass(showClass);
                                    $('#marker-tooltip').data('url', marker.properties.tooltipUrl);
                                    if ($('#bt_showinmeters').hasClass('meters')) {
                                        $('#marker-tooltip').find('.peak-elevation-feet').hide();
                                        $('#marker-tooltip').find('.peak-elevation-meters').show();
                                    } else {
                                        $('#marker-tooltip').find('.peak-elevation-meters').hide();
                                        $('#marker-tooltip').find('.peak-elevation-feet').show();
                                    }
                                } else {
                                    //console.log(peakval.slug);
                                    location = '/' + peakval.slug + '/';;
                                }
                                e.stopPropagation();
                            });

                            el.addEventListener('mouseover', function(e) {

                                if (!isTouchDevice()) {

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();
                                    var showClass = 'scale-in-';
                                    var hideClass = 'scale-out-';

                                    var markerXOffset = document.getElementById('map-canvas').getBoundingClientRect().x;
                                    var markerX = this.getBoundingClientRect().x + 14 - markerXOffset;

                                    var markerYOffset = document.getElementById('map-canvas').getBoundingClientRect().y;
                                    var markerY = this.getBoundingClientRect().y + 14 - markerYOffset;

                                    if (markerY < (bottom / 2)) {
                                        marker_top = markerY;
                                        showClass = showClass + 't';
                                        hideClass = hideClass + 't';
                                    } else {
                                        marker_top = markerY - tooltip_total_height - 15;
                                        showClass = showClass + 'b';
                                        hideClass = hideClass + 'b';
                                    }

                                    if (markerX < (right / 3)) {
                                        marker_left = markerX;
                                        showClass = showClass + 'l';
                                        hideClass = hideClass + 'l';
                                    } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                        marker_left = markerX - (tooltip_total_width / 2);
                                        showClass = showClass + 'm';
                                        hideClass = hideClass + 'm';
                                    } else {
                                        marker_left = markerX - tooltip_total_width;
                                        showClass = showClass + 'r';
                                        hideClass = hideClass + 'r';
                                    }

                                    $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    }).addClass(showClass).removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                    $('#marker-tooltip').data('url', marker.properties.tooltipUrl);
                                    if ($('#bt_showinmeters').hasClass('meters')) {
                                        $('#marker-tooltip').find('.peak-elevation-feet').hide();
                                        $('#marker-tooltip').find('.peak-elevation-meters').show();
                                    } else {
                                        $('#marker-tooltip').find('.peak-elevation-meters').hide();
                                        $('#marker-tooltip').find('.peak-elevation-feet').show();
                                    }
                                }
                                e.stopPropagation();

                            });

                            el.addEventListener('mouseout', function(e) {
                                if (isTouchDevice()) {
                                    //$('#marker-tooltip').hide();
                                } else {
                                    hideMapTooltip();
                                }
                            });

                            var marker = new mapboxgl.Marker(el)
                                .setLngLat(latLng)
                                .setOffset([-5, -10])
                                .setDraggable(is_draggable);

                            marker.properties = {};
                            marker.properties.tooltipContent = tooltip_html;
                            marker.properties.tooltipUrl = tooltip_url;
                            marker.properties.iconstyle = iconstyle;
                            marker.properties.peakid = peakval.id;

                            markersArray.push(marker);
                            LatLngList.push(latLng);

                        }

                        counter++;
                    });

                    //add markers to map in reverse order so highest on top
                    for (var i = markersArray.length - 1; i >= 0; --i) {
                        markersArray[i].addTo(map);
                    }

                    if (!havePeaks) {
                        //didn't have any peaks, so remove all markers
                        delete_old_markers(val);
                    }
                }
            });
        });

    }

}

function initialize() {

    var userLat = readCookie('map_lat');
    var userLng = readCookie('map_lng');
    var userZoom = readCookie('map_zoom');

    var mapDiv = document.getElementById('map-canvas');

    if (userLat != '' && userLat != null && userLng != '' && userLng != null) {
        var latLng = [userLng, userLat];
    } else {
        var latLng = [0, 0];
    }

    if (userZoom != '' && userZoom != null) {
        var mapZoom = parseInt(userZoom);
    } else {
        var mapZoom = 2;
    }

    var LatLngList = [];

    if (isTouchDevice()) {
        map = new mapboxgl.Map({
            container: mapDiv, // HTML container id
            style: mapStyle, // style URL
            center: latLng, // starting position as [lng, lat]
            zoom: mapZoom,
            scrollWheelZoom: true
        });
    } else {
        map = new mapboxgl.Map({
            container: mapDiv, // HTML container id
            style: mapStyle, // style URL
            center: latLng, // starting position as [lng, lat]
            zoom: mapZoom,
            scrollWheelZoom: true
        });
        scale = new mapboxgl.ScaleControl({maxWidth: 120, unit: 'imperial'});
        map.addControl(scale, 'bottom-right');
        var nav = new mapboxgl.NavigationControl({showCompass: false});
        map.addControl(nav, 'bottom-right');
        // disable map rotation using right click + drag
        map.dragRotate.disable();
        // disable map rotation using touch rotation gesture
        map.touchZoomRotate.disableRotation();
    }

    function calculateCenter() {
      center = map.getCenter();
    }

    map.on('resize', function(e) {
      map.setCenter(center);
    });

    map.on('moveend', function () {
        loadPeaks('{{ peak.id }}');
        calculateCenter();
        if (!init) {
            //pre-select meters if map outside US
            var map_center = map.getCenter();
            if (map_center.lng > -124.733333 && map_center.lng < -66.950000 && map_center.lat < 49.383333 && map_center.lat > 25.116667) {
                //in usa
            } else {
                //outside usa
                $('#peak-elevation-m').click();
                $('#peak-prominence-m').click();
                setElevationMeters();
                setProminenceMeters();
            }
            init = true;
        }
        var mapCenter = map.getCenter();
        var latitude = mapCenter.lat;
        var longitude = mapCenter.lng;
        $('#peak-lat').val(latitude);
        $('#peak-lng').val(longitude);
        map_adjusted = true;
        $('#edit_peak').prop('disabled', false);
    });

    map.on('click', function(e) {
        if (isTouchDevice()) {
            hideMapTooltip();
        } else {
            hideMapTooltip();
        }
    });

    map.on('dragstart', function(e) {
        if (isTouchDevice()) {
            hideMapTooltip();
        } else {
            hideMapTooltip();
        }
    });

    map.on('load', function () {
        loadPeaks('{{ peak.id }}');
        calculateCenter();
        if (!init) {
            //pre-select meters if map outside US
            var map_center = map.getCenter();
            if (map_center.lng > -124.733333 && map_center.lng < -66.950000 && map_center.lat < 49.383333 && map_center.lat > 25.116667) {
                //in usa
            } else {
                //outside usa
                $('#peak-elevation-m').click();
                $('#peak-prominence-m').click();
                setElevationMeters();
                setProminenceMeters();
            }
            init = true;
        }
        var mapUnits = readCookie('map_units');
        if (mapUnits != '') {
            toggleMapUnits(mapUnits);
        }
        setMapControls();
    });

    map.on('click', function () {
        map.scrollWheelZoom.enable();
    });


    /* Given a query in the form "lng, lat" or "lat, lng"
    * returns the matching geographic coordinate(s)
    * as search results in carmen geojson format,
    * https://github.com/mapbox/carmen/blob/master/carmen-geojson.md */
    const coordinatesGeocoder = function (query) {
        // Match anything which looks like
        // decimal degrees coordinate pair.
        const matches = query.match(
            /^[ ]*(?:Lat: )?(-?\d+\.?\d*)[, ]+(?:Lng: )?(-?\d+\.?\d*)[ ]*$/i
        );
        if (!matches) {
            return null;
        }

        function coordinateFeature(lng, lat) {
            return {
                center: [lng, lat],
                geometry: {
                    type: 'Point',
                    coordinates: [lng, lat]
                },
                place_name:  'Lat: ' + lat + ', Long: ' + lng,
                place_type: ['coordinate'],
                properties: {},
                type: 'Feature'
            };
        }

        const coord1 = Number(matches[1]);
        const coord2 = Number(matches[2]);
        const geocodes = [];

        geocodes.push(coordinateFeature(coord1, coord2));

        return geocodes;
    };


        // Add the control to the map.
        const geocoder = new MapboxGeocoder({
            accessToken: mapboxgl.accessToken,
            mapboxgl: mapboxgl,
            types: 'country, region, place',
            reverseGeocode: true,
            localGeocoder: coordinatesGeocoder,
            flyTo: { duration: 0 },
            limit: 10,
            marker: false,
            render: function (item) {
                // extract the item's maki icon or use a default
                const maki = item.properties.maki || 'marker';
                return `<div class='geocoder-dropdown-item'>
                <span class='geocoder-dropdown-text'>
                ${item.place_name.replace(', United States','')}
                </span>
                </div>`;
            }
        });

        geocoder.on('results', e => {
            $('.map-tag-input').hide();
        });

        geocoder.on('result', e => {
            $('.mapboxgl-ctrl-geocoder--input').val(e['result'].place_name.replace(', United States',''));
            setTimeout(function(){
                $('.map-tag-input').show();
            }, 1000);
        });

        document.getElementById('geocoder').appendChild(geocoder.onAdd(map));

        $('.mapboxgl-ctrl-geocoder--input').attr("placeholder", "Jump to place or coordinates...");


}

function setMapControls() {
    var check = checkIfMapboxStyleIsLoaded();
    if (!check) {
        // It's not safe to manipulate layers yet, so wait 200ms and then check again
        setTimeout(function() {
            setMapControls();
        }, 200);
        return;
    }
    // Whew, now it's safe to manipulate layers!
    var mapUnits = readCookie('map_units');
    if (mapUnits == 'meters') {
        toggleMapUnits(mapUnits);
    }
}

function addExtraMapLayers() {
    var check = checkIfMapboxStyleIsLoaded();
    if (!check) {
        // It's not safe to manipulate layers yet, so wait 200ms and then check again
        setTimeout(function() {
            addExtraMapLayers();
        }, 200);
        return;
    }
    // Whew, now it's safe to manipulate layers!
    // No extra map layers necessary
}

$(document).ready(function() {

    //$('#map-canvas').height(500);
    var window_width = $(window).width();
    if (window_width < 768) {
        $('#map-canvas').height(350);
    } else {
        $('#map-canvas').height(500);
    }
    //center orange peak marker
    var centerMarkerTop = ($('#map-canvas').height() / 2) - 22;
    var centerMarkerLeft = ($('#map-canvas').width() / 2) - 13;
    $('#center-peak-marker').css({'top':centerMarkerTop});
    $('#center-peak-marker').css({'left':centerMarkerLeft});

    $('#map-canvas').mousemove(function(e) {
        var offset = $(this).offset();
        pageX = e.pageX;
        pageY = e.pageY;
        mapX = (e.pageX - offset.left);
        mapY = (e.pageY - offset.top);
    });

    $('#map-canvas').on('touchstart', function(e) {
        var offset = $(this).offset();
        pageX = e.originalEvent.touches[0].pageX;
        pageY = e.originalEvent.touches[0].pageY;
        mapX = (pageX - offset.left);
        mapY = (pageY - offset.top);
    });

    $(window).resize(function() {
        var window_width = $(window).width();
        if (window_width < 768) {
            $('#map-canvas').height(350);
        } else {
            $('#map-canvas').height(500);
        }
        //re-center orange peak marker
        var centerMarkerTop = ($('#map-canvas').height() / 2) - 22;
        var centerMarkerLeft = ($('#map-canvas').width() / 2) - 13;
        $('#center-peak-marker').css({'top':centerMarkerTop});
        $('#center-peak-marker').css({'left':centerMarkerLeft});
        map.resize();
    });

    initialize();

    //switch map units
    $("#gm-custom-mapunits").click(function(){
        if ($('#gm-custom-mapunitsbutton-label-meters').hasClass('gm-custom-mapunits-selected')) {
            toggleMapUnits('feet');
            scale.setUnit('imperial');
        } else {
            toggleMapUnits('meters');
            scale.setUnit('metric');
        }
    });

    //Enable scrollZoom
    $('#map-canvas').on('mouseleave', function() {
        map.scrollZoom.enable();
    });

    $("#peak-elevation-m").on('change', function() {
        setElevationMeters();
    });

    $("#peak-elevation-ft").on('change', function() {
        setElevationFeet();
    });

    $("#peak-prominence-m").on('change', function() {
        setProminenceMeters();
    });

    $("#peak-prominence-ft").on('change', function() {
        setProminenceFeet();
    });

    $('#peak-countries').on('change', '.add-region-select', function() {
        var new_region_id = $(this).val();
        var country_id = $(this).data('country');
        var new_region_name = $("option:selected", this).text();
        var regionHtml = '<div id="region-'+new_region_id+'"><div style="width: 100%;"></div><div style="float: left; margin-left: 15px; margin-right: 15px; height: 60px;"><i class="fa fa-level-up fa-rotate-90" style="margin-top: 20px;" aria-hidden="true"></i></div><div class="country-regions" style="background-color: #ddd; display: inline-block; height: 60px; margin-right: 20px; margin-bottom: 20px;"><span id="region-'+new_region_id+'-name" style="display: inline-block; margin-left: 20px; font-weight: 500; line-height: 60px;">'+new_region_name+'</span><span class="pull-right" style="padding-top: 17px; padding-right: 15px; padding-left: 20px; font-size: 24px; color: #ddd;"><a class="ajax-link" onclick="removeRegion('+new_region_id+','+country_id+');"><i class="fa fa-times"></i></a></span></div></div>';
        var country_region_div = '#country-'+country_id+'-regions';
        $(country_region_div).append(regionHtml);
        //remove option that was chosen
        $("option:selected", this).remove();
        $(this).val(0);
        var dropdown_hint = '#country-'+country_id+'-add-region option[value="0"]';
        $(dropdown_hint).text('add another region');
        if (map_adjusted) {
            $('#edit_peak').prop('disabled', false);
        }
    });

    $('#reset-add-new-country').click( function() {
        $('#add-new-country').fadeOut(300, function() {
            $('#add-new-country-link').fadeIn(300);
        });
    });

    $('#btnAddAlternateName').click( function() {
        var new_alternate_name_index = $('#peak-alternate-names').children().length;
        var new_alternate_name = $('#peak-alternate-name').val();
        var new_alternate_name_html = '<div id="alternate-name-'+new_alternate_name_index+'"><div class="country-regions" style="background-color: #ddd; display: inline-block; height: 60px; margin-right: 20px; margin-bottom: 20px;"><span style="display: inline-block; margin-left: 20px; font-weight: 500; line-height: 60px;">'+new_alternate_name+'</span><span class="pull-right" style="padding-top: 17px; padding-right: 15px; padding-left: 20px; font-size: 24px; color: #ddd;"><a class="ajax-link" onclick="removeAlternateName('+new_alternate_name_index+');"><i class="fa fa-times"></i></a></span></div></div>';
        $('#peak-alternate-names').append(new_alternate_name_html);
        $('#peak-alternate-name').attr('placeholder', 'add another alternate name');
        $('#peak-alternate-name').blur();
        $('#peak-alternate-name').val('');
        $('#peak-alternate-name').focus();
        if (map_adjusted) {
            $('#edit_peak').prop('disabled', false);
        }
    });

    $('#peak-name').on('input', function() {
        if (map_adjusted) {
            $('#edit_peak').prop('disabled', false);
        }
    });

    $('#peak-elevation').on('input', function() {
        if (map_adjusted) {
            $('#edit_peak').prop('disabled', false);
        }
    });

    $('#peak-prominence').on('input', function() {
        if (map_adjusted) {
            $('#edit_peak').prop('disabled', false);
        }
    });

    $('#peak-range').on('input', function() {
        if (map_adjusted) {
            $('#edit_peak').prop('disabled', false);
        }
    });

    $('#peak-other-changes').on('input', function() {
        if (map_adjusted) {
            $('#edit_peak').prop('disabled', false);
        }
    });

    $('#peak-location-search').bind("enterKey",function(e){
       geoCode();
    });

    $('#edit_peak').on('click', function(e) {
        e.preventDefault();
        validateForm();
    });

    $(document).on("keydown", ":input:not(textarea)", function(event) {
        if (event.keyCode == 13) {
            return false;
        } else {
            return event.keyCode;
        }
    });

    $('#peak-location-search').keyup(function(e){
        if(e.keyCode == 13)
        {
            $(this).trigger("enterKey");
        }
    });

    //on close the thanks modal
    $('#thanks-modal').on('hidden.bs.modal', function () {
        var referrer =  document.referrer;
        if (referrer != '') {
            window.location.href = referrer;
        } else {
            window.location.href = '/map/';
        }
    });

    $('#gm-custom-mapbutton').on('mouseenter', function(){
       $('#gm-custom-mapdropdown').show();
       $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
       $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapbutton').on('mouseleave', function(){
       $('#gm-custom-mapdropdown').hide();
       $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    $('#gm-custom-mapbutton').on('touchstart', function() {
        $('#gm-custom-mapdropdown').toggle();
    });

});

function validateForm() {

    //form validation
    $('#peakedit_form').formValidation({
        // I am validating Bootstrap form
        framework: 'bootstrap',
        message: '',
        // List of fields and their validation rules
        fields: {
            'peak-elevation': {
                validators: {
                    notEmpty: {
                        message: 'Please enter the peak elevation'
                    },
                    numeric: {
                        message: 'Elevation must be numeric'
                    }
                }
            }
        }
    })
    .on('success.form.fv', function(e) {
        // Prevent form submission
        e.preventDefault();
        $('#edit_peak').prop('disabled', true);
        $('#edit_peak').html('<i class="fa fa-spinner fa-spin"></i>');

        // Some instances you can use are
        var $form = $(e.target),        // The form instance
            fv    = $(e.target).data('formValidation'); // FormValidation instance

        // Use Ajax to submit form data
        $.ajax({
            url: $form.attr('action'),
            type: 'POST',
            data: $form.serialize(),
            dataType: 'json',
            success: function(result) {
                if (result.success == 'true') {
                    //set map center cookie
                    var NewMapCenter = map.getCenter();
                    var NewMapZoom = map.getZoom();
                    createCookie('map_lat',NewMapCenter.lat,365);
                    createCookie('map_lng',NewMapCenter.lng,365);
                    createCookie('map_zoom',NewMapZoom,365);
                    //show thanks modal
                    $('#thanks-modal').modal('show');
                } else {
                    //show error modal
                    $('#error-modal').modal('show');
                }
                $('#edit_peak').html('Submit <span class="hidden-xs">peak</span>');
            }
        });
    })
    .formValidation('validate');
}

function removeCountry(country_id) {

    var country_div = '#country-'+country_id;
    $(country_div).fadeOut(500, function() {
        $(this).remove();
        if ($('#peak-countries').children().length == 0) {
            $('#add-new-country-link').html('<a style="cursor: pointer;" onclick="addNewCountry();">add a country</a>');
        } else {
            $('#add-new-country-link').html('if on border <a style="cursor: pointer;" onclick="addNewCountry();">add another country</a>');
        }
    });
    if (map_adjusted) {
        $('#edit_peak').prop('disabled', false);
    }

}

function removeAlternateName(index) {

    var alternate_name_div = '#alternate-name-'+index;
    $(alternate_name_div).fadeOut(500, function() {
        $(this).remove();
        if ($('#peak-alternate-names').children().length == 0) {
            $('#peak-alternate-name').attr('placeholder', 'add an alternate name');
        } else {
            $('#peak-alternate-name').attr('placeholder', 'add another alternate name');
        }
    });
    if (map_adjusted) {
        $('#edit_peak').prop('disabled', false);
    }

}

function removeRegion(region_id, country_id) {

    var region_div = '#region-'+region_id;
    $(region_div).fadeOut(500, function() {
        $(this).remove();
        var dropdown_hint = '#country-'+country_id+'-add-region option[value="0"]';
        if ($(country_regions_div).children().length == 0) {
            $(dropdown_hint).text('add a region');
        } else {
            $(dropdown_hint).text('add another region');
        }
    });
    //add region back to dropdown
    var dropdown = '#country-'+country_id+'-add-region';
    var dropdown_options = '#country-'+country_id+'-add-region option';
    var region_span = '#region-'+region_id+'-name';
    var region_name = $(region_span).text();
    var country_regions_div = '#country-'+country_id+'-regions';
    $(dropdown).append('<option value="'+region_id+'">'+region_name+'</option>');
    $(dropdown).html($(dropdown_options).sort(function (a, b) {
        return a.text == b.text ? 0 : a.text < b.text ? -1 : 1
    }));
    if (map_adjusted) {
        $('#edit_peak').prop('disabled', false);
    }

}

function addNewCountry() {

    $('#add-new-country-link').fadeOut(300, function() {
        $('#add-new-country').fadeIn(300);
    });
    if (map_adjusted) {
        $('#edit_peak').prop('disabled', false);
    }

}

function check_is_in(marker){
    return map.getBounds().contains(marker.getPosition());
}

function delete_out_markers(){
    if (markersArray){
        for (i in markersArray){
            if (!check_is_in(markersArray[i])){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }
}

function deletehighest(){
    if (markersArray){
        for (i in markersArray){
            if (markersArray[i].properties.iconstyle == 'marker_icon_redgreen' || markersArray[i].properties.iconstyle == 'marker_icon_red'){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }
}

function limit_number_of_markers(limit){
    if (markersArray.length > limit){
        for (i = markersArray.length-1; i>=limit; i--){
            markersArray[i].remove();
            markersArray.splice(i,1);
        }
    }
}

function elevation_range(data){
    if (markersArray){
        for (i = markersArray.length-1; i>=0; i--){
            var inrange = false;
            $.each(data, function(k, v){
                var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                    inrange = true;
                }
            });
            if (!inrange){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }
}

function delete_old_markers(data){
    if (markersArray){
        for (i = markersArray.length-1; i>=0; i--){
            var inrange = false;
            $.each(data, function(k, v){
                var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                    inrange = true;
                }
            });
            if (!inrange){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }
}

function fromLatLngToString(latLng) {
    return latLng.lat + ',' + latLng.lng;
}

function numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

function check_lat_lon(lat, lon){

    var ck_lat = /^(-?[1-8]?\d(?:\.\d{1,18})?|90(?:\.0{1,18})?)$/;
    var ck_lon = /^(-?(?:1[0-7]|[1-9])?\d(?:\.\d{1,18})?|180(?:\.0{1,18})?)$/;

    var validLat = ck_lat.test(lat);
    var validLon = ck_lon.test(lon);
    if(validLat && validLon) {
        return true;
    } else {
        return false;
    }
}

function setElevationMeters() {
    if ($('#peak-elevation').val() != '') {
        var elevation_feet = parseFloat($('#peak-elevation').val());
        var elevation_meters = Math.round(elevation_feet * .3048);
        $('#peak-elevation').val(elevation_meters);
        if (map_adjusted) {
            $('#edit_peak').prop('disabled', false);
        }
    }
    $('#peak-elevation').attr('placeholder','in meters...');
}

function setElevationFeet() {
    if ($('#peak-elevation').val() != '') {
        var elevation_meters = parseFloat($('#peak-elevation').val());
        var elevation_feet = Math.round(elevation_meters / .3048);
        $('#peak-elevation').val(elevation_feet);
        if (map_adjusted) {
            $('#edit_peak').prop('disabled', false);
        }
    }
    $('#peak-elevation').attr('placeholder','in feet...');
}

function setProminenceMeters() {
    if ($('#peak-prominence').val() != '') {
        var prominence_feet = parseFloat($('#peak-prominence').val());
        var prominence_meters = Math.round(prominence_feet * .3048);
        $('#peak-prominence').val(prominence_meters);
        if (map_adjusted) {
            $('#edit_peak').prop('disabled', false);
        }
    }
    $('#peak-prominence').attr('placeholder','in meters...');
}

function setProminenceFeet() {
    if ($('#peak-prominence').val() != '') {
        var prominence_meters = parseFloat($('#peak-prominence').val());
        var prominence_feet = Math.round(prominence_meters / .3048);
        $('#peak-prominence').val(prominence_feet);
        if (map_adjusted) {
            $('#edit_peak').prop('disabled', false);
        }
    }
    $('#peak-prominence').attr('placeholder', 'in feet...');
}

</script>

{% include "mapbox/map_layers.html" %}

{% endblock %}

{% block end_body_form %}</form>{% endblock %}
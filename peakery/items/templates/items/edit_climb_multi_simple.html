{% extends "base_no_header_footer.html" %}
{% load static %}
{% load avatar_tags %}
{% load item_tags %}
{% load cache %}
{% load humanize %}

{% block title %}Log a climb{% endblock %}
{% block titlemeta %}Log a climb{% endblock %}
{% block description %}Log a climb{% endblock %}

{% block mapbox %}
{% include "mapbox/mapbox.html" %}
{% endblock %}

{% block full_height_form %}
<form id="summitlog_form" class="summitlog_form" method="POST" action="{% url "edit_climb_multi_simple" %}">
{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row" style="border-bottom: none;">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 form-header-bar">
            <span style="font-size: 24px; font-weight: 600; color: #fff;">
            <div id="breadcrumbs">
                <div id="cancel-x-button-div" class="pull-right cancel-x-button-div">
                    <a class="cancel-x-button" onclick="cancelEdit();"><i class="fa fa-times"></i></a>
                </div>
                <div id="cancel-location-search-div" class="pull-right cancel-x-button-div" style="display: none;">
                    <a class="cancel-x-button" onclick="cancelLocationSearch();"><i class="fa fa-times"></i></a>
                </div>
                <div id="save-changes-button-div" class="pull-right save-changes-button-div">
                    <button class="btn btn-secondary set2 input save-changes-button" type="submit" id="add_peak2" disabled>Save log</button>
                </div>
                <div>
                    <div id="log-a-climb-title" class="form-header-title ellipsis"><span>Log a climb</span></div>
                    <div id="location-search-title" class="form-header-title ellipsis" style="display: none;"><span>Select a peak from the map</span></div>
                </div>
            </div>
            </span>
        </div>
    </div>
{% endblock %}

{% block content %}

<script src="{% static 'vendor/s3.fine-uploader/s3.fine-uploader.js' %}"></script>

<style>

    .btn, input[type="checkbox"], input[type="text"], .toggle-switch label, .toggle-switch {
        border-radius: 12px !important;
    }

    html, body {
        letter-spacing: .03125em;
    }

    body.modal-open {
        overflow: visible;
    }

    #date {
        border-radius: 0px 0px 8px 8px;
        border: 1px solid #ddd;
        border-top: none;
    }

    .ui-datepicker {
        width: 100%;
        border-radius: 0px 0px 8px 8px;
    }

    .ui-state-active, .ui-state-highlight, .ui-state-hover {
        border-radius:8px;
    }

    .ui-datepicker-title {
        border-radius: 8px 8px 0px 0px;
        border: 1px solid #ddd;
    }

    .help-block {
        display: block;
        margin-top: -7px;
        margin-bottom: 10px;
        color: #ff0000;
    }

    .remove-photo {
        cursor: pointer;
        color: #fff;
        font-size: 20px;
    }

    .remove-photo:hover {
        color: #ccc;
    }

    .ui-state-active {
        color: #fff;
        background-color: #f24100;
    }

    #summit {
        border-radius: 8px;
    }

    div.summitlog.caption textarea {
        border-radius: 0px 8px 8px 0px;
    }

    @media screen and (min-width: 0px) and (max-width: 375px) {
        .peak-climbed-wrapper {
            width: 210px;
        }
    }

   @media screen and (min-width: 1024px) {

       #summitmemq1 {
           width: 50%;
       }

       #btnAddSummitCompanion {
           width: 150px;
       }

       #member-search-icon-div {
           position: relative;
           left: -100px;
       }

       form.summitlog_form span {
            padding-bottom: 0px;
        }

       .ui-datepicker-title {
            text-indent: 5px;
        }

       .peak-climbed-div {
           width: 160px;
           height: 120px;
           border-top-left-radius: 8px;
           border-bottom-left-radius: 8px;
       }

       .peak-climbed-container {
           width: 100%;
           height: 120px;
           border-radius: 8px;
       }

       .peak-climbed-peak-name {
           font-weight: 500;
           font-size: 18px;
           padding-left: 10px;
           letter-spacing: 0.54px;
       }

       .peak-climbed-peak-stats {
           height: 36px;
           line-height: 20px;
       }

       .peak-climbed-outcome {
           margin-top: 2px;
       }

       .suggestion-peak-photo {
           width: 112px;
           height: 84px;
       }

       .suggestion-peak-name {
           padding-left: 122px;
           margin-top: -7px;
       }

       .suggestion-peak-stats {
           font-size: 12px;
           padding-left: 124px;
           line-height: 20px;
           padding-bottom: 9px;
       }

       #peaks-climbed-map-instructions {
           height: 40px;
       }

       .gpx-stat-1 {
           border-right: solid 1px #eeeeee;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-2 {
           background-color: #f6f6f6;
           border-right: solid 1px #eeeeee;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-3 {
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-4 {
           border-right: solid 1px #eeeeee;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-5 {
           background-color: #f6f6f6;
           border-right: solid 1px #eeeeee;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-6 {
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .header-peak-name {
            color: #f24100;
        }
        .header-peak {
            font-size: 20px;
            font-weight: 300;
        }
        .close-log-edit {
            margin-right: 20px;
        }
        .header-help {
            color: #999;
            font-size: 14px;
            font-weight: 300;
            margin-left: 10px;
        }
        .field-title {
            font-size: 21px;
            font-weight: 700;
        }
        .form-header-title {
            font-size: 24px;
            font-weight: 500;
        }
        #extra-photo-1 {
            top: 74px;
            left: 330px;
        }
        #extra-photo-2 {
            top: 74px;
            left: 640px;
        }
        #extra-photo-3 {
            top: 74px;
            left: 950px;
        }
        #hide-new-summit-route-span {
            top: 80px;
        }
        #add_peak2 {
            width: 140px;
        }
        #gm-custom-mapunits {
            right: 142px;
        }

        .autocomplete-suggestions {
            max-height: 70% !important;
        }

        .suggestion-companion-div {
            height: 75px;
        }

        .suggestion-companion-name {
            position: absolute;
            left: 85px;
            height: 75px;
            line-height: 75px;
            font-size: 16px;
            font-weight: 500;
        }

        .suggestion-companion-avatar {
            width: 75px;
            height: 75px;
            float: left;
            left: 0px;
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
            border-left: solid 1px #e0e0e0;
            overflow: hidden;
        }

        ::-webkit-input-placeholder { font-size: 18px; }
        ::-moz-placeholder { font-size: 18px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 18px; } /* ie */
        input:-moz-placeholder { font-size: 18px; }
        input { font-size: 18px; }
   }

   @media screen and (min-width: 768px) and (max-width: 1023px) {

       #summitmemq1 {
           width: 50%;
       }

       #btnAddSummitCompanion {
           width: 150px;
       }

       #member-search-icon-div {
           position: relative;
           left: -100px;
       }

       .ui-datepicker-title {
            text-indent: 5px;
        }

       form.summitlog_form span {
            padding-bottom: 0px;
        }

       .peak-climbed-div {
           border-top-left-radius: 4px;
           border-bottom-left-radius: 4px;
           width: 120px;
           height: 120px;
       }

       .peak-climbed-container {
           width: 450px;
           height: 120px;
           border-radius: 8px;
       }

       .peak-climbed-peak-name {
           letter-spacing: -0.5px;
           padding-left: 8px;
           padding-right: 12px;
           font-size: 16px;
           font-weight: 500;
       }

       .peak-climbed-peak-stats {
           line-height: 20px
           height: 36px;
       }

       .peak-climbed-outcome {
           margin-top: -2px;
       }

       .suggestion-peak-photo {
           width: 84px;
           height: 84px;
       }

       .suggestion-peak-name {
           padding-left: 90px;
       }

       .suggestion-peak-stats {
           padding-left: 92px;
           padding-bottom: 6px;
           font-size: 11px;
           line-height: 18px;
       }

       #peaks-climbed-map-instructions {
           height: 0px;
       }

       #peaks-climbed-map-instructions-text {
           display: none;
       }

       .gpx-stat-1 {
           border-right: solid 1px #eeeeee;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-2 {
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-3 {
           background-color: #f6f6f6;
           border-right: solid 1px #eeeeee;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-4 {
           background-color: #f6f6f6;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-5 {
           border-right: solid 1px #eeeeee;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-6 {
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .header-peak-name {
            color: #fff;
        }
        .header-peak {
            font-size: 18px;
            font-weight: 500;
        }
        .close-log-edit {
            margin-right: 0px;
        }
        .header-help {
            color: #999;
            font-size: 12px;
            font-weight: 300;
            margin-left: 10px;
        }
        .field-title {
            font-size: 18px;
            font-weight: 700;
        }
        .form-header-title {
            font-size: 18px;
            font-weight: 500;
        }
        #extra-photo-1 {
            top: 48px;
            left: 330px;
            margin-top: 15px !important;
        }
        #extra-photo-2 {
            top: 48px;
            left: 640px;
        }
        #extra-photo-3 {
            top: 48px;
            left: 950px;
        }
        #hide-new-summit-route-span {
            top: 75px;
        }
        #add_peak2 {
            width: 140px;
        }

        .autocomplete-suggestions {
            max-height: 387px !important;
        }

        .suggestion-companion-div {
            height: 75px;
        }

        .suggestion-companion-name {
            position: absolute;
            left: 85px;
            height: 75px;
            line-height: 75px;
            font-size: 16px;
            font-weight: 500;
        }

        .suggestion-companion-avatar {
            width: 75px;
            height: 75px;
            float: left;
            left: 0px;
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
            border-left: solid 1px #e0e0e0;
            overflow: hidden;
        }

        ::-webkit-input-placeholder { font-size: 16px; }
        ::-moz-placeholder { font-size: 16px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 16px; } /* ie */
        input:-moz-placeholder { font-size: 16px; }
        input { font-size: 16px; }
   }

   @media screen and (min-width: 1px) and (max-width: 767px) {

       #summitmemq1 {
           width: 255px;
       }

       #btnAddSummitCompanion {
           width: 70px;
       }

       #member-search-icon-div {
           position: absolute;
           left: 270px;
       }

       form.summitlog_form span {
            padding-bottom: 10px;
        }

       #peaks-climbed-date {
           padding-bottom: 10px;
       }

       body {
           -webkit-user-select: none;
       }

       input {
            -webkit-user-select: auto;
        }
        form textarea {
            -webkit-user-select: auto;
        }

       .peak-climbed-div {
           border-top-left-radius: 4px;
           border-bottom-left-radius: 4px;
           width: 120px;
           height: 120px;
       }

       .peak-climbed-container {
           width: 100%;
           height: 120px;
           border-radius: 8px;
       }

       .peak-climbed-peak-name {
           letter-spacing: -0.5px;
           padding-left: 8px;
           padding-right: 12px;
           font-size: 16px;
           font-weight: 500;
       }

       .peak-climbed-peak-stats {
           line-height: 20px
           height: 36px;
       }

       .peak-climbed-outcome {
           margin-top: -2px;
       }

       .suggestion-peak-photo {
           width: 84px;
           height: 84px;
       }

       .suggestion-peak-name {
           padding-left: 90px;
       }

       .suggestion-peak-stats {
           padding-left: 92px;
           padding-bottom: 6px;
           font-size: 11px;
           line-height: 18px;
       }

       #peaks-climbed-map-instructions {
           height: 0px;
       }

       #peaks-climbed-map-instructions-text {
           display: none;
       }

       #peaks-climbed-map-container {
            /*position: fixed;*/
            top: 70px;
            left: 0px;
            padding-top: 0px;
            padding-left: 0px;
            padding-right: 0px;
            width: 100%;
            /*height: 100%;*/
            z-index: 9999;
            background-color: #eee;
        }

       .peaks-climbed-map-container-open {
           position: fixed;
           height: 100%;
       }

       #map-canvas {
           height: 100% !important;
       }

       #peak-location-search-container {
           padding-left: 0px;
           padding-top: 0px;
           padding-right: 0px;
       }

       .save-changes-button {
           font-size: 16px;
       }

       .gpx-stat-1 {
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-2 {
           background-color: #f6f6f6;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-3 {
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-4 {
           background-color: #f6f6f6;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-5 {
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-6 {
           background-color: #f6f6f6;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       #txtAddActivityTag, #txtAddChallengeTag, #txtAddGearTag, #summit-related-link {
            width: 70%;
            font-size: 16px;
        }
        #btnAddActivityTag, #btnAddChallengeTag, #btnAddGearTag, #btnAddRelatedLink {
            width: 84px;
        }
       input {
            font-size: 16px;
            font-weight: 300;
        }
        form textarea {
            font-size: 16px;
            font-weight: 300;
        }
        .header-peak-name {
            color: #fff;
        }
        .header-peak {
            font-size: 18px;
            font-weight: 500;
        }
        .close-log-edit {
            margin-right: 0px;
        }
        .header-help {
            color: #999;
            font-size: 11px;
            font-weight: 300;
            margin-left: 10px;
        }
        .field-title {
            font-size: 16px;
            font-weight: 700;
        }
        .summitlog-section {
            margin-top: 20px;
        }
        .field-title-spacer {
            height: 0px;
        }
        .form-header-title {
            font-size: 16px;
            font-weight: 500;
        }
        #extra-photo-1 {
            top: 48px;
            left: 330px;
        }
        #extra-photo-2 {
            top: 48px;
            left: 640px;
        }
        #extra-photo-3 {
            top: 48px;
            left: 950px;
        }
        #hide-new-summit-route-span {
            top: 60px;
        }

        .autocomplete-suggestions {
            height: 270px !important;
        }
        .summit-companion {
            width: 100%;
        }
        #add_peak2 {
            width: 110px;
        }
        .member-searchbox-input {
            margin-bottom: 20px;
        }

        .suggestion-companion-div {
            height: 53px;
        }

        .suggestion-companion-name {
            position: relative;
            left: 10px;
            height: 53px;
            line-height: 75px;
            font-size: 16px;
            font-weight: 500;
        }

        .suggestion-companion-avatar {
            width: 75px;
            height: 75px;
            float: left;
            left: 0px;
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
            border-left: solid 1px #e0e0e0;
            overflow: hidden;
        }

        ::-webkit-input-placeholder { font-size: 16px; }
        ::-moz-placeholder { font-size: 16px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 16px; } /* ie */
        input:-moz-placeholder { font-size: 16px; }
        input { font-size: 16px; }
   }

   @media screen and (max-width: 767px) and (min-width: 1px) {
       #content-body {
           margin-top: 0px;
           padding-bottom: 0px;
       }
       .content-pane {
           margin-top: 20px;
       }
       #peak-name-search-div, #peak-location-search-div {
            width: 100%;
           margin-bottom: 10px;
        }
        #btnSearchPeakMapDiv {
            /*width: 100%;*/
            margin-bottom: 10px;
        }
   }
    @media screen and (min-width: 768px) {
        #content-body {
           margin-top: 30px;
       }
        .content-pane {
            margin-top: 0px;
       }
        input {
            font-size: 18px;
            font-weight: 300;
        }
        form textarea {
            font-size: 18px;
            font-weight: 300;
        }
        #txtAddActivityTag, #txtAddChallengeTag, #txtAddGearTag {
            width: 33%;
            font-size: 18px;
        }
        #summit-related-link {
            width: 70%;
            font-size: 18px;
        }
        #btnAddActivityTag, #btnAddChallengeTag, #btnAddGearTag, #btnAddRelatedLink {
            width: 150px;
        }
        .summitlog-section {
            margin-top: 20px;
        }
        .field-title-spacer {
            height: 10px;
        }
        .summit-companion {
            width: 46%;
        }
        #peak-name-search-div, #peak-location-search-div {
            width: 35%;
            min-width: 280px;
        }
        #btnSearchPeakMapDiv {

        }
    }
    @media screen and (min-width: 1px) and (max-width: 1279px) {
         .no-header-container {
           padding-left: 0px;
       }
    }
    ::-webkit-input-placeholder { font-weight: 300; }
    ::-moz-placeholder { font-weight: 300; } /* firefox 19+ */
    :-ms-input-placeholder { font-weight: 300; } /* ie */
    input:-moz-placeholder { font-weight: 300; }
    #ui-datepicker-div { display: none; }

    #summitlog-files li {
        float: left;
        margin-right: 7px;
    }

    ul#summitlog-files .qq-upload-button.loading {
        height: 63%;
        padding-top: 95px;
        padding-left: 150px;
    }

    ul#summitlog-files li div.a:hover {
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
    }

    .slow .toggle-group { transition: background-color 2.5s ease; -webkit-transition: background-color 2.5s ease; }

    .qq-upload-list {
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
    }

    .qq-upload-list li.qq-upload-success {
        background-color: #f2f2f2;
        color: #424242;
        border-bottom: none;
        border-top: none;
    }

    .qq-upload-list li.qq-in-progress {
        background-color: #f2f2f2;
        color: #424242;
        border-bottom: none;
        border-top: none;
    }

    .qq-thumbnail-selector {
        border-radius: 8px 0px 0px 8px;
    }

    div.summitlog.caption {
        width: 100% !important;
    }

    @media screen and (min-width: 1px) and (max-width: 479px) {
        .qq-thumbnail-wrapper, .qq-thumbnail-selector {
            width: 120px;
            max-width: 120px;
            min-height: 80px;
        }
        .remove-photo {
            width: 120px;
            max-width: 120px;
            right: 6px;
            top: 11px;
            vertical-align: top;
            position: absolute;
            display: table-cell;
            text-align: right;
        }
    }
    @media screen and (min-width: 480px) and (max-width: 767px) {
        .qq-thumbnail-wrapper, .qq-thumbnail-selector {
            width: 200px;
            max-width: 200px;
            min-height: 133px;
        }
        .remove-photo {
            width: 200px;
            max-width: 200px;
            right: 6px;
            top: 11px;
            vertical-align: top;
            position: absolute;
            display: table-cell;
            text-align: right;
        }
    }
    @media screen and (min-width: 768px) {
        .qq-thumbnail-wrapper, .qq-thumbnail-selector {
            width: 200px;
            max-width: 200px;
            min-height: 133px;
        }
        .remove-photo {
            width: 200px;
            max-width: 200px;
            right: 6px;
            top: 11px;
            vertical-align: top;
            position: absolute;
            display: table-cell;
            text-align: right;
        }
    }

    .qq-alert-dialog-selector, .qq-confirm-dialog-selector, .qq-prompt-dialog-selector {
        margin: auto;
        padding: 20px;
    }

    ul#summitlog-gpx-files li div.a {
        background-color: transparent;
        margin: 0px;
    }

    .autocomplete-suggestion {
        border-bottom: solid 1px #e0e0e0;
        padding: 0px;
    }

    .suggestion-peak-name {
        /*padding-left: 10px;*/
        padding-top: 10px;
        float: left;
        font-weight: 500;
    }

    .suggestion-peak-stats {
        float: left;
        clear: left;
        color: #666;
        width: 100%;
        padding-right: 10px;
    }

    .peak-climbed-wrapper {
        float: left;
    }

    .peak-climbed-peak-name {
        /*padding-left: 10px;
        font-size: 18px;
        letter-spacing: 0.54px;*/
        padding-top: 4px;
        color: #333333;
        height: 38px;
    }

    .peak-climbed-peak-stats {
        /*height: 30px;
        line-height: 14px;*/
        padding-left: 10px;
        padding-bottom: 0px;
        font-size: 12px;
        color: #666666;
        letter-spacing: 0.36px;
        margin-top: 0px;
    }

    .peak-climbed-outcome {
        /*margin-top: -8px;*/
        margin-left: 8px;
        height: 30px;
    }

    .peak-climbed-outcome-label {
        font-size: 10px !important;
    }

    .peak-climbed-outcome-span {
        font-size: 14px !important;
    }

    .add-another-peak-title {
        font-size: 18px;
        color: #00B1F2;
        letter-spacing: 0.34px;
    }

    .add-another-peak-help {
        font-size: 14px;
        color: #999999;
        letter-spacing: 0.54px;
    }

    #peaks-climbed-list {
        margin-bottom: 30px;
    }

    @media screen and (min-width: 768px) {

        .ui-datepicker-month, .ui-datepicker-year {
            width: 48%;
        }

        #peak-search {
            padding-left: 10px;
            line-height: 38px;
            border-radius: 12px;
        }

        #peak-search-container {
            width: 100%;
        }

        #peak-search-icon-div {
            position: relative;
        }

        #peak-search-icon {
            top: -35px;
            right: 15px;
            font-size: 16px;
            display: block;
        }

        form.summitlog_form input[type="text"], form.summitlog_form input[type="password"] {
            width: 100%;
        }

        .peak-climbed-container {
            background-color: #fff;
            /*width: 560px;
            height: 100px;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;*/
            margin-top: 10px;
            box-shadow: rgba(0, 0, 0, .2) 0px 2px 5px;
        }

        .peak-climbed-icon-div {
            position: relative;
        }

        .peak-climbed-icon {
            top: 10px;
            right: 8px;
            font-size: 16px;
            display: block;
        }

        #add-another-peak {
            margin-top: 20px;
        }

        #member-search-container {
            width: 560px;
        }

        #member-search-icon {
            top: -35px;
            right: 190px;
            font-size: 16px;
        }

        #member-search-button-div {
            float: right;
            margin-right: 10px;
            margin-top: -54px;
        }

    }

    @media screen and (max-width: 767px) {

        #peak-search {
            padding-left: 10px;
            line-height: 38px;
            width: 100%;
            border-radius: 12px;
        }

        #peak-search-container {
            width: 100%;
        }

        .peak-climbed-peak-stats {
            font-size: 11px;
        }

        #peak-search-icon-div {
            position: relative;
        }

        #peak-search-icon {
            top: -35px;
            right: 15px;
            font-size: 16px;
            display: block;
        }

        .peak-climbed-container {
            background-color: #fff;
            /*width: 100%;
            height: 100px;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;*/
            margin-top: 10px;
            box-shadow: rgba(0, 0, 0, .2) 0px 2px 5px;
        }

        .peak-climbed-icon-div {
            position: relative;
        }

        .peak-climbed-icon {
            top: 10px;
            right: 8px;
            font-size: 16px;
            display: block;
        }

        #add-another-peak {
            margin-top: 20px;
        }

        #member-search-container {
            width: 100%;
        }

        #member-search-icon {
            top: -55px;
            right: 15px;
            font-size: 16px;
        }

        #member-search-button-div {
            float: left;
        }

    }

    .summitlog-section-hidden, .summitlog-route-hidden {
        display: none;
    }

    #add-more-info-container {
        padding-bottom: 60px;
    }

    #content-holder {
        background-image: none !important;
        background-color: #f6f6f6;
    }

    .map-tooltip-info {
        background-color: rgba(0,0,0,.6);
        -webkit-backdrop-filter: blur(0px) !important;
    }

    .marker_icon:hover, .marker_icon_red:hover, .marker_icon_green:hover, .marker_icon_redgreen:hover, .marker_icon_yellow:hover, .marker_icon_purple:hover, .marker_icon_peak:hover, .marker-icon-hover {
        background-image: url('{% static 'img/<EMAIL>' %}') !important;
        height: 28px;
        width: 28px;
        -webkit-animation-name: markerPulse;
        -webkit-animation-duration: 3s;
        -webkit-animation-iteration-count: infinite;
    }

    @-webkit-keyframes markerPulse {
        from { -webkit-filter: brightness(1.2) saturate(1.5); }
        50% { -webkit-filter: brightness(0.9) saturate(1); }
        to { -webkit-filter: brightness(1.2) saturate(1.5); }
    }

    @media screen and (min-width: 769px) {
        .noUi-horizontal .noUi-handle {
            width: 35px;
            height: 35px;
            left: -24px;
            top: -17px;
            background-color: transparent;
            background-size: contain;
        }
    }

    /*tooltip animations*/
    .scale-in-tl{
        -webkit-animation: scale-in-tl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-tl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-tl {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-in-tl {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
    }

    .scale-out-tl {
        -webkit-animation: scale-out-tl .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-tl .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-tl {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-out-tl {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
    }

    .scale-in-tm{
        -webkit-animation: scale-in-tm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-tm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-tm {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-in-tm {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
    }

    .scale-out-tm {
        -webkit-animation: scale-out-tm .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-tm .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-tm {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-out-tm {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
    }

    .scale-in-tr{
        -webkit-animation: scale-in-tr .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-tr .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-tr {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-in-tr {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
    }

    .scale-out-tr {
        -webkit-animation: scale-out-tr .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-tr .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-tr {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-out-tr {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
    }

    /*bottom*/
    .scale-in-bl{
        -webkit-animation: scale-in-bl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-bl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-bl {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-in-bl {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
    }

    .scale-out-bl {
        -webkit-animation: scale-out-bl .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-bl .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-bl {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-out-bl {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
    }

    .scale-in-bm{
        -webkit-animation: scale-in-bm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-bm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-bm {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-in-bm {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
    }

    .scale-out-bm {
        -webkit-animation: scale-out-bm .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-bm .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-bm {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-out-bm {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
    }

    .scale-in-br{
        -webkit-animation: scale-in-br .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-br .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-br {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-in-br {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
    }

    .scale-out-br {
        -webkit-animation: scale-out-br .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-br .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-br {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-out-br {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
    }

</style>

    <script type="text/template" id="qq-image-template">
        <div class="qq-uploader-selector qq-uploader" qq-drop-area-text="" style="max-height: none; background-color: #f2f2f2; border: none; padding: 0px; min-height: 0px;">
            <div class="qq-upload-drop-area-selector qq-upload-drop-area" qq-hide-dropzone>
                <span class="qq-upload-drop-area-text-selector"></span>
            </div>
            <ul class="qq-upload-list-selector qq-upload-list" aria-live="polite" aria-relevant="additions removals" style="max-height: none;">
                <li style="padding: 5px;">
                    <div style="display: flex; display: -webkit-flex;">
                        <div class="qq-thumbnail-wrapper" style="position: relative; align-content: stretch;">
                            <div>
                                <img class="qq-thumbnail-selector" qq-max-size="300" qq-server-scale style="margin-right: 0px;">
                            </div>
                            <div id="file-2-remove-photo" class="remove-photo"><i style="text-shadow: 0px 0px 5px #ccc;" class="fa fa-times-circle" aria-hidden="true"></i></div>
                        </div>
                        <div class="summitlog caption textareaContainer" style="margin-top: 0px; align-content: stretch; position: relative;">
                            <textarea id="" name="" class="blur addACaption" placeholder="write a caption..." style="width: 99% !important; height: 100% !important; padding-top: 16px; color: #333 !important; position: absolute;"></textarea>
                            <input class="hiddenPhoto" type="hidden" id="" name="" value="">
                        </div>
                        <div style="position: absolute;">
                            <button type="button" class="qq-upload-retry-selector qq-upload-retry btn btn-secondary">Retry</button>
                        </div>
                    </div>
                </li>
            </ul>
            <div class="qq-upload-button-selector btn btn-secondary" style="width: 230px; height: 54px; font-size: 16px; border-radius: 12px; text-align: center; background-color: #33c1f5; margin: 5px; z-index: 1;">
                <div><i class="fa fa-images" style="padding-right: 5px;"></i>Add your photos</div>
            </div>
            <span class="qq-drop-processing-selector qq-drop-processing">
                <span>Processing dropped files...</span>
                <span class="qq-drop-processing-spinner-selector qq-drop-processing-spinner"></span>
            </span>

            <dialog class="qq-alert-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">Close</button>
                </div>
            </dialog>

            <dialog class="qq-confirm-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">No</button>
                    <button type="button" class="qq-ok-button-selector btn btn-secondary">Yes</button>
                </div>
            </dialog>

            <dialog class="qq-prompt-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <input type="text">
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">Cancel</button>
                    <button type="button" class="qq-ok-button-selector btn btn-secondary">Ok</button>
                </div>
            </dialog>
        </div>
    </script>

    <div class="container" style="padding-left: 0px; padding-right: 0px;">

        <div class="content-pane" style="background-color: #f6f6f6;">

            <input type="hidden" id="summit_id" name="summit_id" value="{{ summit.id }}">
            <input type="hidden" id="summit_ids" name="summit_ids" value="">
            <input type="hidden" name="summit-date" id="summit-date">
            <input type="hidden" name="summit-gpx-file" id="summit-gpx-file">
            <input type="hidden" name="last-peak-added-lat" id="last-peak-added-lat">
            <input type="hidden" name="last-peak-added-lng" id="last-peak-added-lng">
            <!--STEP 2-->

            <div class="row content-pane-main-row">

                <div class="col-md-5" style="background-color: #f6f6f6;">

                    <div id="peaks-you-climbed-container" class="row row-full-width" style="padding-top: 15px;">
                        <div class="col-md-12">
                            <fieldset class="peaksYouClimbed">
                                <div id="peaks-climbed-date" class="field-title">Peak you climbed</div>
                                <div class="field-title-spacer"></div>
                                <div id="peaks-climbed-list" style="display: none;"></div>

                                <div id="peaks-climbed-map-container" style="display: none;">
                                    <div id="peak-location-search-container" style="width: 100%; height: 70px; background-color: #f6f6f6;">
                                        <div id="peak-name-search-div" style="float: left; margin-right: 20px; margin-bottom: 20px;"><input placeholder="peak name (optional)..." type="text" name="peak-name-search" id="peak-name-search" style="border-radius: 12px; padding: 10px 10px 10px 15px; width: 100%; min-width: 280px; max-width: 345px;"></div>
                                        <div id="peak-location-search-div" style="float: left; margin-right: 20px; margin-bottom: 20px;"><input placeholder="location..." type="text" name="peak-location-search" id="peak-location-search" style="border-radius: 12px; padding: 10px 10px 10px 15px; width: 100%; min-width: 280px; max-width: 345px;"></div>
                                        <div id="btnSearchPeakMapDiv" style="float: left; margin-right: 20px; width: 100%;"><button type="button" id="btnSearchPeakMap" class="btn btn-secondary" style="height: 54px; width: 122px; margin-top: 1px; margin-left: 2px; border-radius: 12px; font-size: 16px; text-align: center;">Search map</button></div>
                                        <div id="btnCancelSearchPeakMapDiv" style="display: none; float: left; margin-right: 20px;"><button type="button" id="btnCancelSearchPeakMap" class="btn btn-primary" style="height: 52px; width: 86px; margin-top: 1px; margin-left: 2px;">Cancel</button></div>
                                        <div style="float: left; margin-top: 12px;"><span class="search-by-peak-name-title"><a href="javascript: void(0);" id="search-by-peak-name-link">Search by peak name</a></span></div>
                                    </div>
                                    <div id="peaks-climbed-map-instructions" style="width: 100%; display: none;">
                                        <div id="peaks-climbed-map-instructions-text" style="float: left; margin-right: 20px; width: 100%;"><span class="header-help" style="margin-left: 0px; color: #333; font-size: 18px; font-weight: 500;">Select a peak from the map</span></div>
                                    </div>
                                    <div id="peak-search-map-instructions" style="width: 100%; height: 40px; display: none;">
                                        <div style="float: left; margin-right: 20px;"><span class="header-help" style="margin-left: 0px;">Click a peak on the map to add</span></div>
                                    </div>
                                    <div id="map-canvas" style="display: none; width: 100%; height: 100%;">
                                        <div id="gm-custom-mapunits" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 100px; top: 0px;">
                                            <div id="gm-custom-mapunitsbutton" draggable="false" title="Change map units" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: center; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                                                <span id="gm-custom-mapunitsbutton-label-feet" class="gm-custom-mapunits-selected">Feet</span> | <span id="gm-custom-mapunitsbutton-label-meters" class="gm-custom-mapunits-unselected">Meters</span>
                                            </div>
                                        </div>
                                        <div id="gm-custom-maptype" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px;">
                                            <div id="gm-custom-mapbutton" draggable="false" title="Change map style" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: left; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                                                <span id="gm-custom-mapbutton-label">Terrain</span><img src="https://maps.gstatic.com/mapfiles/arrow-down.png" draggable="false" style="-webkit-user-select: none; border: 0px; padding: 0px; margin: -2px 0px 0px; position: absolute; right: 6px; top: 50%; width: 7px; height: 4px;">
                                            </div>
                                            <div id="gm-custom-mapdropdown" style="background-color: white; z-index: -1; padding-left: 2px; padding-right: 2px; border-bottom-left-radius: 2px; border-bottom-right-radius: 2px; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; position: absolute; top: 100%; left: 0px; right: 0px; text-align: left; display: none;">
                                                <div id="gm-custom-mapoption-terrain" draggable="false" title="Show street map with terrain" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                    Terrain
                                                </div>
                                                <div id="gm-custom-mapoption-natatl" draggable="false" title="Show natural atlas" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                    Natural Atlas (US)
                                                </div>
                                                <div id="gm-custom-mapoption-outdoors" draggable="false" title="Show open topo map" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                    OpenTopoMap
                                                </div>
                                                <div id="gm-custom-mapoption-topo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                    Topo Govt (as avail)
                                                </div>
                                                <div id="gm-custom-mapoption-satstreets" draggable="false" title="Show satellite imagery with street map" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                    Satellite
                                                </div>
                                                <div id="gm-custom-mapoption-sat" draggable="false" title="Show satellite imagery" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                    Satellite Topo
                                                </div>
                                            </div>
                                        </div>

                                        <div id="message_map_div" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px; display: none;">
                                            <div id="message_map" draggable="false" style="direction: ltr; overflow: hidden; text-align: left; position: relative; color: rgb(86, 86, 86); font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; border-bottom-left-radius: 2px; border-top-left-radius: 2px; border-bottom-right-radius: 2px; border-top-right-radius: 2px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">

                                            </div>
                                        </div>

                                        <div id="marker-tooltip" data-url="" data-index="" style="z-index: 2; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; cursor: pointer; opacity: 0; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>

                                    </div>
                                </div>

                                <div id="peaks-climbed-search-container">
                                    <div id="peak-search-container" style="float: left;">
                                        <input type="text" name="peak-search" id="peak-search" placeholder="peak name...">
                                        <div id="peak-search-icon-div">
                                            <a id="peak-search-icon" href="javascript: void(0);" class="searchbox-icon"><i class="fa fa-search"></i></a>
                                        </div>
                                    </div>
                                    <div id="find-on-map" style="float: left; margin-top: 12px;">
                                        <span class="find-on-map-title"><a href="javascript: void(0);" id="find-on-map-link"><i class="fa fa-map"></i> Find peak on map</a></span>
                                    </div>
                                    <div id="add-another-peak" style="display: none;">
                                        <span class="add-another-peak-title"><a href="javascript: void(0);" id="add-another-peak-link">Add another peak</a> <span class="hidden-xs add-another-peak-help">only if climbed on same day</span></span>
                                    </div>
                                </div>

                                <input value="" name="climbed_peak_ids" id="climbed_peak_ids" type="hidden">
                            </fieldset>
                        </div>
                    </div>

                    <div id="summit-date-container" class="row row-full-width">
                        <div class="col-md-12">
                            <fieldset>
                                <div class="form-group" id="summit-field-date">
                                    <span class="field-title" style="display: block;">Date of climb<span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">estimate if exact date unknown</span></span>
                                    <div class="field-title-spacer"></div>
                                    <div class="ui-datepicker-title" style="width: 100%;">
                                        <select class="ui-datepicker-month" style="text-indent: 10px; text-align: left; text-align-last: left; background-position-x: 35px;">
                                            <option value="0">Jan</option>
                                            <option value="1">Feb</option>
                                            <option value="2">Mar</option>
                                            <option value="3">Apr</option>
                                            <option value="4">May</option>
                                            <option value="5">Jun</option>
                                            <option value="6">Jul</option>
                                            <option value="7">Aug</option>
                                            <option value="8">Sep</option>
                                            <option value="9">Oct</option>
                                            <option value="10">Nov</option>
                                            <option value="11">Dec</option>
                                        </select>
                                        <select class="ui-datepicker-year" style="text-indent: 5px; text-align: left; text-align-last: left; margin-left: 0px; background-position-x: 35px;"></select>
                                    </div>
                                    <div id="date" style="width: 100%;"></div>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                </div>

                <div class="col-md-7" style="background-color: #f2f2f2; min-height: 100vh;">

                    <div class="row row-full-width summitlog-section">
                        <div class="col-md-12">
                            <fieldset class="whatHappened">
                                <span class="field-title" style="display: block;">Trip report</span>
                                <div class="field-title-spacer"></div>
                                <textarea onkeyup="textAreaAdjust();" name="log" class="route-step-text" id="summit" style="display: block; height: 100px; resize: none; width: 100%; border: 1px solid #CCC; padding: 8px 10px; overflow: hidden;" placeholder="what happened out there? share your story...">{% if summit.log %}{{ summit.log }}{% endif %}</textarea>
                            </fieldset>
                        </div>
                    </div>

                    <div class="row row-full-width" style="padding-top: 15px;">
                        <div class="col-md-12">
                            <span class="field-title" style="display: block;">Photos<span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">add JPG photos from your climb</span></span>
                            <div class="field-title-spacer"></div>
                            <div id="uploader"></div>
                        </div>
                    </div>

                    {% if default_route_name %}

                        <div id="summit-route-container" class="row row-full-width summitlog-section">
                            <div class="col-md-12">
                                <span class="field-title" style="display: block;">Route</span>
                                <div class="field-title-spacer"></div>
                                <fieldset id="routes">
                                    <div class="clearfix routeUp" style="width: 100%;">
                                        <span class="a arrowUp onFocus" style="padding-bottom: 0px;">
                                            <div id="route_up_container">
                                                <select name="route_up" id="route_up" style="cursor: pointer;"></select>
                                                <div style="font-size: 26px; float: right; margin-top: -40px; margin-right: 10px; color: #666;"><i class="fa fa-angle-down"></i></div>
                                            </div>
                                        </span>
                                    </div>
                                    <input type="text" name="summit-route-name" id="summit-route-name" style="padding-left: 10px; width: 100%; height: 55px; display: none;" placeholder="route name...">
                                    <div id="hide-new-summit-route-span" style="position: absolute; right: 30px; font-size: 20px; color: #cccccc; display: none;"><a id="hide-new-summit-route" style="color: #ccc;" class="ajax-link"><i class="fa fa-times"></i></a></div>
                                </fieldset>
                            </div>
                        </div>

                    {% endif %}

                    <div id="add-more-info-container" class="row row-full-width summitlog-section">
                        <div class="col-md-12">
                            <div id="add-more-info">
                                <span class="add-more-info-title"><a href="javascript: void(0);" id="add-more-info-link">Add more info</a> <span class="hidden-xs add-another-peak-help">companions, obstacles, key gear, and more</span></span>
                            </div>
                        </div>
                    </div>

                    <div class="row row-full-width hidden-xs summitlog-section summitlog-extras summitlog-section-hidden">
                        <div class="col-md-12">
                            <fieldset>
                                <div id="summit-field-triptype">
                                    <span class="field-title" style="display: block;">Trip type</span>
                                    <div style="margin-left: 5px;">
                                        <div class="btn-group" data-toggle="buttons">
                                            <label class="btn" style="margin-right: 15px;">
                                            <input type="radio" class="trip-type-input" name="rdoTripType" id="rdoOutAndBack" value="out-and-back"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  out-and-back</span>
                                            </label>
                                            <label class="btn" style="margin-right: 15px;">
                                            <input type="radio" class="trip-type-input" name="rdoTripType" id="rdoLoop" value="loop"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> loop</span>
                                            </label>
                                            <label class="btn">
                                            <input type="radio" class="trip-type-input" name="rdoTripType" id="rdoPointToPoint" value="point-to-point/traverse"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> point-to-point/traverse</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                    <div class="row row-full-width hidden-xs summitlog-section summitlog-extras summitlog-section-hidden">
                        <div class="col-md-12" style="padding-left: 0px; padding-right: 0px;">
                            <fieldset>
                                <div id="summit-field-trip-activities">
                                    <span class="field-title" style="display: block; margin-left: 15px;">Trip activities <span class="hidden-xs header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">select all that apply</span></span>
                                    <div class="field-title-spacer"></div>
                                    <div id="activity-tag-choices">
                                        <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="hiking"><label>hiking</label></div>
                                        <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="scrambling"><label>scrambling</label></div>
                                        <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="rock climbing"><label>rock climbing</label></div>
                                        <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="ice climbing"><label>ice climbing</label></div>
                                        <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="mountaineering"><label>mountaineering</label></div>
                                        <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="glacier climb"><label>glacier climb</label></div>
                                        <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="skiing"><label>skiing</label></div>
                                        <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="snowboarding"><label>snowboarding</label></div>
                                        <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="snowshoeing"><label>snowshoeing</label></div>
                                        <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="biking"><label>biking</label></div>
                                        <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="running"><label>running</label></div>
                                        <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="glissading"><label>glissading</label></div>
                                    </div>
                                    <div style="margin-top: 10px; margin-left: 15px; float: left; width: 100%;">
                                        <input type="text" name="txtAddActivityTag" id="txtAddActivityTag" style="padding-left: 10px; line-height: 38px;" placeholder="other activity...">
                                        <button type="button" id="btnAddActivityTag" class="btn btn-secondary" style="height: 56px; margin-top: -2px; margin-left: 2px;">Add</button>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                    <div class="row row-full-width hidden-xs summitlog-section summitlog-extras summitlog-section-hidden">
                        <div class="col-md-12" style="padding-left: 0px; padding-right: 0px;">
                            <fieldset>
                                <div id="summit-field-trip-challenges">
                                    <span class="field-title" style="display: block; margin-left: 15px;">Obstacles <span class="hidden-xs header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">select all that apply</span></span>
                                    <div class="field-title-spacer"></div>
                                    <div id="challenge-tag-choices">
                                        <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="road/access issues"><label>road/access issues</label></div>
                                        <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="routefinding"><label>routefinding</label></div>
                                        <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="stream crossing"><label>stream crossing</label></div>
                                        <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="bushwhacking"><label>bushwhacking</label></div>
                                        <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="blowdowns"><label>blowdowns</label></div>
                                        <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="no water source"><label>no water source</label></div>
                                        <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="rockfall/loose rock"><label>rockfall/loose rock</label></div>
                                        <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="snow on route"><label>snow on route</label></div>
                                        <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="avalanche danger"><label>avalanche danger</label></div>
                                        <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="crevasse danger"><label>crevasse danger</label></div>
                                        <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="buggy"><label>buggy</label></div>
                                        <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="weather"><label>weather</label></div>
                                    </div>
                                    <div style="margin-top: 10px; margin-left: 15px; float: left; width: 100%;">
                                        <input type="text" name="txtAddChallengeTag" id="txtAddChallengeTag" style="padding-left: 10px; line-height: 38px;" placeholder="other obstacle...">
                                        <button type="button" id="btnAddChallengeTag" class="btn btn-secondary" style="height: 56px; margin-top: -2px; margin-left: 2px;">Add</button>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                    <div class="row row-full-width hidden-xs summitlog-section summitlog-extras summitlog-section-hidden">
                        <div class="col-md-12" style="padding-left: 0px; padding-right: 0px;">
                            <fieldset>
                                <div id="summit-field-trip-gear">
                                    <span class="field-title" style="display: block; margin-left: 15px;">Key gear <span class="hidden-xs header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">gear used or recommended</span></span>
                                    <div class="field-title-spacer"></div>
                                    <div id="gear-tag-choices">
                                        <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="ice axe"><label>ice axe</label></div>
                                        <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="crampons"><label>crampons</label></div>
                                        <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="helmet"><label>helmet</label></div>
                                        <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="rope/harness"><label>rope/harness</label></div>
                                        <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="climbing rack"><label>climbing rack</label></div>
                                        <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="skis"><label>skis</label></div>
                                        <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="snowboard"><label>snowboard</label></div>
                                        <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="snowshoes"><label>snowshoes</label></div>
                                        <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="bike"><label>bike</label></div>
                                        <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="trekking poles"><label>trekking poles</label></div>
                                        <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="mountaineering boots"><label>mountaineering boots</label></div>
                                        <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="GPS device"><label>GPS device</label></div>
                                    </div>
                                    <div style="margin-top: 10px; margin-left: 15px; float: left; width: 100%;">
                                        <input type="text" name="txtAddGearTag" id="txtAddGearTag" style="padding-left: 10px; line-height: 38px;" placeholder="other key gear...">
                                        <button type="button" id="btnAddGearTag" class="btn btn-secondary" style="height: 56px; margin-top: -2px; margin-left: 10px;">Add</button>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                    <div class="row row-full-width summitlog-section summitlog-extras summitlog-section-hidden">
                        <div class="col-md-12">
                            <fieldset>
                                <div id="summit-field-companions">
                                    <span class="field-title" style="display: block;">Companions</span>
                                    <div class="field-title-spacer"></div>
                                    <div id="summit-companions">
                                    </div>
                                    <div id="member-search-container">
                                        <input type="text" class="member-searchbox-input" name="summitmemq1" id="summitmemq1" style="max-width: 422px; padding-left: 10px; line-height: 38px;" placeholder="name or peakery member..." onkeyup="memberButtonUp();">
                                        <button type="button" id="btnAddSummitCompanion" class="btn btn-secondary" style="height: 56px; margin-top: -2px; font-size: 14px; font-weight: 500;">Add</button>
                                        <div id="member-search-icon-div" style="display: none;">
                                            <a id="member-search-icon" href="javascript: void(0);" class="searchbox-icon"><i class="fa fa-times"></i></a>
                                        </div>
                                        <div id="member-search-button-div">

                                        </div>
                                        <input value="" name="fellow_selected_users" id="fellow_selected_users" type="hidden">
                                        <input value="" name="fellow_nonmember_baggers" id="fellow_nonmember_baggers" type="hidden">
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                        <div id="mobile-companions-spacer" class="hidden-sm hidden-md hidden-lg" style="height: 270px;"></div>
                    </div>

                    <div class="row row-full-width hidden-xs hidden-sm summitlog-section summitlog-extras summitlog-section-hidden">
                        <div class="col-md-12">
                            <fieldset>
                                <div id="summit-field-video-link">
                                    <span class="field-title" style="display: block;">Your videos</span>
                                    <div class="field-title-spacer"></div>
                                    <div id="summit-videos">
                                    </div>
                                    <div>
                                        <input type="text" name="summit-video-link" id="summit-video-link" style="width: 50%; padding-left: 10px; line-height: 38px;" placeholder="YouTube or Vimeo link...">
                                        <button type="button" id="btnAddVideoLink" class="btn btn-secondary" style="width: 150px; height: 56px; margin-top: -2px; margin-left: 10px;">Add</button>
                                        <input value="" name="summit_video_links" id="summit_video_links" type="hidden">
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                    <div class="row row-full-width hidden-xs hidden-sm summitlog-section summitlog-extras summitlog-section-hidden">
                        <div class="col-md-12">
                            <fieldset>
                                <div id="summit-field-related-link">
                                    <span class="field-title" style="display: block;">Related links</span>
                                    <div class="field-title-spacer"></div>
                                    <div id="summit-related-links">
                                    </div>
                                    <div>
                                        <input type="text" name="summit-related-link" id="summit-related-link" style="width: 50%; padding-left: 10px; line-height: 38px;" placeholder="link to your blog post, photo set, etc...">
                                        <button type="button" id="btnAddRelatedLink" class="btn btn-secondary" style="width: 150px; height: 56px; margin-top: -2px; margin-left: 10px;">Add</button>
                                        <input value="" name="summit_related_links" id="summit_related_links" type="hidden">
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                </div>

            </div>

        </div>

    </div>

    <div class="message-modal modal fade" id="message-modal" tabindex="-1" role="dialog" aria-labelledby="message-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="message-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="message-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="confirm-modal modal fade" id="confirm-modal" tabindex="-1" role="dialog" aria-labelledby="confirm-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="confirm-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="confirm-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="savetext-modal modal fade" id="savetext-modal" tabindex="-1" role="dialog" aria-labelledby="savetext-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="savetext-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="savetext-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<style type="text/css">
    .ui-autocomplete {
        max-height: 100px;
        overflow-y: auto;
        /* prevent horizontal scrollbar */
        overflow-x: hidden;
        /* add padding to account for vertical scrollbar */
        padding-right: 20px;
    }
        /* IE 6 doesn't support max-height
       * we use height instead, but this forces the menu to always be this tall
       */
    * html .ui-autocomplete {
        height: 100px;
    }
</style>

<script type="text/javascript">

var map;
var points = [];
var linePoints = [];
var lineData;
var viewer;
var topo;
var outdoors;
var center = null;
var map_bounds;
var photos_displayed = 0;
var photos_page = 1;
var photos = [];
var map_adjusted = false;
var pageX, pageY, mapX, mapY;
var iconstyle;
{% if init_gpx_file %}
var init_gpx = true;
{% else %}
var init_gpx = false;
{% endif %}
var geocoding = false;
var map_init = false;
var searched_by_map = false;

var markersArray = [];

function hideMapTooltip() {
    if ($('#marker-tooltip').hasClass('scale-in-tl')) {
        $('#marker-tooltip').addClass('scale-out-tl').removeClass('scale-in-tl');
    } else if ($('#marker-tooltip').hasClass('scale-in-tm')) {
        $('#marker-tooltip').addClass('scale-out-tm').removeClass('scale-in-tm');
    } else if ($('#marker-tooltip').hasClass('scale-in-tr')) {
        $('#marker-tooltip').addClass('scale-out-tr').removeClass('scale-in-tr');
    } else if ($('#marker-tooltip').hasClass('scale-in-bl')) {
        $('#marker-tooltip').addClass('scale-out-bl').removeClass('scale-in-bl');
    } else if ($('#marker-tooltip').hasClass('scale-in-bm')) {
        $('#marker-tooltip').addClass('scale-out-bm').removeClass('scale-in-bm');
    } else if ($('#marker-tooltip').hasClass('scale-in-br')) {
        $('#marker-tooltip').addClass('scale-out-br').removeClass('scale-in-br');
    }
}

function loadPeaks() {

    if (init_gpx) {

        //get map bounds
        var bounds = map.getBounds();

        var counter = 0;
        var strTemp = '';
        var LatLngList = [];

        var params = '';
        params = params + '&q=';
        params = params + '&n=';
        params = params + '&elev_min=0';
        params = params + '&elev_max=29500';
        params = params + '&prom_min=0';
        params = params + '&prom_max=29500';
        params = params + '&summits_min=0';
        params = params + '&summits_max=500';
        params = params + '&difficulty_min=1';
        params = params + '&difficulty_max=5';
        params = params + '&lat=';
        params = params + '&lng=';
        params = params + '&bounds=' + bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng;

        //update hidden parameters
        map_bounds = bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng;

        if (params.length > 0) params = '?' + params.slice(-1 * (params.length - 1));
        var byRegion = false;
        var totalPeaks = 0;
        $.getJSON('{% url "peaks_map" %}' + params + '&get_regions=true', function (data) {
            $.each(data, function (key, val) {
                var currentRequest = true;
                if (key == 'parameters') {
                    $.each(val, function (parameterkey, parameterval) {
                        if (parameterval.bounds != map_bounds) currentRequest = false;
                    });
                }

                if (!currentRequest) {
                    return false;
                }

                if (key == 'peaks') {

                    var havePeaks = false;

                    $.each(val, function (peakkey, peakval) {

                        if (!havePeaks) {

                            //first time through, delete highest peak marker and remove any markers not on map
                            deletehighest();
                            //delete markers out of margins
                            delete_old_markers(val);

                        }

                        havePeaks = true;

                        //build country string
                        var country = '';
                        var climbed_peak_country = '';
                        $.each( peakval.country, function( countrykey, countryval ) {
                            country = country + '<div><a href="' + countryval.country_slug + '/">' + countryval.country_name + '</a></div>';
                            climbed_peak_country = climbed_peak_country + countryval.country_name + ' / ';
                        });
                        climbed_peak_country = climbed_peak_country.substr(0, climbed_peak_country.length-3);
                        if (country == '') {
                            country = 'Unknown Country';
                            climbed_peak_country = 'Unknown Country';
                        }

                        //build region string
                        var region = '';
                        var mobile_region = '';
                        var climbed_peak_region = '';
                        var region_bull_class = 'hidden-lg hidden-md hidden-sm hidden-xs';
                        $.each( peakval.region, function( regionkey, regionval ) {
                            region_bull_class = '';
                            region = region + '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.region_name + ', ' + regionval.country_name + '</a></div>';
                            mobile_region = '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.country_name + '</a></div>';
                            climbed_peak_region = climbed_peak_region + regionval.region_name + ', ' + regionval.country_name + ' / ';
                        });
                        climbed_peak_region = climbed_peak_region.substr(0, climbed_peak_region.length-3);
                        if (region == '') {
                            region = country;
                            mobile_region = country;
                            climbed_peak_region = climbed_peak_country;
                        }

                        //build challenges string
                        var challenges = '';
                        var challenge_count = peakval.challenge_count;
                        if (challenge_count > 0) {
                            challenges = ' &bull; <span>' + challenge_count + ' <i class="fa fa-trophy"></i></span>';
                        }

                        //buld distance string
                        var distance = '';

                        //build summits string
                        var summits, tooltip_your_summits;
                        if (peakval.summit_count > 0) {
                            summits = '<a href="/' + peakval.slug + '/summits/">' + peakval.summit_count + '</a>';
                        } else {
                            summits = '<a href="/' + peakval.slug + '/summits/">0</a><br /><a href="/' + peakval.slug + '/">Be the first!</a>';
                        }
                        if (peakval.your_summits > 0) {
                            summits = summits + '<br /><span style="color: #3fc663; font-weight: 500;">' + peakval.your_summits + 'x by you</span>';
                            tooltip_your_summits = '&nbsp;<span style="color: #3fc663; font-weight: 500;">(You ' + peakval.your_summits + 'x)</span>';
                        } else {
                            tooltip_your_summits = '';
                        }

                        //tooltip vars
                        var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left, tooltip_total_width, tooltip_total_height;

                        //show tooltip badges?
                        var showClassic = 'display: none;';
                        var showChallenges = 'display: none;';
                        var showYourSummits = 'display: none;';
                        if (peakval.is_classic == 'True') {
                            showClassic = '';
                        }
                        if (peakval.challenge_count > 0) {
                            showChallenges = '';
                        }
                        if (peakval.your_summits > 0) {
                            showYourSummits = '';
                        }

                        peaklist_html = '';

                        //show feet or meters?
                        var showFeet = 'display: none;';
                        var showMeters = 'display: none;';
                        if ($('#bt_showinmeters').hasClass('meters')) {
                            showMeters = '';
                        } else {
                            showFeet = '';
                        }

                        //build tooltip string
                        if (peakval.thumbnail_url.indexOf('default.png.350x245_q95_crop.jpg') < 0) {
                            tooltip_html = '<img class="map-tooltip-img hover-photos-hover" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px;" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-peak-badges" style="font-weight:500; top: 115px; width: 220px; position: absolute; background-color: transparent; background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%); padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; bottom: -35px; height: 100px; background-color: transparent !important;"><div style="margin-left: -8px; margin-top: 43px; height: 55px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 210px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; padding-left: 10px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div>';
                            tooltip_width = 220;
                            tooltip_height = 165;
                            tooltip_total_width = 250;
                            tooltip_total_height = 230;
                            peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-photos" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: -60px;"><img class="map-tooltip-img hover-photos-hover" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; position: relative;" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-peak-badges" style="font-weight:500; top: -52px; width: 220px; position: relative; background-color: transparent; background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%); padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; position: relative; left: inherit; top: -65px; width: 220px; height: 100px; background-color: transparent !important;"><div class="peak-listitem-footer" style="margin-left: -8px; margin-top: 43px; height: 55px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff;"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 210px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; padding-left: 10px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></div></a>';
                        } else {
                            if (peakval.is_classic == 'True' || peakval.challenge_count > 0 || peakval.your_summits > 0) {
                                tooltip_html = '<div class="map-tooltip-peak-badges" style="font-weight:500; top: 4px; z-index: 2; width: 220px; position: absolute; background-color: transparent; padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="height: 75px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="margin-top: 25px; color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div>';
                                tooltip_width = 220;
                                tooltip_height = 75;
                                tooltip_total_width = 250;
                                tooltip_total_height = 105;
                                peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-photos" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: 5px;"><div class="map-tooltip-peak-badges" style="font-weight:500; top: 4px; z-index: 2; width: 220px; position: relative; background-color: transparent; padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info peak-listitem-footer" style="height: 80px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; position: relative;"><div class="map-tooltip-peak-name ellipsis" style="margin-top: 25px; color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></a>';
                            } else {
                                tooltip_html = '<div class="map-tooltip-info" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div>';
                                tooltip_width = 220;
                                tooltip_height = 50;
                                tooltip_total_width = 250;
                                tooltip_total_height = 80;
                                peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-photos" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: 5px;"><div class="map-tooltip-info peak-listitem-footer" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; position: relative; height: 54px;"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></a>';
                            }
                        }

                        var tooltip_url = '/' + peakval.slug;

                        var latLng = new mapboxgl.LngLat(peakval.lng, peakval.lat);

                        if (counter == 0) {
                            //highest peak gets red icon
                            iconstyle = 'marker_icon_red';
                        } else if (peakval.your_summits > 0) {
                            //if you have summited then green icon
                            iconstyle = 'marker_icon_green';
                        } else if (peakval.your_attempts > 0) {
                            //if you have attempted then yellow icon
                            iconstyle = 'marker_icon_yellow';
                        } else {
                            iconstyle = 'marker_icon';
                        }

                        var is_draggable = false;

                        //check if already exist so don't put again
                        var exists = false;
                        for (i = markersArray.length-1; i>=0; i--){
                            if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng)){
                                exists = true ;
                                //if the highest is in the actual viewport, not as the highest, delete it
                                if (iconstyle=='marker_icon_red' || iconstyle=='marker_icon_redgreen' || iconstyle=='marker_icon_green') {
                                    markersArray[i].remove();
                                    markersArray.splice(i,1);
                                    exists = false;
                                }
                            }
                        }

                        if (!exists) {
                            var latLng = [peakval.lng, peakval.lat];
                            //add marker
                            //create an HTML element for the marker
                            var el = document.createElement('div');
                            el.className = iconstyle;

                            el.addEventListener('click', function(e) {
                                if (isTouchDevice()) {

                                    $('#marker-tooltip').data('peak_id',peakval.id);
                                    $('#marker-tooltip').data('peak_name',peakval.name);
                                    $('#marker-tooltip').data('peak_region',climbed_peak_region);
                                    $('#marker-tooltip').data('peak_elevation',peakval.elevation);
                                    $('#marker-tooltip').data('peak_summit_count',peakval.summit_count);
                                    $('#marker-tooltip').data('peak_thumbnail_url',peakval.thumbnail_url);
                                    $('#marker-tooltip').data('peak_lat',peakval.lat);
                                    $('#marker-tooltip').data('peak_lng',peakval.lng);
                                    $('#marker-tooltip').removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                    $('#marker-tooltip').addClass(showClass);
                                    $('#marker-tooltip').data('url', marker.properties.tooltipUrl);

                                    hideMapTooltip();

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();
                                    var showClass = 'scale-in-';
                                    var hideClass = 'scale-out-';

                                    var markerXOffset = 15;
                                    var markerX = this.getBoundingClientRect().x + 14 - markerXOffset;

                                    var markerYOffset = document.getElementById('map-canvas').getBoundingClientRect().y;
                                    var markerY = this.getBoundingClientRect().y + 14 - markerYOffset;

                                    if (markerY < (bottom / 2)) {
                                        marker_top = markerY;
                                        showClass = showClass + 't';
                                        hideClass = hideClass + 't';
                                    } else {
                                        marker_top = markerY - tooltip_total_height - 15;
                                        showClass = showClass + 'b';
                                        hideClass = hideClass + 'b';
                                    }

                                    if (markerX < (right / 3)) {
                                        marker_left = markerX;
                                        showClass = showClass + 'l';
                                        hideClass = hideClass + 'l';
                                    } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                        marker_left = markerX - (tooltip_total_width / 2);
                                        showClass = showClass + 'm';
                                        hideClass = hideClass + 'm';
                                    } else {
                                        marker_left = markerX - tooltip_total_width;
                                        showClass = showClass + 'r';
                                        hideClass = hideClass + 'r';
                                    }

                                    $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    }).addClass(showClass).removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                    if ($('#bt_showinmeters').hasClass('meters')) {
                                        $('#marker-tooltip').find('.peak-elevation-feet').hide();
                                        $('#marker-tooltip').find('.peak-elevation-meters').show();
                                    } else {
                                        $('#marker-tooltip').find('.peak-elevation-meters').hide();
                                        $('#marker-tooltip').find('.peak-elevation-feet').show();
                                    }
                                } else {
                                    //console.log(peakval.slug);
                                    addPeakToLog(peakval.id, peakval.name, climbed_peak_region, peakval.elevation, peakval.summit_count, peakval.thumbnail_url, 0, 'False');
                                    $('#last-peak-added-lat').val(peakval.lat);
                                    $('#last-peak-added-lng').val(peakval.lng);
                                }
                                e.stopPropagation();
                            });

                            el.addEventListener('mouseover', function(e) {

                                if (!isTouchDevice()) {

                                    $('#marker-tooltip').data('peak_id',peakval.id);
                                    $('#marker-tooltip').data('peak_name',peakval.name);
                                    $('#marker-tooltip').data('peak_region',climbed_peak_region);
                                    $('#marker-tooltip').data('peak_elevation',peakval.elevation);
                                    $('#marker-tooltip').data('peak_summit_count',peakval.summit_count);
                                    $('#marker-tooltip').data('peak_thumbnail_url',peakval.thumbnail_url);
                                    $('#marker-tooltip').data('peak_lat',peakval.lat);
                                    $('#marker-tooltip').data('peak_lng',peakval.lng);
                                    $('#marker-tooltip').data('url', marker.properties.tooltipUrl);

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();
                                    var showClass = 'scale-in-';
                                    var hideClass = 'scale-out-';

                                    var markerXOffset = 15;
                                    var markerX = this.getBoundingClientRect().x + 14 - markerXOffset;

                                    var markerYOffset = document.getElementById('map-canvas').getBoundingClientRect().y;
                                    var markerY = this.getBoundingClientRect().y + 14 - markerYOffset;

                                    if (markerY < (bottom / 2)) {
                                        marker_top = markerY;
                                        showClass = showClass + 't';
                                        hideClass = hideClass + 't';
                                    } else {
                                        marker_top = markerY - tooltip_total_height - 15;
                                        showClass = showClass + 'b';
                                        hideClass = hideClass + 'b';
                                    }

                                    if (markerX < (right / 3)) {
                                        marker_left = markerX;
                                        showClass = showClass + 'l';
                                        hideClass = hideClass + 'l';
                                    } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                        marker_left = markerX - (tooltip_total_width / 2);
                                        showClass = showClass + 'm';
                                        hideClass = hideClass + 'm';
                                    } else {
                                        marker_left = markerX - tooltip_total_width;
                                        showClass = showClass + 'r';
                                        hideClass = hideClass + 'r';
                                    }

                                    $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    }).addClass(showClass).removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                    if ($('#bt_showinmeters').hasClass('meters')) {
                                        $('#marker-tooltip').find('.peak-elevation-feet').hide();
                                        $('#marker-tooltip').find('.peak-elevation-meters').show();
                                    } else {
                                        $('#marker-tooltip').find('.peak-elevation-meters').hide();
                                        $('#marker-tooltip').find('.peak-elevation-feet').show();
                                    }
                                }
                                e.stopPropagation();

                            });

                            el.addEventListener('mouseout', function(e) {
                                if (isTouchDevice()) {
                                    //$('#marker-tooltip').hide();
                                } else {
                                    hideMapTooltip();
                                }
                            });

                            var marker = new mapboxgl.Marker(el)
                                .setLngLat(latLng)
                                .setOffset([-5, -10])
                                .setDraggable(is_draggable);

                            marker.properties = {};
                            marker.properties.tooltipContent = tooltip_html;
                            marker.properties.tooltipUrl = tooltip_url;
                            marker.properties.iconstyle = iconstyle;
                            marker.properties.peakid = peakval.id;

                            markersArray.push(marker);
                            LatLngList.push(latLng);

                        }

                        counter++;
                    });

                    for (var i = markersArray.length - 1; i >= 0; --i) {
                        markersArray[i].addTo(map);
                    }

                    if (!havePeaks) {
                        //didn't have any peaks, so remove all markers
                        delete_old_markers(val);
                    }
                    map_init = false;
                }
            });
        });

    }

}

function loadPeaksFromMapSearch(bounds) {

    var keyword = $('#peak-name-search').val();
    var near = $('#peak-location-search').val();

    if (keyword != '' || near != '') {

        $('#btnSearchPeakMap').html('<i class="fa fa-spinner fa-spin"></i>');
        $('#btnSearchPeakMap').prop('disabled', true);
        $('#btnCancelSearchPeakMap').prop('disabled', true);
        $('#peak-name-search').prop('disabled', true);
        $('#peak-location-search').prop('disabled', true);

        if (!bounds) {
            map_init = true;
        }

        if (1 == 1) {

            var counter = 0;
            var strTemp = '';
            var LatLngList = [];

            var params = '';
            params = params + '&q='+keyword;
            params = params + '&n=';
            params = params + '&elev_min=0';
            params = params + '&elev_max=29500';
            params = params + '&prom_min=0';
            params = params + '&prom_max=29500';
            params = params + '&summits_min=0';
            params = params + '&summits_max=500';
            params = params + '&difficulty_min=1';
            params = params + '&difficulty_max=5';
            params = params + '&lat=';
            params = params + '&lng=';
            if (bounds) {
                params = params + '&near=true&bounds=' + bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng;
            }

            if (params.length > 0) params = '?' + params.slice(-1 * (params.length - 1));
            var byRegion = false;
            var totalPeaks = 0;
            $.getJSON('{% url "peaks_map" %}' + params + '&get_regions=true', function (data) {
                $.each(data, function (key, val) {

                    if (key == 'peaks') {

                        var havePeaks = false;

                        $.each(val, function (peakkey, peakval) {

                            havePeaks = true;

                            //build country string
                            var country = '';
                            var climbed_peak_country = '';
                            $.each( peakval.country, function( countrykey, countryval ) {
                                country = country + '<div><a href="' + countryval.country_slug + '/">' + countryval.country_name + '</a></div>';
                                climbed_peak_country = climbed_peak_country + countryval.country_name + ' / ';
                            });
                            climbed_peak_country = climbed_peak_country.substr(0, climbed_peak_country.length-3);
                            if (country == '') {
                                country = 'Unknown Country';
                                climbed_peak_country = 'Unknown Country';
                            }

                            //build region string
                            var region = '';
                            var mobile_region = '';
                            var climbed_peak_region = '';
                            var region_bull_class = 'hidden-lg hidden-md hidden-sm hidden-xs';
                            $.each( peakval.region, function( regionkey, regionval ) {
                                region_bull_class = '';
                                region = region + '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.region_name + ', ' + regionval.country_name + '</a></div>';
                                mobile_region = '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.country_name + '</a></div>';
                                climbed_peak_region = climbed_peak_region + regionval.region_name + ', ' + regionval.country_name + ' / ';
                            });
                            climbed_peak_region = climbed_peak_region.substr(0, climbed_peak_region.length-3);
                            if (region == '') {
                                region = country;
                                mobile_region = country;
                                climbed_peak_region = climbed_peak_country;
                            }

                            //build challenges string
                            var challenges = '';
                            var challenge_count = peakval.challenge_count;
                            if (challenge_count > 0) {
                                challenges = ' &bull; <span>' + challenge_count + ' <i class="fa fa-trophy"></i></span>';
                            }

                            //buld distance string
                            var distance = '';

                            //build summits string
                            var summits, tooltip_your_summits;
                            if (peakval.summit_count > 0) {
                                summits = '<a href="/' + peakval.slug + '/summits/">' + peakval.summit_count + '</a>';
                            } else {
                                summits = '<a href="/' + peakval.slug + '/summits/">0</a><br /><a href="/' + peakval.slug + '/">Be the first!</a>';
                            }
                            if (peakval.your_summits > 0) {
                                summits = summits + '<br /><span style="color: #3fc663; font-weight: 500;">' + peakval.your_summits + 'x by you</span>';
                                tooltip_your_summits = '&nbsp;<span style="color: #3fc663; font-weight: 500;">(You ' + peakval.your_summits + 'x)</span>';
                            } else {
                                tooltip_your_summits = '';
                            }

                            //tooltip vars
                            var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left, tooltip_total_width, tooltip_total_height;

                            //show tooltip badges?
                            var showClassic = 'display: none;';
                            var showChallenges = 'display: none;';
                            var showYourSummits = 'display: none;';
                            if (peakval.is_classic == 'True') {
                                showClassic = '';
                            }
                            if (peakval.challenge_count > 0) {
                                showChallenges = '';
                            }
                            if (peakval.your_summits > 0) {
                                showYourSummits = '';
                            }

                            peaklist_html = '';

                            //show feet or meters?
                            var showFeet = 'display: none;';
                            var showMeters = 'display: none;';
                            if ($('#bt_showinmeters').hasClass('meters')) {
                                showMeters = '';
                            } else {
                                showFeet = '';
                            }

                            //build tooltip string
                            if (peakval.thumbnail_url.indexOf('default.png.350x245_q95_crop.jpg') < 0) {
                                tooltip_html = '<img class="map-tooltip-img hover-photos-hover" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px;" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-peak-badges" style="font-weight:500; top: 115px; width: 220px; position: absolute; background-color: transparent; background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%); padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; bottom: -35px; height: 100px; background-color: transparent !important;"><div style="margin-left: -8px; margin-top: 43px; height: 55px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 210px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; padding-left: 10px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div>';
                                tooltip_width = 220;
                                tooltip_height = 165;
                                tooltip_total_width = 250;
                                tooltip_total_height = 230;
                                peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-photos" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: -60px;"><img class="map-tooltip-img hover-photos-hover" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; position: relative;" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-peak-badges" style="font-weight:500; top: -52px; width: 220px; position: relative; background-color: transparent; background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%); padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; position: relative; left: inherit; top: -65px; width: 220px; height: 100px; background-color: transparent !important;"><div class="peak-listitem-footer" style="margin-left: -8px; margin-top: 43px; height: 55px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff;"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 210px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; padding-left: 10px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></div></a>';
                            } else {
                                if (peakval.is_classic == 'True' || peakval.challenge_count > 0 || peakval.your_summits > 0) {
                                    tooltip_html = '<div class="map-tooltip-peak-badges" style="font-weight:500; top: 4px; z-index: 2; width: 220px; position: absolute; background-color: transparent; padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="height: 75px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="margin-top: 25px; color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div>';
                                    tooltip_width = 220;
                                    tooltip_height = 75;
                                    tooltip_total_width = 250;
                                    tooltip_total_height = 105;
                                    peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-photos" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: 5px;"><div class="map-tooltip-peak-badges" style="font-weight:500; top: 4px; z-index: 2; width: 220px; position: relative; background-color: transparent; padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info peak-listitem-footer" style="height: 80px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; position: relative;"><div class="map-tooltip-peak-name ellipsis" style="margin-top: 25px; color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></a>';
                                } else {
                                    tooltip_html = '<div class="map-tooltip-info" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div>';
                                    tooltip_width = 220;
                                    tooltip_height = 50;
                                    tooltip_total_width = 250;
                                    tooltip_total_height = 80;
                                    peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-photos" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: 5px;"><div class="map-tooltip-info peak-listitem-footer" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; position: relative; height: 54px;"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></a>';
                                }
                            }

                            var tooltip_url = '/' + peakval.slug;

                            var latLng = new mapboxgl.LngLat(peakval.lng, peakval.lat);

                            if (counter == 0) {
                                //highest peak gets red icon
                                iconstyle = 'marker_icon_red';
                            } else if (peakval.your_summits > 0) {
                                //if you have summited then green icon
                                iconstyle = 'marker_icon_green';
                            } else if (peakval.your_attempts > 0) {
                                //if you have attempted then yellow icon
                                iconstyle = 'marker_icon_yellow';
                            } else {
                                iconstyle = 'marker_icon';
                            }

                            var is_draggable = false;

                            //check if already exist so don't put again
                            var exists = false;
                            for (i = markersArray.length-1; i>=0; i--){
                                if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng)){
                                    exists = true ;
                                    //if the highest is in the actual viewport, not as the highest, delete it
                                    if (iconstyle=='marker_icon_red' || iconstyle=='marker_icon_redgreen' || iconstyle=='marker_icon_green') {
                                        markersArray[i].remove();
                                        markersArray.splice(i,1);
                                        exists = false;
                                    }
                                }
                            }

                            if (!exists) {
                                var latLng = [peakval.lng, peakval.lat];
                                //add marker
                                //create an HTML element for the marker
                                var el = document.createElement('div');
                                el.className = iconstyle;

                                el.addEventListener('click', function(e) {
                                    if (isTouchDevice()) {

                                        $('#marker-tooltip').data('peak_id',peakval.id);
                                        $('#marker-tooltip').data('peak_name',peakval.name);
                                        $('#marker-tooltip').data('peak_region',climbed_peak_region);
                                        $('#marker-tooltip').data('peak_elevation',peakval.elevation);
                                        $('#marker-tooltip').data('peak_summit_count',peakval.summit_count);
                                        $('#marker-tooltip').data('peak_thumbnail_url',peakval.thumbnail_url);
                                        $('#marker-tooltip').data('peak_lat',peakval.lat);
                                        $('#marker-tooltip').data('peak_lng',peakval.lng);
                                        $('#marker-tooltip').removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                        $('#marker-tooltip').addClass(showClass);
                                        $('#marker-tooltip').data('url', marker.properties.tooltipUrl);

                                        hideMapTooltip();

                                        var bottom = $('#map-canvas').height();
                                        var right = $('#map-canvas').width();
                                        var showClass = 'scale-in-';
                                        var hideClass = 'scale-out-';

                                        var markerXOffset = 15;
                                        var markerX = this.getBoundingClientRect().x + 14 - markerXOffset;

                                        var markerYOffset = document.getElementById('map-canvas').getBoundingClientRect().y;
                                        var markerY = this.getBoundingClientRect().y + 14 - markerYOffset;

                                        if (markerY < (bottom / 2)) {
                                            marker_top = markerY;
                                            showClass = showClass + 't';
                                            hideClass = hideClass + 't';
                                        } else {
                                            marker_top = markerY - tooltip_total_height - 15;
                                            showClass = showClass + 'b';
                                            hideClass = hideClass + 'b';
                                        }

                                        if (markerX < (right / 3)) {
                                            marker_left = markerX;
                                            showClass = showClass + 'l';
                                            hideClass = hideClass + 'l';
                                        } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                            marker_left = markerX - (tooltip_total_width / 2);
                                            showClass = showClass + 'm';
                                            hideClass = hideClass + 'm';
                                        } else {
                                            marker_left = markerX - tooltip_total_width;
                                            showClass = showClass + 'r';
                                            hideClass = hideClass + 'r';
                                        }

                                        $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                            'left': marker_left,
                                            'top': marker_top,
                                            'width': tooltip_width,
                                            'height': tooltip_height
                                        }).addClass(showClass).removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                        if ($('#bt_showinmeters').hasClass('meters')) {
                                            $('#marker-tooltip').find('.peak-elevation-feet').hide();
                                            $('#marker-tooltip').find('.peak-elevation-meters').show();
                                        } else {
                                            $('#marker-tooltip').find('.peak-elevation-meters').hide();
                                            $('#marker-tooltip').find('.peak-elevation-feet').show();
                                        }
                                    } else {
                                        //console.log(peakval.slug);
                                        addPeakToLog(peakval.id, peakval.name, climbed_peak_region, peakval.elevation, peakval.summit_count, peakval.thumbnail_url, 0, 'False');
                                        $('#last-peak-added-lat').val(peakval.lat);
                                        $('#last-peak-added-lng').val(peakval.lng);
                                    }
                                    e.stopPropagation();
                                });

                                el.addEventListener('mouseover', function(e) {

                                    if (!isTouchDevice()) {

                                        $('#marker-tooltip').data('peak_id',peakval.id);
                                        $('#marker-tooltip').data('peak_name',peakval.name);
                                        $('#marker-tooltip').data('peak_region',climbed_peak_region);
                                        $('#marker-tooltip').data('peak_elevation',peakval.elevation);
                                        $('#marker-tooltip').data('peak_summit_count',peakval.summit_count);
                                        $('#marker-tooltip').data('peak_thumbnail_url',peakval.thumbnail_url);
                                        $('#marker-tooltip').data('peak_lat',peakval.lat);
                                        $('#marker-tooltip').data('peak_lng',peakval.lng);
                                        $('#marker-tooltip').data('url', marker.properties.tooltipUrl);

                                        var bottom = $('#map-canvas').height();
                                        var right = $('#map-canvas').width();
                                        var showClass = 'scale-in-';
                                        var hideClass = 'scale-out-';

                                        var markerXOffset = 15;
                                        var markerX = this.getBoundingClientRect().x + 14 - markerXOffset;

                                        var markerYOffset = document.getElementById('map-canvas').getBoundingClientRect().y;
                                        var markerY = this.getBoundingClientRect().y + 14 - markerYOffset;

                                        if (markerY < (bottom / 2)) {
                                            marker_top = markerY;
                                            showClass = showClass + 't';
                                            hideClass = hideClass + 't';
                                        } else {
                                            marker_top = markerY - tooltip_total_height - 15;
                                            showClass = showClass + 'b';
                                            hideClass = hideClass + 'b';
                                        }

                                        if (markerX < (right / 3)) {
                                            marker_left = markerX;
                                            showClass = showClass + 'l';
                                            hideClass = hideClass + 'l';
                                        } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                            marker_left = markerX - (tooltip_total_width / 2);
                                            showClass = showClass + 'm';
                                            hideClass = hideClass + 'm';
                                        } else {
                                            marker_left = markerX - tooltip_total_width;
                                            showClass = showClass + 'r';
                                            hideClass = hideClass + 'r';
                                        }

                                        $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                            'left': marker_left,
                                            'top': marker_top,
                                            'width': tooltip_width,
                                            'height': tooltip_height
                                        }).addClass(showClass).removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                        if ($('#bt_showinmeters').hasClass('meters')) {
                                            $('#marker-tooltip').find('.peak-elevation-feet').hide();
                                            $('#marker-tooltip').find('.peak-elevation-meters').show();
                                        } else {
                                            $('#marker-tooltip').find('.peak-elevation-meters').hide();
                                            $('#marker-tooltip').find('.peak-elevation-feet').show();
                                        }
                                    }
                                    e.stopPropagation();

                                });

                                el.addEventListener('mouseout', function(e) {
                                    if (isTouchDevice()) {
                                        //$('#marker-tooltip').hide();
                                    } else {
                                        hideMapTooltip();
                                    }
                                });

                                var marker = new mapboxgl.Marker(el)
                                    .setLngLat(latLng)
                                    .setOffset([-5, -10])
                                    .setDraggable(is_draggable);

                                marker.properties = {};
                                marker.properties.tooltipContent = tooltip_html;
                                marker.properties.tooltipUrl = tooltip_url;
                                marker.properties.iconstyle = iconstyle;
                                marker.properties.peakid = peakval.id;

                                markersArray.push(marker);
                                LatLngList.push(latLng);

                            }

                            counter++;
                        });

                        for (var i = markersArray.length - 1; i >= 0; --i) {
                            markersArray[i].addTo(map);
                        }

                        if (!havePeaks) {
                            //didn't have any peaks, so remove all markers
                            delete_old_markers(val);
                            //show message
                            var peakErrors = '';
                            if (keyword != '') {
                                peakErrors = 'Keyword: <strong>"'+keyword+'"</strong> ';
                            }
                            if (near != '') {
                                peakErrors = peakErrors + 'Near: <strong>"'+near+'"</strong> ';
                            }
                            //$('#message-modal-label').html('No peaks found');
                            //$('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>No matching peaks found for</p><p>' + peakErrors + '</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
                            //$('#message-modal').modal('show');
                        } else {
                            //$('#peaks-climbed-map-container').css('position','fixed');
                            //$('#peaks-climbed-map-container').css('height','100%');
                            $('#peaks-climbed-map-container').addClass('peaks-climbed-map-container-open');

                            $('#map-canvas').show();

                            $('#peaks-climbed-map-instructions').show();
                            $('#log-a-climb-title').hide();
                            {% if init_gpx_file %}
                            {% else %}
                            $('#save-changes-button-div').hide();
                            $('#cancel-x-button-div').hide();
                            $('#location-search-title').show();
                            $('#cancel-location-search-div').show();
                            {% endif %}

                            map.resize();
                            //if not a near query, zoom to fit results
                            if (!bounds) {
                                var new_bounds = new mapboxgl.LngLatBounds();
                                for (var i = 0, LtLgLen = LatLngList.length; i < LtLgLen; i++) {
                                    new_bounds.extend(LatLngList[i]);
                                }
                                map.fitBounds(new_bounds, {padding: 50, duration: 0});
                            }
                        }

                        $('#btnSearchPeakMap').html('Search');
                        $('#btnSearchPeakMap').prop('disabled', false);
                        $('#btnCancelSearchPeakMap').prop('disabled', false);
                        $('#peak-name-search').prop('disabled', false);
                        $('#peak-location-search').prop('disabled', false);
                        $('#peak-location-search-container').hide();
                        geocoding = false;
                        if (bounds) {
                            map_init = false;
                        }

                    }
                });
            });

        }

    }

}

function initialize() {

    var userLat = readCookie('map_lat');
    var userLng = readCookie('map_lng');
    var userZoom = readCookie('map_zoom');

    var mapDiv = document.getElementById('map-canvas');

    if (userLat != '' && userLat != null && userLng != '' && userLng != null) {
        var latLng = [userLng, userLat];
    } else {
        var latLng = [-122.7400, 38.0400];
    }

    if (userZoom != '' && userZoom != null) {
        var mapZoom = parseInt(userZoom);
    } else {
        var mapZoom = 12;
    }

    var LatLngList = [];

    if (isTouchDevice()) {
        map = new mapboxgl.Map({
            container: mapDiv, // HTML container id
            style: mapStyle, // style URL
            center: latLng, // starting position as [lng, lat]
            zoom: mapZoom,
            scrollZoom: false
        });
    } else {
        map = new mapboxgl.Map({
            container: mapDiv, // HTML container id
            style: mapStyle, // style URL
            center: latLng, // starting position as [lng, lat]
            zoom: mapZoom,
            scrollZoom: false
        });
        scale = new mapboxgl.ScaleControl({maxWidth: 120, unit: 'imperial'});
        map.addControl(scale, 'bottom-right');
        var nav = new mapboxgl.NavigationControl({showCompass: false});
        map.addControl(nav, 'bottom-right');
        // disable map rotation using right click + drag
        map.dragRotate.disable();
        // disable map rotation using touch rotation gesture
        map.touchZoomRotate.disableRotation();
    }

    function calculateCenter() {
      center = map.getCenter();
    }

    map.on('moveend', function () {

        if (!map_init) {
            if (init_gpx) {
                loadPeaks();
            } else {
                if (!geocoding) {
                    //get map bounds
                    var bounds = map.getBounds();
                    loadPeaksFromMapSearch(bounds);
                }
            }
            calculateCenter();
            var mapCenter = map.getCenter();
            var latitude = mapCenter.lat;
            var longitude = mapCenter.lng;
            $('#peak-lat').val(latitude);
            $('#peak-lng').val(longitude);
            map_adjusted = true;
            $('#edit_peak').prop('disabled', false);
        }
        map_init = true;
    });

    map.on('click', function(e) {
        if (isTouchDevice()) {
            hideMapTooltip();
        } else {
            hideMapTooltip();
        }
    });

    map.on('dragstart', function(e) {
        if (isTouchDevice()) {
            hideMapTooltip();
        } else {
            hideMapTooltip();
        }
    });

    map.on('load', function () {
        loadPeaks();
        calculateCenter();
        var mapUnits = readCookie('map_units');
        if (mapUnits != '') {
            toggleMapUnits(mapUnits);
        }
        setMapControls();

        {% if init_gpx_file %}
        //add gpx
        var gpxJSON = {};
        gpxJSON.success = 'true';
        gpxJSON.valid_file = 'true';
        gpxJSON.gpx_file = '{{ MEDIA_URL }}{{ init_gpx_file }}';
        gpxJSON.gpx_filename = '{{ init_gpx_file }}';
        gpxJSON.start_index = '0';
        gpxJSON.end_index = '500';
        set_gpx(gpxJSON);
        {% endif %}

    });

    map.on('click', function () {
        map.scrollZoom.enable();
    });

    var checking_style_status = false;
    map.on('styledata', function (e) {
        if (checking_style_status) {
            // If already checking style status, bail out
            // (important because styledata event may fire multiple times)
            return;
        } else {
            checking_style_status = true;
            check_style_status();
        }
    });

    function check_style_status() {
        if (map.isStyleLoaded()) {
            checking_style_status = false;
            map.fire('map_style_finally_loaded');
        } else {
            // If not yet loaded, repeat check after delay:
            setTimeout(function() {check_style_status();}, 200);
            return;
        }
    }

    map.on('map_style_finally_loaded', function (e) {
        addPolyline();
    });

}

function setMapControls() {
    var check = checkIfMapboxStyleIsLoaded();
    if (!check) {
        // It's not safe to manipulate layers yet, so wait 200ms and then check again
        setTimeout(function() {
            setMapControls();
        }, 200);
        return;
    }
    // Whew, now it's safe to manipulate layers!
    var mapUnits = readCookie('map_units');
    if (mapUnits == 'meters') {
        toggleMapUnits(mapUnits);
    }
}

function addExtraMapLayers() {
    var check = checkIfMapboxStyleIsLoaded();
    if (!check) {
        // It's not safe to manipulate layers yet, so wait 200ms and then check again
        setTimeout(function() {
            addExtraMapLayers();
        }, 200);
        return;
    }
    // Whew, now it's safe to manipulate layers!
    if (linePoints.length > 0) {
        addPolyline();
    }
}

function addPolyline() {
    if (linePoints.length > 0) {
        if (map.getSource('route-data') == undefined) {
            map.addSource('route-data', {
                type: 'geojson',
                data: lineData
            });
        }
        if (map.getLayer('route-layer') == undefined) {
            map.addLayer({
                "id": "route-layer",
                "type": "line",
                "source": "route-data",
                "layout": {
                    "line-join": "round",
                    "line-cap": "round"
                },
                "paint": {
                    "line-color": "#fc202e",
                    "line-width": 4
                }
            });
        }
    }
}

var uploaderIdle = true;
var formSubmitted = false;

function isFutureDate(text){
    var arrDate = text.split("-");
    var date = new Date(arrDate[0], arrDate[1]-1, arrDate[2]);
    var _now=new Date();
    if(date.getTime()>_now.getTime()){
        $("span.invalid-date").show();
        return true;
    }
    $("span.invalid-date").hide();
    return false;
}

function loadSummitCompanion(data, value) {
    var companiondata = data.split("|");
    var username = companiondata[0];
    var avatar_url = companiondata[1];
    var user_id = companiondata[2];
    if (user_id != '0') {
        $("#summit-companions").append('<div class="summit-companion" id="companion-' + user_id + '" style="display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; background: #fff; box-shadow: rgba(0, 0, 0, .2) 0px 2px 5px;"><p><span style="display: inline;"><img src="{{ MEDIA_URL }}' + avatar_url + '" style="width: 100px; height: 100px; border-top-left-radius: 4px; border-bottom-left-radius: 4px;"></span><span style="display: inline; margin-left: 20px; font-weight: 500;">' + username + '</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeCompanion(\'' + user_id + '\');"><i class="fa fa-times"></i></a></span></p></div>');
        var current_selected_users = $("#fellow_selected_users").val();
        $("#fellow_selected_users").val(current_selected_users+'|'+user_id+'|,');
    } else {
        var user_slug = slugify(username);
        if ($('#companion-' + user_slug).length == 0) {
            $("#summit-companions").append('<div class="summit-companion" id="companion-' + user_slug + '" style="display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; background: #fff; box-shadow: rgba(0, 0, 0, .2) 0px 2px 5px;"><p><span style="display: inline;"><img src="{{ MEDIA_URL }}' + avatar_url + '" style="width: 100px; height: 100px; border-top-left-radius: 4px; border-bottom-left-radius: 4px;"></span><span style="display: inline; margin-left: 20px; font-weight: 500;">' + username + '</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeCompanion(\'' + user_slug + '\');"><i class="fa fa-times"></i></a></span></p></div>');
            var current_nonmember_baggers = $("#fellow_nonmember_baggers").val();
            $("#fellow_nonmember_baggers").val(current_nonmember_baggers + '|' + username + '|,');
        }
    }
    $("#summitmemq1").val('');
    $("#summitmemq1").focus();
}

function removeCompanion(user_id) {
    if (!isNaN(user_id)) {
        var companion_div = '#companion-' + user_id;
        $(companion_div).remove();
        var current_selected_users = $("#fellow_selected_users").val();
        var selected_users = current_selected_users.replace('|' + user_id + '|,', '');
        $("#fellow_selected_users").val(selected_users);
    } else {
        var companion_div = '#companion-' + user_id;
        $(companion_div).remove();
        var current_nonmember_baggers = $("#fellow_nonmember_baggers").val();
        var nonmember_baggers = current_nonmember_baggers.replace('|' + user_id + '|,', '');
        $("#fellow_nonmember_baggers").val(nonmember_baggers);
    }
}

function slugify(str) {
    str = str.replace(/^\s+|\s+$/g, ''); // trim
    str = str.toLowerCase();

    // remove accents, swap ñ for n, etc
    var from = "àáäâèéëêìíïîòóöôùúüûñç·/_,:;";
    var to   = "aaaaeeeeiiiioooouuuunc------";
    for (var i=0, l=from.length ; i<l ; i++) {
    str = str.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
    }

    str = str.replace(/[^a-z0-9 -]/g, '') // remove invalid chars
    .replace(/\s+/g, '-') // collapse whitespace and replace by -
    .replace(/-+/g, '-'); // collapse dashes

    return str;
}

function processURL(url){
    var numVideos = $(".related-videos").length;
    var id;
    var video_url;

    if (url.indexOf('youtube.com') > -1) {
        <!-- CHANGED -->
        id = url.split('v=')[1].split('&')[0];
        video_thumb_url = 'http://i2.ytimg.com/vi/' + id + '/hqdefault.jpg';
        $("#summit-videos").append('<div class="related-videos" id="video-' + numVideos + '" style="width: 98%; background-color: #fff; display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px;"><p><span style="display: inline;"><img src="' + video_thumb_url + '" style="width: 150px; height: 100px;"></span><span id="video-url-' + numVideos + '" style="display: inline; margin-left: 20px; font-weight: 500;">' + url + '</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeVideo(\'' + numVideos + '\');"><i class="fa fa-times"></i></a></span></p></div>');
        var current_videos = $("#summit_video_links").val();
        $("#summit_video_links").val(current_videos + '|' + url + '|,');
        $("#summit-video-link").val('');
    } else if (url.indexOf('youtu.be') > -1) {
        id = url.split('/')[3];
        video_thumb_url = 'http://i2.ytimg.com/vi/' + id + '/hqdefault.jpg';
        $("#summit-videos").append('<div class="related-videos" id="video-' + numVideos + '" style="width: 98%; background-color: #fff; display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px;"><p><span style="display: inline;"><img src="' + video_thumb_url + '" style="width: 150px; height: 100px;"></span><span id="video-url-' + numVideos + '" style="display: inline; margin-left: 20px; font-weight: 500;">' + url + '</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeVideo(\'' + numVideos + '\');"><i class="fa fa-times"></i></a></span></p></div>');
        var current_videos = $("#summit_video_links").val();
        $("#summit_video_links").val(current_videos + '|' + url + '|,');
        $("#summit-video-link").val('');
    } else if (url.indexOf('vimeo') > -1) {
        id = url.substr(url.lastIndexOf('/') + 1);
        $.getJSON('https://www.vimeo.com/api/v2/video/' + id + '.json?callback=?', {format: "json"}, function(data) {
            video_thumb_url = data[0].thumbnail_large;
            $("#summit-videos").append('<div class="related-videos" id="video-' + numVideos + '" style="width: 98%; background-color: #fff; display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px;"><p><span style="display: inline;"><img src="' + video_thumb_url + '" style="width: 150px; height: 100px;"></span><span id="video-url-' + numVideos + '" style="display: inline; margin-left: 20px; font-weight: 500;">' + url + '</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeVideo(\'' + numVideos + '\');"><i class="fa fa-times"></i></a></span></p></div>');
            var current_videos = $("#summit_video_links").val();
            $("#summit_video_links").val(current_videos + '|' + url + '|,');
            $("#summit-video-link").val('');
        });
    } else {
        $('#message-modal-label').html('Invalid URL');
        $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>Only YouTube or Vimeo supported</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
        $('#message-modal').modal('show');
    }
}

function removeVideo(video_id) {
    var video_div = '#video-'+video_id;
    var video_url_div = '#video-url-'+video_id;
    var video_url = $(video_url_div).html();
    $(video_div).remove();
    var current_videos = $("#summit_video_links").val();
    var selected_videos = current_videos.replace('|'+video_url+'|,', '');
    $("#summit_video_links").val(selected_videos);
}

function removeRelatedLink(link_id) {
    var link_div = '#related-link-'+link_id;
    var link_url_div = '#related-link-url-'+link_id;
    var link_url = $(link_url_div).html();
    $(link_div).remove();
    var current_links = $("#summit_related_links").val();
    var selected_links = current_links.replace('|'+link_url+'|,', '');
    $("#summit_related_links").val(selected_links);
}

function validateForm() {
    //form validation
    //validate any URLs
    var validVideoLink = true;
    var validRelatedLink = true;
    var isYoutubeOrVimeo = true;
    if ($('#summit-video-link').val() != '') {
        var videoUrl = $('#summit-video-link').val();
        var validVideoLink = isUrlValid(videoUrl);
        var isYoutubeOrVimeo = false;
        if (videoUrl.indexOf('youtube.com') > -1 || videoUrl.indexOf('youtu.be') > -1 || videoUrl.indexOf('vimeo') > -1) {
            isYoutubeOrVimeo = true;
        }
    }
    if ($('#summit-related-link').val() != '') {
        var validRelatedLink = isUrlValid($('#summit-related-link').val());
    }
    if (validVideoLink && validRelatedLink && isYoutubeOrVimeo) {
        //any text left un-added in input fields?
        if ($('#txtAddActivityTag').val() != '' || $('#txtAddChallengeTag').val() != '' || $('#txtAddGearTag').val() != '' || $('#summitmemq1').val() != '' || $('#summit-video-link').val() != '' || $('#summit-related-link').val() != '') {
            var unsavedText = '';
            if ($('#txtAddActivityTag').val() != '') {
                unsavedText = unsavedText + '<p>Trip activity: <b>' + $('#txtAddActivityTag').val() + '</b></p>';
            }
            if ($('#txtAddChallengeTag').val() != '') {
                unsavedText = unsavedText + '<p>Obstacle: <b>' + $('#txtAddChallengeTag').val() + '</b></p>';
            }
            if ($('#txtAddGearTag').val() != '') {
                unsavedText = unsavedText + '<p>Key gear: <b>' + $('#txtAddGearTag').val() + '</b></p>';
            }
            if ($('#summitmemq1').val() != '') {
                unsavedText = unsavedText + '<p>Companion: <b>' + $('#summitmemq1').val() + '</b></p>';
            }
            if ($('#summit-video-link').val() != '') {
                unsavedText = unsavedText + '<p>Video: <b>' + $('#summit-video-link').val() + '</b></p>';
            }
            if ($('#summit-related-link').val() != '') {
                unsavedText = unsavedText + '<p>Related link: <b>' + $('#summit-related-link').val() + '</b></p>';
            }
            $('#savetext-modal-label').html('Save your changes?');
            $('#savetext-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>You entered the following info but didn’t click the "Add" button next to it. Save this too?</p><p>' + unsavedText + '</p><p style="text-align: center;"><a onclick="saveUnaddedText();" class="btn btn-primary" style="width: 100px;">Save</a><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></p></div>');
            $('#savetext-modal').modal('show');
        } else {
            formSubmitted = true;
            $('#add_peak2').prop('disabled', true);
            $('#add_peak2').html('<i class="fa fa-spinner fa-spin"></i>');
            $('#darkness').append('<div style="height: 100%; width: 100%; color: #fff; display: flex; justify-content: center; align-items: center;"><span style="width: 300px; text-align: center;">Saving your log<br /><br /><div style="text-align: center;"><i style="font-size: 22px;" class="fa fa-spinner fa-2x fa-spin" aria-hidden="true"></i></div></span></div>');
	        $('#darkness').show();
	        window.scrollTo(0,0);
            if (uploaderIdle) {
                saveLog();
            }
        }
    } else if (!isYoutubeOrVimeo) {
        var urlErrors = '<p>Your videos: <b>' + $('#summit-video-link').val() + '</b></p>';
        $('#message-modal-label').html('Invalid URL');
        $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>Only YouTube or Vimeo supported</p><p>' + urlErrors + '</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
        $('#message-modal').modal('show');
    } else {
        var urlErrors = '';
        if (!validVideoLink) {
            urlErrors = urlErrors + '<p>Your videos: <b>' + $('#summit-video-link').val() + '</b></p>';
        }
        if (!validRelatedLink) {
            urlErrors = urlErrors + '<p>Related links: <b>' + $('#summit-related-link').val() + '</b></p>';
        }
        $('#message-modal-label').html('Invalid URL');
        $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>You entered the following invalid URL(s)</p><p>' + urlErrors + '</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
        $('#message-modal').modal('show');
    }
}

function isUrlValid(url) {
    return /^(https?|s?ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i.test(url);
}

function saveLog() {
    //submit form
    $('#summitlog_form').submit();
}

function getPeakRoutes(peak_id) {

    var haveRoutes = false;
    $.getJSON('{% url "peak_routes_list" %}?peak_id='+peak_id, function(data) {
        $.each(data, function (key, val) {
            if (key == 'routes') {
                $("#route_up").empty();
                $("#route_up").append($("<option></option>").val("0").text("select route"));
                $("#route_up").append($("<optgroup></optgroup>").attr('label','===========================').prop('disabled', true));
                $("#route_up").append($("<option></option>").val("add_new_route").text("Or add a new route..."));
                $.each(val, function (routekey, routeval) {
                    haveRoutes = true;
                    var encoded = routeval.name + ' &bull; ' + routeval.count + ' summits';
                    var decoded = $("<div/>").html(encoded).text();
                    $("#route_up option").eq(0).after($("<option></option>").val(routeval.route_id).text(decoded));
                });
            }
        });
        if (haveRoutes) {
            $('#summit-route-name').hide();
            $('#route_up_container').show();
        } else {
            $('#route_up_container').hide();
            $('#summit-route-name').show();
        }
    });
}

function addPeakToLog(peak_id, peak_name, peak_region, peak_elevation, peak_summitlog_count, peak_thumbnail_url, summit_log_id, attempt) {
    if (peak_id !== undefined) {
        var summitChecked = '';
        var attemptChecked = '';
        if (attempt == 'False') {
            summitChecked = 'checked';
        } else {
            attemptChecked = 'checked';
        }
        var peakHtml = '<div id="peak-climbed-' + peak_id + '" class="peak-climbed-container">';
        peakHtml = peakHtml + '<div class="peak-climbed-div" style="float: left; background-image: url(\'{{ MEDIA_URL }}' + peak_thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}"></div></div>';
        peakHtml = peakHtml + '<div class="peak-climbed-wrapper">';
        peakHtml = peakHtml + '<div class="peak-climbed-peak-name ellipsis">' + peak_name + '</div><div class="peak-climbed-peak-stats ellipsis">' + peak_region + '<br />' + numberWithCommas(Math.floor(peak_elevation)) + ' ft / ' + numberWithCommas(Math.floor(peak_elevation * .3048)) + ' m&nbsp;&bull;&nbsp;' + peak_summitlog_count + ' climb' + ((peak_summitlog_count != 1) ? 's' : '') + '</div>';
        peakHtml = peakHtml + '<div class="peak-climbed-outcome"><div class="btn-group" data-toggle="buttons"><label class="btn active peak-climbed-outcome-label" style="padding-right: 10px;"><input type="radio" name="rdoOutcome' + peak_id + '" id="rdoSuccess' + peak_id + '" value="0" ' + summitChecked + '><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span class="peak-climbed-outcome-span"> summit</span></label><label class="btn peak-climbed-outcome-label"><input type="radio" name="rdoOutcome' + peak_id + '" id="rdoAttempt' + peak_id + '" value="1" ' + attemptChecked + '><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span class="peak-climbed-outcome-span"> attempt</span></label></div></div>';
        peakHtml = peakHtml + '</div>';
        peakHtml = peakHtml + '<div class="peak-climbed-icon-div"><a class="peak-climbed-icon searchbox-icon" href="javascript:removeClimbedPeak(' + peak_id + ');"><i class="fa fa-times-circle" style="opacity: .5;"></i></a></div>';
        peakHtml = peakHtml + '</div>';
        $('#find-on-map-link').hide();
        $('#peak-search-container').hide();

        {% if init_gpx_file %}
        {% else %}
        $('#location-search-title').hide();
        $('#cancel-location-search-div').hide();
        $('#log-a-climb-title').show();
        $('#save-changes-button-div').show();
        $('#cancel-x-button-div').show();
        {% endif %}

        {% if not init_gpx_file %}
        $('#peaks-climbed-map-container').hide();
        {% endif %}
        hideMapTooltip();
        {% if not init_gpx_file %}
        $('#peaks-climbed-search-container').show();
        {% endif %}

        $('#add-another-peak').show();
        //add value to hidden field
        var aryPeakIds = [];
        var currentPeakIds = [];
        var temp = $('#climbed_peak_ids').val();
        if (temp != '') {
            currentPeakIds = temp.split(',');
        }
        var peakExists = false;
        for (var i = 0; i < currentPeakIds.length; i++) {
            aryPeakIds.push(currentPeakIds[i]);
            if (currentPeakIds[i] == peak_id) {
                peakExists = true;
            }
        }
        if (!peakExists) {
            $('#peaks-climbed-list').show();
            $('#peaks-climbed-list').append(peakHtml);
            aryPeakIds.push(peak_id);
            $('#climbed_peak_ids').val(aryPeakIds);
            //if only peak chosen, fill routes dropdown
            if (aryPeakIds.length == 1) {
                getPeakRoutes(aryPeakIds[0]);
                $('#summit-route-container').removeClass('summitlog-route-hidden');
            } else {
                if (aryPeakIds.length > 1) {
                    $('.peaksYouClimbed .field-title').html('Peaks you climbed on ' + $.format.date($("#summit-date").val() + 'T00:00:00Z', 'MMM d, yyyy'));
                }
                $('#summit-route-container').addClass('summitlog-route-hidden');
            }
            if (uploaderIdle) {
                $('#add_peak2').prop('disabled', false);
            }
        }
    }
}

function removeClimbedPeak(peak_id) {
    //remove element
    var peakDiv = '#peak-climbed-' + peak_id;
    $(peakDiv).remove();
    //remove value from hidden field
    var aryPeakIds = [];
    var currentPeakIds = [];
    var temp = $('#climbed_peak_ids').val();
    if (temp != '') {
        currentPeakIds = temp.split(',');
    }
    for (var i = 0; i < currentPeakIds.length; i++) {
        if (currentPeakIds[i] != peak_id) {
            aryPeakIds.push(currentPeakIds[i]);
        }
    }
    $('#climbed_peak_ids').val(aryPeakIds);
    //if no peaks left, show search box again
    if (aryPeakIds.length == 0) {
        $('#peaks-climbed-list').hide();
        $('#add-another-peak').hide();
        $('#peak-search').val('');
        $('#peak-search-container').show();
        $('#find-on-map-link').show();
        //disable save button
        $('#add_peak2').prop('disabled', true);
    } else {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    }
    //if only peak chosen, fill routes dropdown
    if (aryPeakIds.length == 1) {
        getPeakRoutes(aryPeakIds[0]);
        $('#summit-route-container').removeClass('summitlog-route-hidden');
        $('.peaksYouClimbed .field-title').html('Peak you climbed on ' + $.format.date($("#summit-date").val() + 'T00:00:00Z', 'MMM d, yyyy'));
    } else {
        $('#summit-route-container').addClass('summitlog-route-hidden');
    }
}

function getParameterByName(name, url) {
    if (!url) url = window.location.href;
    name = name.replace(/[\[\]]/g, '\\$&');
    var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
        results = regex.exec(url);
    if (!results) return null;
    if (!results[2]) return '';
    return decodeURIComponent(results[2].replace(/\+/g, ' '));
}

$(document).ready(function(){

    $('#default-route-new-name').on('input', function() {
        if ($('#default-route-new-name').val() != '') {
            $('#save-new-route-name').removeAttr('disabled');
        } else {
            $('#save-new-route-name').attr('disabled','disabled');
        }
    });

    //pre-init matched peaks?
    var summit_ids = getParameterByName('summits');
    if (summit_ids) {
        $('#summit_ids').val(summit_ids);
        //hide date container
        $('#summit-date-container').remove();
        $('#summit-date').val('');
        $('#peaks-you-climbed-container').css('margin-top',0);
        $.getJSON('{% url "climbed_peaks_list_init" %}?summits='+summit_ids, function(data) {
            $.each(data, function (key, val) {
                if (key == 'suggestions') {
                    $.each(val, function (peakkey, peakval) {
                        //set date and gpx filefor first climb
                        if ($('#summit-date').val() == '') {
                            $('#summit-gpx-file').val(peakval.data.gpx_file);
                            $('#summit-date').val(peakval.data.summit_date);
                            //set peaks you climbed title
                            $('.peaksYouClimbed .field-title').html('Peak you climbed on ' + $.format.date($("#summit-date").val() + 'T00:00:00Z', 'MMM d, yyyy'));
                        }

                        if (peakval.data.id > 0) {
                            //build country string
                            var country = '';
                            $.each( peakval.data.country, function( countrykey, countryval ) {
                                country = country + countryval.country_name + ' / ';
                            });
                            country = country.substr(0, country.length-3);
                            if (country == '') {
                                country = 'Unknown Country';
                            }

                            //build region string
                            var region = '';
                            $.each( peakval.data.region, function( regionkey, regionval ) {
                                region = region + regionval.region_name + ', ' + regionval.country_name + ' / ';
                            });
                            region = region.substr(0, region.length-3);
                            if (region == '') {
                                region = country;
                            }
                            addPeakToLog(peakval.data.id, peakval.data.name, region, peakval.data.elevation, peakval.data.summitlog_count, peakval.data.peak_thumbnail_url, peakval.data.summit_log_id, peakval.data.attempt);
                        }

                    });
                }
            });
        });
    }

    var window_width = $(window).width();
    if (window_width < 768) {
        $('#map-canvas').height(350);
    } else {
        $('#map-canvas').height(480);
    }

    $('#map-canvas').mousemove(function(e) {
        var offset = $(this).offset();
        pageX = e.pageX;
        pageY = e.pageY;
        mapX = (e.pageX - offset.left);
        mapY = (e.pageY - offset.top);
    });

    $('#map-canvas').on('touchstart', function(e) {
        var offset = $(this).offset();
        pageX = e.originalEvent.touches[0].pageX;
        pageY = e.originalEvent.touches[0].pageY;
        mapX = (pageX - offset.left);
        mapY = (pageY - offset.top);
    });

    $(window).resize(function() {
        var window_width = $(window).width();
        if (window_width < 768) {
            $('#map-canvas').height(350);
        } else {
            $('#map-canvas').height(480);
        }
        map.resize();
    });

    initialize();

    //switch map units
    $("#gm-custom-mapunits").click(function(){
        if ($('#gm-custom-mapunitsbutton-label-meters').hasClass('gm-custom-mapunits-selected')) {
            toggleMapUnits('feet');
            scale.setUnit('imperial');
        } else {
            toggleMapUnits('meters');
            scale.setUnit('metric');
        }
    });

    //Disable scrollZoom
    $('#map-canvas').on('mouseleave', function() {
        map.scrollZoom.disable();
    });

    $('#marker-tooltip').on('click', function(){
        addPeakToLog($(this).data('peak_id'), $(this).data('peak_name'), $(this).data('peak_region'), $(this).data('peak_elevation'), $(this).data('peak_summit_count'), $(this).data('peak_thumbnail_url'), 0, 'False');
        $('#last-peak-added-lat').val($(this).data('peak_lat'));
        $('#last-peak-added-lng').val($(this).data('peak_lng'));
        {% if not init_gpx_file %}
        $('#peak-name-search').val('');
        $('#peak-location-search').val('');
        map.setCenter([$(this).data('peak_lng'), $(this).data('peak_lat')]);
        map.setZoom(13);
        {% endif %}
    });

    //Custom Google Map type stuff

    $('#gm-custom-mapbutton').on('mouseenter', function(){
       $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapbutton').on('mouseleave', function(){
       $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapbutton').on('touchstart', function() {
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-satstreets').on('click', function() {
        toggleMapType('satellite');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-satstreets').on('touchstart', function() {
        toggleMapType('satellite');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-satstreets').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-satstreets').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapoption-topo').on('click', function() {
        toggleMapType('caltopo');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-topo').on('touchstart', function() {
        toggleMapType('caltopo');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-topo').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-topo').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapoption-sat').on('click', function() {
        toggleMapType('sat_topo');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-sat').on('touchstart', function() {
        toggleMapType('sat_topo');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-sat').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-sat').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapoption-terrain').on('click', function() {
        toggleMapType('terrain');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-terrain').on('touchstart', function() {
        toggleMapType('terrain');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-terrain').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-terrain').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapoption-outdoors').on('click', function() {
        toggleMapType('outdoors');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-outdoors').on('touchstart', function() {
        toggleMapType('outdoors');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-outdoors').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-outdoors').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    //Natural atlas stuff
    $('#gm-custom-mapoption-natatl').on('click', function() {
        toggleMapType('natural_atlas');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-natatl').on('touchstart', function() {
        toggleMapType('natural_atlas');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-natatl').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-natatl').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    $('#btnSearchPeakMap').on('click', function() {
        if ($('#peak-location-search').val() != '') {
            searched_by_map = true;
            delete_all_markers();
            geoCode();
        } else if ($('#peak-name-search').val() != '') {
            searched_by_map = true;
            delete_all_markers();
            loadPeaksFromMapSearch(null);
        }
    });

    $('#btnCancelSearchPeakMap').on('click', function() {
        $('#peaks-climbed-map-container').hide();
        $('#peaks-climbed-search-container').show();
    });

    $('#peak-location-search').bind("enterKey",function(e){
        if ($('#peak-location-search').val() != '') {
            searched_by_map = true;
            delete_all_markers();
            geoCode();
        } else if ($('#peak-name-search').val() != '') {
            searched_by_map = true;
            delete_all_markers();
            loadPeaksFromMapSearch(null);
        }
    });

    $('#peak-name-search').bind("enterKey",function(e){
        if ($('#peak-location-search').val() != '') {
            searched_by_map = true;
            delete_all_markers();
            geoCode();
        } else if ($('#peak-name-search').val() != '') {
            searched_by_map = true;
            delete_all_markers();
            loadPeaksFromMapSearch(null);
        }
    });

    $(document).on("keydown", ":input:not(textarea)", function(event) {
        if (event.keyCode == 13) {
            return false;
        } else {
            return event.keyCode;
        }
    });

    $('#peak-location-search').keyup(function(e){
        if(e.keyCode == 13)
        {
            $(this).trigger("enterKey");
        }
    });

    $('#peak-name-search').keyup(function(e){
        if(e.keyCode == 13)
        {
            $(this).trigger("enterKey");
        }
    });

    $('#find-on-map-link').on('click', function (e) {
        //center map on last peak added if necessary
        if ($('#last-peak-added-lat').val() != '' && $('#last-peak-added-lng').val() != '') {
            if ($('#peak-search').val() != '') {
                $('#peak-name-search').val($('#peak-search').val());
            }
            $('#peaks-climbed-search-container').hide();
            $('.search-by-peak-name-title').hide();
            var lat = $('#last-peak-added-lat').val();
            var lng = $('#last-peak-added-lng').val();
            map.setCenter([lng, lat]);
            map.setZoom(13);
            $('#peaks-climbed-map-container').show();
        } else {
            if ($('#peak-search').val() != '') {
                $('#peak-name-search').val($('#peak-search').val());
            }
            $('#peaks-climbed-search-container').hide();
            $('.search-by-peak-name-title').hide();

            $('#peaks-climbed-map-instructions').hide();
            {% if init_gpx_file %}
            {% else %}
            $('#location-search-title').hide();
            $('#cancel-location-search-div').hide();
            $('#log-a-climb-title').show();
            $('#save-changes-button-div').show();
            $('#cancel-x-button-div').show();
            {% endif %}

            $('#map-canvas').hide();
            $('#peaks-climbed-map-container').show();
            $('#peak-location-search-container').show();
        }
    });

    $('#search-by-peak-name-link').on('click', function (e) {
        $('#peaks-climbed-map-container').hide();
        $('#peak-search').val('');
        $('#peaks-climbed-search-container').show();
    });

    $('#add-more-info-link').on('click', function (e) {
        $('#add-more-info-container').hide();
        $('.summitlog-extras').removeClass('summitlog-section-hidden');
    });

    $('#peak-search-icon').on('click', function (e) {
        $('#peak-search').val('');
    });

    $('#member-search-icon').on('click', function (e) {
        $('#summitmemq1').val('');
    });

    $('#add-another-peak-link').on('click', function (e) {
        if (!searched_by_map) {
            if ($('#last-peak-added-lat').val() != '' && $('#last-peak-added-lng').val() != '') {
                map_init = false;
                init_gpx = true;
                $('#peaks-climbed-search-container').hide();
                $('.search-by-peak-name-title').hide();
                var lat = $('#last-peak-added-lat').val();
                var lng = $('#last-peak-added-lng').val();
                map.setCenter([lng, lat]);
                map.setZoom(13);
                $('#peaks-climbed-map-container').addClass('peaks-climbed-map-container-open');
                $('#peaks-climbed-map-container').show();
                searched_by_map = true;
                //delete_all_markers();
                //loadPeaksFromMapSearch(null);
                $('#peak-location-search-container').hide();
                $('#peaks-climbed-search-container').hide();
                $('#peaks-climbed-map-container').show();
                $('#map-canvas').show();

                $('#peaks-climbed-map-instructions').show();

                {% if init_gpx_file %}
                {% else %}
                $('#log-a-climb-title').hide();
                $('#save-changes-button-div').hide();
                $('#cancel-x-button-div').hide();
                $('#location-search-title').show();
                $('#cancel-location-search-div').show();
                {% endif %}

                map.resize();
                var bounds = map.getBounds();
                loadPeaksFromMapSearch(bounds);
            } else {
                $('#add-another-peak').hide();
                $('#peak-search').val('');
                $('#peak-search-container').show();
                $('#find-on-map-link').show();
            }
        } else {
            if ($('#last-peak-added-lat').val() != '' && $('#last-peak-added-lng').val() != '') {
                map_init = false;
                init_gpx = true;
                $('#peaks-climbed-search-container').hide();
                $('.search-by-peak-name-title').hide();
                var lat = $('#last-peak-added-lat').val();
                var lng = $('#last-peak-added-lng').val();
                map.setCenter([lng, lat]);
                map.setZoom(13);
                $('#peaks-climbed-map-container').show();
                searched_by_map = true;
                //delete_all_markers();
                //loadPeaksFromMapSearch(null);
                $('#peak-location-search-container').hide();
                $('#peaks-climbed-search-container').hide();
                $('#peaks-climbed-map-container').show();
                $('#map-canvas').show();

                $('#peaks-climbed-map-instructions').show();

                {% if init_gpx_file %}
                {% else %}
                $('#log-a-climb-title').hide();
                $('#save-changes-button-div').hide();
                $('#cancel-x-button-div').hide();
                $('#location-search-title').show();
                $('#cancel-location-search-div').show();
                {% endif %}

                map.resize();
                var bounds = map.getBounds();
                loadPeaksFromMapSearch(bounds);
            } else {
                $('#add-another-peak').hide();
                $('#peaks-climbed-map-container').show();
                map_init = false;
                map.resize();
            }
        }
    });

    $('#peak-search').autocomplete({
        serviceUrl: '/api/peaks/climb_suggestions/',
        minChars: 3,
        triggerSelectOnValidInput: false,
        formatResult: function(suggestion, currentValue){
            var suggestionHtml = '';
            //build country string
            var country = '';
            $.each( suggestion.data.country, function( countrykey, countryval ) {
                country = country + countryval.country_name + ' / ';
            });
            country = country.substr(0, country.length-3);
            if (country == '') {
                country = 'Unknown Country';
            }

            //build region string
            var region = '';
            $.each( suggestion.data.region, function( regionkey, regionval ) {
                region = region + regionval.region_name + ', ' + regionval.country_name + ' / ';
            });
            region = region.substr(0, region.length-3);
            if (region == '') {
                region = country;
            }
            suggestionHtml = '<div>';
            suggestionHtml = suggestionHtml + '<div class="suggestion-peak-photo" style="position: absolute; left: 0px; background-image: url(\'{{ MEDIA_URL }}' + suggestion.data.peak_thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}"></div></div>';
            suggestionHtml = suggestionHtml + '<div class="suggestion-peak-name ellipsis" style="width: 100%;">' + suggestion.value + '</div><div class="suggestion-peak-stats ellipsis">' + region + '<br />' + numberWithCommas(Math.floor(suggestion.data.elevation)) + ' ft / ' + numberWithCommas(Math.floor(suggestion.data.elevation * .3048)) + ' m&nbsp;&bull;&nbsp;' + suggestion.data.summitlog_count + ' climb' + ((suggestion.data.summitlog_count != 1) ? 's' : '') + '</div>';
            suggestionHtml = suggestionHtml + '</div>';
            return suggestionHtml;
        },
        onSelect: function (suggestion) {
            //alert('You selected: ' + suggestion.data.id + ': ' + suggestion.data.name);
            //build country string
            var country = '';
            $.each( suggestion.data.country, function( countrykey, countryval ) {
                country = country + countryval.country_name + ' / ';
            });
            country = country.substr(0, country.length-3);
            if (country == '') {
                country = 'Unknown Country';
            }

            //build region string
            var region = '';
            $.each( suggestion.data.region, function( regionkey, regionval ) {
                region = region + regionval.region_name + ', ' + regionval.country_name + ' / ';
            });
            region = region.substr(0, region.length-3);
            if (region == '') {
                region = country;
            }
            addPeakToLog(suggestion.data.id, suggestion.data.name, region, suggestion.data.elevation, suggestion.data.summitlog_count, suggestion.data.peak_thumbnail_url, 0, 'False');
            $('#last-peak-added-lat').val(suggestion.data.lat);
            $('#last-peak-added-lng').val(suggestion.data.lng);
        },
        onSearchStart: function (query) {
            $('#peak-search-icon').html('<i class="fa fa-spinner fa-spin"></i>');
        },
        onSearchComplete: function (query, suggestions) {
            $('#peak-search-icon').html('<i class="fa fa-times"></i>');
        },
        onHide: function (container) {
            $('#peak-search').val('');
        }
    });

    //member searchbox on summitlog page
    $('#summitmemq1').autocomplete({
        serviceUrl: '/api/companions/suggestions',
        minChars: 3,
        triggerSelectOnValidInput: false,
        formatResult: function(suggestion, currentValue){
            var companiondata = suggestion.data.split("|");
            var username = companiondata[0];
            var avatar_url = companiondata[1];
            var user_id = companiondata[2];
            var suggestionHtml = '';
            suggestionHtml = '<div class="suggestion-companion-div">';
            suggestionHtml = suggestionHtml + '<div class="suggestion-companion-avatar" style="background-image: url(\'{{ MEDIA_URL }}' + avatar_url + '\');"><div><img src="{% static 'img/spacer.png' %}"></div></div>';
            suggestionHtml = suggestionHtml + '<div class="suggestion-companion-name ellipsis">' + suggestion.value + '</div>';
            suggestionHtml = suggestionHtml + '</div>';
            return suggestionHtml;
        },
        onSelect: function (suggestion) {
            loadSummitCompanion(suggestion.data, suggestion.value);
        },
        onSearchStart: function (query) {
            $('#member-search-icon-div').show();
            $('#member-search-icon').html('<i class="fa fa-spinner fa-spin"></i>');
        },
        onSearchComplete: function (query, suggestions) {
            //window.scrollTo(0,document.body.scrollHeight);
            $('#member-search-icon').html('<i class="fa fa-times"></i>');
        },
        onHide: function (container) {
            $('#summitmemq1').val('');
            $('#member-search-icon-div').hide();
        }
    });

    $('#summitmemq1').on('focus', function() {
        $('#summitmemq1').val('');
    });

    $('#summitmemq1').on('change', function() {
        if ($('#summitmemq1').val() != '') {
            $('#member-search-icon-div').show();
        } else {
            $('#member-search-icon-div').hide();
        }
    });

    var maxYear = new Date().getFullYear();
    var minYear = 1950;
    var currMonth = new Date().getMonth();

    for (var i = minYear; i<=maxYear; i++){
        $('.ui-datepicker-year').append('<option value="'+i+'">'+i+'</option>');
    }
    $('.ui-datepicker-year').val(maxYear);
    $('.ui-datepicker-month').val(currMonth);

    //when click the submit button
    $('#add_peak2').on('click', function(e) {
        e.preventDefault();
        //validate form
        validateForm();
    });

    $('.toggle-switch').on('mouseenter', 'input', function() {
        var currentBackground = $(this).siblings('label').css('background-color');
        if (currentBackground == 'rgb(234, 234, 234)') {
            $(this).siblings('label').css('background-color','#ccc');
        } else if (currentBackground == 'rgb(238, 238, 238)') {
            $(this).siblings('label').css('background-color','#ccc');
        } else if (currentBackground == 'rgb(230, 230, 230)') {
            $(this).siblings('label').css('background-color','#ccc');
        } else {
            $(this).siblings('label').css('background-color','#ff4400');
        }
    });

    $('.toggle-switch').on('mouseleave', 'input', function() {
        var currentBackground = $(this).siblings('label').css('background-color');
        if (currentBackground == 'rgb(204, 204, 204)') {
            $(this).siblings('label').css('background-color','rgb(234, 234, 234)');
        } else if (currentBackground == 'rgb(238, 238, 238)') {
            $(this).siblings('label').css('background-color','rgb(234, 234, 234)');
        } else if (currentBackground == 'rgb(230, 230, 230)') {
            $(this).siblings('label').css('background-color','rgb(234, 234, 234)');
        } else {
            $(this).siblings('label').css('background-color','#f24100');
        }
    });

    $('#summitmemq1').on('keydown', function (e) {
        if(e.which === 13){
            var companiondata = $('#summitmemq1').val() + '|img/default-user.png|0';
            loadSummitCompanion(companiondata, '');
            if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
                $('#add_peak2').prop('disabled', false);
            }
        }
    });

    $("#btnAddSummitCompanion").click( function() {
        if ($("#summitmemq1").val() != '') {
            var companiondata = $('#summitmemq1').val() + '|img/default-user.png|0';
            loadSummitCompanion(companiondata, '');
            if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
                $('#add_peak2').prop('disabled', false);
            }
        }
        return false;
    });

    //check form input changes to enable save button
    $('#summit-date').on('change', function() {
        if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
            $('#add_peak2').prop('disabled', false);
        }
        if ($('.peaksYouClimbed .field-title').html().substring(0, 5) == 'Peaks') {
            //$('.peaksYouClimbed .field-title').html('Peaks you climbed on ' + $.format.date($("#summit-date").val() + 'T00:00:00Z', 'MMM d, yyyy'));
        } else {
            //$('.peaksYouClimbed .field-title').html('Peak you climbed on ' + $.format.date($("#summit-date").val() + 'T00:00:00Z', 'MMM d, yyyy'));
        }
    });
    $('#rdoSuccess').on('change', function() {
        if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#rdoAttempt').on('change', function() {
        if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#summit').on('input', function() {
        if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#rdoOutAndBack').on('input', function() {
        if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#rdoLoop').on('input', function() {
        if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#rdoPointToPoint').on('input', function() {
        if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#summitmemq1').on('input', function() {
        if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
            $('#add_peak2').prop('disabled', false);
        }
    });

    $("#btnAddActivityTag").click( function() {
        var new_activity = $("#txtAddActivityTag").val();
        if (new_activity != '') {
            var numActivities = $(".user-added-activity").length;
            var activityId = '#user-activity-' + numActivities;
            $("#activity-tag-choices").append('<div class="toggle-switch"><input id="user-activity-'+numActivities+'" class="activity-tag-input user-added-activity" type="checkbox" name="chkActivityTags" value="'+new_activity+'"><label>'+new_activity+'</label></div>');
            $("#user-activity-"+numActivities).prop('checked', true);
            $("#user-activity-"+numActivities).next().animate({
                backgroundColor: '#f24100',
            },200);
            $("#user-activity-"+numActivities).next().css('color','#fff');
            $("#txtAddActivityTag").val('');
            if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
                $('#add_peak2').prop('disabled', false);
            }
        }
        return false;
    });

    $('#activity-tag-choices').on('change', '.activity-tag-input', function () {
        if (this.checked) {
            $(this).next().animate({
                backgroundColor: '#f24100',
            },200);
            $(this).next().css('color','#fff');
        } else {
            $(this).next().animate({
                backgroundColor: '#e6e6e6',
            },200);
            $(this).next().css('color','#000');
        }
        if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
            $('#add_peak2').prop('disabled', false);
        }
    });

    $("#btnAddChallengeTag").click( function() {
        var new_challenge = $("#txtAddChallengeTag").val();
        if (new_challenge != '') {
            var numChallenges = $(".user-added-challenge").length;
            var challengeId = '#user-challenge-' + numChallenges;
            $("#challenge-tag-choices").append('<div class="toggle-switch"><input id="user-challenge-' + numChallenges + '" class="challenge-tag-input user-added-challenge" type="checkbox" name="chkChallengeTags" value="' + new_challenge + '"><label>' + new_challenge + '</label></div>');
            $("#user-challenge-" + numChallenges).prop('checked', true);
            $("#user-challenge-" + numChallenges).next().animate({
                backgroundColor: '#f24100',
            }, 200);
            $("#user-challenge-" + numChallenges).next().css('color', '#fff');
            $("#txtAddChallengeTag").val('');
            if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
                $('#add_peak2').prop('disabled', false);
            }
        }
        return false;
    });

    $('#challenge-tag-choices').on('change', '.challenge-tag-input', function () {
        if (this.checked) {
            $(this).next().animate({
                backgroundColor: '#f24100',
            },200);
            $(this).next().css('color','#fff');
        } else {
            $(this).next().animate({
                backgroundColor: '#e6e6e6',
            },200);
            $(this).next().css('color','#000');
        }
        if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
            $('#add_peak2').prop('disabled', false);
        }
    });

    $("#btnAddGearTag").click( function() {
        var new_gear = $("#txtAddGearTag").val();
        if (new_gear != '') {
            var numGear = $(".user-added-gear").length;
            var gearId = '#user-gear-' + numGear;
            $("#gear-tag-choices").append('<div class="toggle-switch"><input id="user-gear-' + numGear + '" class="gear-tag-input user-added-gear" type="checkbox" name="chkGearTags" value="' + new_gear + '"><label>' + new_gear + '</label></div>');
            $("#user-gear-" + numGear).prop('checked', true);
            $("#user-gear-" + numGear).next().animate({
                backgroundColor: '#f24100',
            }, 200);
            $("#user-gear-" + numGear).next().css('color', '#fff');
            $("#txtAddGearTag").val('');
            if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
                $('#add_peak2').prop('disabled', false);
            }
        }
        return false;
    });

    $('#gear-tag-choices').on('change', '.gear-tag-input', function () {
        if (this.checked) {
            $(this).next().animate({
                backgroundColor: '#f24100',
            },200);
            $(this).next().css('color','#fff');
        } else {
            $(this).next().animate({
                backgroundColor: '#e6e6e6',
            },200);
            $(this).next().css('color','#000');
        }
        if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
            $('#add_peak2').prop('disabled', false);
        }
    });

    $("#btnAddVideoLink").click( function() {
        if ($("#summit-video-link").val() != '') {
            var validVideoLink = isUrlValid($('#summit-video-link').val());
            if (validVideoLink) {
                var video_url = $("#summit-video-link").val();
                processURL(video_url);
                if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
                    $('#add_peak2').prop('disabled', false);
                }
            } else {
                var urlErrors = '<p>Your videos: <b>' + $('#summit-video-link').val() + '</b></p>';
                $('#message-modal-label').html('Invalid URL');
                $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>You entered the following invalid URL</p><p>' + urlErrors + '</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
                $('#message-modal').modal('show');
            }
        }
        return false;
    });

    $("#btnAddRelatedLink").click( function() {
        if ($("#summit-related-link").val() != '') {
            var validRelatedLink = isUrlValid($('#summit-related-link').val());
            if (validRelatedLink) {
                var link_url = $("#summit-related-link").val();
                var numLinks = $(".related-links").length;
                $("#summit-related-links").append('<div class="related-links" id="related-link-' + numLinks + '" style="width: 98%; background-color: #fff; display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px;"><p><span id="related-link-url-' + numLinks + '" style="display: inline-block; margin-left: 20px; font-weight: 500; line-height: 110px;">' + link_url + '</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeRelatedLink(\'' + numLinks + '\');"><i class="fa fa-times"></i></a></span></p></div>');
                var current_links = $("#summit_related_links").val();
                $("#summit_related_links").val(current_links + '|' + link_url + '|,');
                $("#summit-related-link").val('');
                if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
                    $('#add_peak2').prop('disabled', false);
                }
            } else {
                var urlErrors = '<p>Related links: <b>' + $('#summit-related-link').val() + '</b></p>';
                $('#message-modal-label').html('Invalid URL');
                $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>You entered the following invalid URL</p><p>' + urlErrors + '</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
                $('#message-modal').modal('show');
            }
        }
        return false;
    });

    /*
     * Step 2 javascripts
     */
    $(document).bind('close.facebox', function() {location.reload(true) });
    $('#step2').facebox();
    $('input[title!=""]').hint();
    //$('a#skip2').click(function(){ $("a#step2").trigger('click') });
    $("#date").datepicker({
        dateFormat: 'yy-mm-dd',
        dayNamesMin: [ "S", "M", "T", "W", "T", "F", "S" ],
        changeMonth: false,
        changeYear: false,
        yearRange: "1950:{% now "Y" %}",
        onSelect: function() {
            var date = new Date($(this).val().substring(0,4), $(this).val().substring(5,7)-1, $(this).val().substring(8,10));
            var dd = date.getDate();
            var mm = date.getMonth();
            var yyyy = date.getFullYear();
            //make sure date not in the future
            var selectedDate = new Date(yyyy, mm, dd);
            var today = new Date();
            if (selectedDate > today) {
                var tempDate = new Date($('#summit-date').val().substring(0,4), $('#summit-date').val().substring(5,7)-1, $('#summit-date').val().substring(8,10));
                dd = tempDate.getDate();
                mm = tempDate.getMonth();
                yyyy = tempDate.getFullYear();
                var originalDate = new Date(yyyy, mm, dd);
                $("#date").datepicker("setDate", originalDate);
                $('.ui-datepicker-month').val(mm);
                $('.ui-datepicker-year').val(yyyy);
            } else {
                if (dd < 10) {
                    dd = '0' + dd;
                }
                mm = mm + 1;
                if (mm < 10) {
                    mm = '0' + mm;
                }
                var date = yyyy + '-' + mm + '-' + dd;
                $('#summit-date').val(date).trigger('change');
            }
        }
    });

    if (!summit_ids) {
        $("#date").datepicker("setDate", new Date());

        var today = new Date();
        var dd = today.getDate();
        var mm = today.getMonth() + 1;
        var yyyy = today.getFullYear();
        if (dd < 10) {
            dd = '0' + dd;
        }
        if (mm < 10) {
            mm = '0' + mm;
        }
        var today = yyyy + '-' + mm + '-' + dd;
        $('#summit-date').val(today);

        //set peaks you climbed title
        //$('.peaksYouClimbed .field-title').html('Peak you climbed on ' + $.format.date($("#summit-date").val() + 'T00:00:00Z', 'MMM d, yyyy'));
    }

    $("textarea").click(function(){ reset_textareas($(this)) });

    $('.ui-datepicker-month').on('change', function () {
        var newMonth = parseInt($('.ui-datepicker-month').val()) + 1;
        var newYear = $('.ui-datepicker-year').val();
        $("#date").datepicker( "setDate", newYear+'-'+newMonth+'-01' );
        $('.ui-state-active').removeClass('ui-state-active');
        $(this).blur();
    });

    $('.ui-datepicker-year').on('change', function () {
        var newMonth = parseInt($('.ui-datepicker-month').val()) + 1;
        var newYear = $('.ui-datepicker-year').val();
        $("#date").datepicker( "setDate", newYear+'-'+newMonth+'-01' );
        $('.ui-state-active').removeClass('ui-state-active');
        $(this).blur();
    });

    var loading = "<div style='width:108px;'>Uploading...</div>";

    var allowedExtensions = ['jpg', 'jpeg'];
    var fileNum = 1;
    var uploader = new qq.s3.FineUploader({
        debug: false,
        element: document.getElementById('uploader'),
        template: 'qq-image-template',
        request: {
            endpoint: 'https://peakery-media.s3.amazonaws.com',
            accessKey: 'AKIAJPWBM4YZXFHWBTPQ'
        },
        signature: {
            endpoint: '{% url "s3signature" %}'
        },
        uploadSuccess: {
            endpoint: '{% url "s3_summit_photo_upload" %}',
            params: {
                'summit_id': '{{ summit.id }}',
                'photo_index': function() {
                    return fileNum++;
                }
            }
        },
        {% if photos %}
        session: {
            endpoint: '{% url "s3_summit_photo_init" %}',
            params: {
                'summit_id': '{{ summit.id }}'
            }
        },
        {% endif %}
        iframeSupport: {
            localBlankPagePath: '/api/s3blank/'
        },
        retry: {
           enableAuto: false // defaults to false
        },
        validation: {
            acceptFiles: ['image/jpg, image/jpeg, .jpg, .jpeg'],
            allowedExtensions: allowedExtensions,
            sizeLimit: 15000000,
            image: {
                minHeight: 1000,
                minWidth: 1000
            }
        },
        messages: {
            typeError: 'Sorry, must be a JPG file.',
            sizeError: 'Sorry, this file is too big. Must be under 15 MB.',
            minHeightImageError: 'Sorry, this photo is too small. Must be at least 1000 x 1000 pixels.',
            minWidthImageError: 'Sorry, this photo is too small. Must be at least 1000 x 1000 pixels.'
        },
        showMessage: function (message) {
            $('#message-modal-label').html('Error');
            $('#message-modal-body').html(message);
            $('#message-modal').modal('show');
        },
        text: {
            fileInputTitle: 'Choose file(s)'
        },
        callbacks: {
            onSubmit: function(id, name) {},
            onSubmitted: function(id, name) {
                uploaderIdle = false;
                if ($('#climbed_peak_ids').val() != '') {
                    $('#add_peak2').prop('disabled', false);
                }
                $('.qq-upload-list > li').each(function () {
                    var photoId = $(this).attr('qq-file-id');
                    photoId++;
                    $(this).find('.remove-photo').attr('id','file-' + photoId + '-remove-photo');
                    $(this).find('.remove-photo').on('click', function() {
                        confirmRemovePhoto('file-' + photoId);
                    });
                    $(this).find('.addACaption').attr('id','file-' + photoId + '-caption');
                    $(this).find('.hiddenPhoto').attr('id','file-' + photoId + '-photo');
                });
            },
            onComplete: function(id, name, responseJSON, maybeXhr) {
                var fileId = id + 1;
                $('#file-' + fileId + '-caption').attr('name', 'caption'+responseJSON.photo_id);
                $('#file-' + fileId + '-photo').attr('name', 'photo'+responseJSON.photo_id);
                $('#file-' + fileId + '-photo').val(responseJSON.photo_id);
            },
            onAllComplete: function(successful, failed) {
                uploaderIdle = true;
                if (formSubmitted) {
                    saveLog();
                }
            },
            onCancel: function(id, name) {},
            onUpload: function(id, name) {},
            onUploadChunk: function(id, name, chunkData) {},
            onUploadChunkSuccess: function(id, chunkData, responseJSON, xhr) {},
            onResume: function(id, fileName, chunkData) {},
            onProgress: function(id, name, loaded, total) {},
            onTotalProgress: function(loaded, total) {},
            onError: function(id, name, reason, maybeXhrOrXdr) {
                $('#message-modal-label').html('Error');
                $('#message-modal-body').html('Error uploading ' + name + ' because of ' + reason + '. Hit the Retry button on the photo to try again.');
                $('#message-modal').modal('show');
            },
            onAutoRetry: function(id, name, attemptNumber) {},
            onManualRetry: function(id, name) {},
            onValidateBatch: function(fileOrBlobData) {},
            onValidate: function(fileOrBlobData) {},
            onSubmitDelete: function(id) {},
            onDelete: function(id) {},
            onDeleteComplete: function(id, xhrOrXdr, isError) {},
            onPasteReceived: function(blob) {},
            onStatusChange: function(id, oldStatus, newStatus) {},
            onSessionRequestComplete: function(response, success, xhrOrXdr) {
                if (success) {
                    for ( var i = 0; i < response.length; i++) {
                        var obj = response[i];
                        //console.log(obj);
                        var photoId = i + 1;
                        fileNum++;
                        $('.qq-file-id-'+i).find('.remove-photo').attr('id','file-' + photoId + '-remove-photo');
                        $('.qq-file-id-'+i).find('.remove-photo').data('photoid',photoId);
                        $('.qq-file-id-'+i).find('.qq-thumbnail-selector').attr('src', obj.thumbnail_url);
                        $('.qq-file-id-'+i).find('.remove-photo').on('click', function() {
                            confirmRemovePhoto('file-' + $(this).data('photoid'));
                        });
                        $('.qq-file-id-'+i).find('.addACaption').attr('id','file-' + photoId + '-caption');
                        $('.qq-file-id-'+i).find('.hiddenPhoto').attr('id','file-' + photoId + '-photo');
                        $('#file-' + photoId + '-caption').attr('name', 'caption'+obj.uuid);
                        $('#file-' + photoId + '-photo').attr('name', 'photo'+obj.uuid);
                        $('#file-' + photoId + '-photo').val(obj.uuid);
                        $('#file-' + photoId + '-caption').val(obj.caption);
                    }
                }
            }
        },
        objectProperties: {
            acl: 'public-read',
            key: function (fileId) {

                var filename = uploader.getName(fileId);
                var uuid = uploader.getUuid(fileId);
                var ext = filename.substr(filename.lastIndexOf('.') + 1).toLowerCase();

                return  'items/users/' + uuid + '.' + ext;

            }
        }
    });

    /*
     * Step 3 javascripts
     */
    $("h4.routes-companion-info").click(function(){
        $("div.routes-companion-info").slideToggle();
        //if ($(this).hasClass("open")){$(this).removeClass("open")}else{$(this).addClass("open")}
    });

    $("span#same-route").click(function(){
        var up = $("input#route_up").val();
        if (up!=""){
            $("input#route_down").val(up);
            $("input#route_down").removeClass("blur");
        }
    });

    $("#route_up").change(function(){
        if ($(this).val() == 'add_new_route') {
            $('#route_up_container').hide();
            $('#summit-route-name').show().focus();
            $('#hide-new-summit-route-span').show();
        }
        if (uploaderIdle && $('#climbed_peak_ids').val() != '') {
            $('#add_peak2').prop('disabled', false);
        }
    });

    $("#hide-new-summit-route").on('click', function(){
        $('#summit-route-name').hide().val('');
        $('#hide-new-summit-route-span').hide();
        $('#route_up_container').show();
        $('#route_up').val('0');
    });

    textAreaAdjust();

    //set metadata checkboxes
    {% if summit.trip_metadata %}
    var json = '{{ summit.trip_metadata }}'.replace(/&quot;/g,'"');
    var trip_metadata = $.parseJSON(json);

    $.each(trip_metadata, function (key, data) {
        if (key == 'type') {
            for (var i = 0; i < data.length; i++) {
                if ($('.trip-type-input[value="'+data[i]+'"]').prop('checked') == false) {
                    $('.trip-type-input[value="'+data[i]+'"]').prop('checked', true);
                }
            }
        }
        if (key == 'gear') {
            for (var i = 0; i < data.length; i++) {
                if ($('.gear-tag-input[value="'+data[i]+'"]').prop('checked') == false) {
                    $('.gear-tag-input[value="' + data[i] + '"]').prop('checked', true);
                    $('.gear-tag-input[value="' + data[i] + '"]').next().animate({
                        backgroundColor: '#f24100',
                    },200);
                    $('.gear-tag-input[value="' + data[i] + '"]').next().css('color','#fff');
                } else {
                    var new_gear = data[i];
                    var numGear = $(".user-added-gear").length;
                    var gearId = '#user-gear-'+numGear;
                    $("#gear-tag-choices").append('<div class="toggle-switch"><input id="user-gear-'+numGear+'" class="gear-tag-input user-added-gear" type="checkbox" name="chkGearTags" value="'+new_gear+'"><label>'+new_gear+'</label></div>');
                    $("#user-gear-"+numGear).prop('checked', true);
                    $("#user-gear-"+numGear).next().animate({
                        backgroundColor: '#f24100',
                    },200);
                    $("#user-gear-"+numGear).next().css('color','#fff');
                }
            }
        }
        if (key == 'activities') {
            for (var i = 0; i < data.length; i++) {
                if ($('.activity-tag-input[value="'+data[i]+'"]').prop('checked') == false) {
                    $('.activity-tag-input[value="' + data[i] + '"]').prop('checked', true);
                    $('.activity-tag-input[value="' + data[i] + '"]').next().animate({
                        backgroundColor: '#f24100',
                    },200);
                    $('.activity-tag-input[value="' + data[i] + '"]').next().css('color','#fff');
                } else {
                    var new_activity = data[i];
                    var numActivities = $(".user-added-activity").length;
                    var activityId = '#user-activity-'+numActivities;
                    $("#activity-tag-choices").append('<div class="toggle-switch"><input id="user-activity-'+numActivities+'" class="activity-tag-input user-added-activity" type="checkbox" name="chkActivityTags" value="'+new_activity+'"><label>'+new_activity+'</label></div>');
                    $("#user-activity-"+numActivities).prop('checked', true);
                    $("#user-activity-"+numActivities).next().animate({
                        backgroundColor: '#f24100',
                    },200);
                    $("#user-activity-"+numActivities).next().css('color','#fff');
                }
            }
        }
        if (key == 'challenges') {
            for (var i = 0; i < data.length; i++) {
                if ($('.challenge-tag-input[value="'+data[i]+'"]').prop('checked') == false) {
                    $('.challenge-tag-input[value="' + data[i] + '"]').prop('checked', true);
                    $('.challenge-tag-input[value="' + data[i] + '"]').next().animate({
                        backgroundColor: '#f24100',
                    },200);
                    $('.challenge-tag-input[value="' + data[i] + '"]').next().css('color','#fff');
                } else {
                    var new_challenge = data[i];
                    var numChallenges = $(".user-added-challenge").length;
                    var challengeId = '#user-challenge-'+numChallenges;
                    $("#challenge-tag-choices").append('<div class="toggle-switch"><input id="user-challenge-'+numChallenges+'" class="challenge-tag-input user-added-challenge" type="checkbox" name="chkChallengeTags" value="'+new_challenge+'"><label>'+new_challenge+'</label></div>');
                    $("#user-challenge-"+numChallenges).prop('checked', true);
                    $("#user-challenge-"+numChallenges).next().animate({
                        backgroundColor: '#f24100',
                    },200);
                    $("#user-challenge-"+numChallenges).next().css('color','#fff');
                }
            }
        }
    });
    {% endif %}

    //load companions
    {% if companions %}
        var companion_data;
        {% for c in companions %}
            companion_data = '{{ c.username }}|{{ c.avatar_url|urlencode }}|{{ c.id }}';
            loadSummitCompanion(companion_data, '');
        {% endfor %}
    {% endif %}

    //load videos
    {% if videos %}
        {% for v in videos %}
            processURL('{{ v.url }}');
        {% endfor %}
    {% endif %}

    //load related links
    {% if related_links %}
        var link_url, numLinks, current_links;
        {% for r in related_links %}
            link_url = '{{ r.url }}';
            numLinks = $(".related-links").length;
            $("#summit-related-links").append('<div class="related-links" id="related-link-' + numLinks + '" style="width: 98%; background-color: #f0f0f0; display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px;"><p><span id="related-link-url-' + numLinks + '" style="display: inline-block; margin-left: 20px; font-weight: 500; line-height: 110px;">' + link_url + '</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeRelatedLink(\'' + numLinks + '\');"><i class="fa fa-times"></i></a></span></p></div>');
            current_links = $("#summit_related_links").val();
            $("#summit_related_links").val(current_links + '|' + link_url + '|,');
        {% endfor %}
    {% endif %}

});

function check_is_in(marker){
    return map.getBounds().contains(marker.getPosition());
}

function delete_out_markers(){
    if (markersArray){
        for (i in markersArray){
            if (!check_is_in(markersArray[i])){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }
}

function deletehighest(){
    if (markersArray){
        for (i in markersArray){
            if (markersArray[i].properties.iconstyle == 'marker_icon_redgreen' || markersArray[i].properties.iconstyle == 'marker_icon_red'){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }
}

function limit_number_of_markers(limit){
    if (markersArray.length > limit){
        for (i = markersArray.length-1; i>=limit; i--){
            markersArray[i].remove();
            markersArray.splice(i,1);
        }
    }
}

function elevation_range(data){
    if (markersArray){
        for (i = markersArray.length-1; i>=0; i--){
            var inrange = false;
            $.each(data, function(k, v){
                var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                    inrange = true;
                }
            });
            if (!inrange){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }
}

function delete_old_markers(data){
    if (markersArray){
        for (i = markersArray.length-1; i>=0; i--){
            var inrange = false;
            $.each(data, function(k, v){
                var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                    inrange = true;
                }
            });
            if (!inrange){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }
}

function delete_all_markers(){
    if (markersArray){
        for (i = markersArray.length-1; i>=0; i--){
            markersArray[i].remove();
            markersArray.splice(i,1);
        }
    }
}

function fromLatLngToString(latLng) {
    return latLng.lat + ',' + latLng.lng;
}

function check_lat_lon(lat, lon){

    var ck_lat = /^(-?[1-8]?\d(?:\.\d{1,18})?|90(?:\.0{1,18})?)$/;
    var ck_lon = /^(-?(?:1[0-7]|[1-9])?\d(?:\.\d{1,18})?|180(?:\.0{1,18})?)$/;

    var validLat = ck_lat.test(lat);
    var validLon = ck_lon.test(lon);
    if(validLat && validLon) {
        return true;
    } else {
        return false;
    }
}

function saveUnaddedText() {
    //when click the button to save unadded text
    if ($('#txtAddActivityTag').val() != '') {
        $("#btnAddActivityTag").click();
    }
    if ($('#txtAddChallengeTag').val() != '') {
        $("#btnAddChallengeTag").click();
    }
    if ($('#txtAddGearTag').val() != '') {
        $("#btnAddGearTag").click();
    }
    if ($('#summitmemq1').val() != '') {
        $("#btnAddSummitCompanion").click();
    }
    if ($('#summit-video-link').val() != '') {
        $("#btnAddVideoLink").click();
    }
    if ($('#summit-related-link').val() != '') {
        $("#btnAddRelatedLink").click();
    }
    $('#savetext-modal').modal('hide');
    formSubmitted = true;
    $('#add_peak2').html('<i class="fa fa-spinner fa-spin"></i>');
    $('#darkness').append('<div style="height: 100%; width: 100%; color: #fff; display: flex; justify-content: center; align-items: center;"><span style="width: 300px; text-align: center;">Saving your log<br /><br /><div style="text-align: center;"><i style="font-size: 22px;" class="fa fa-spinner fa-2x fa-spin" aria-hidden="true"></i></div></span></div>');
	$('#darkness').show();
	window.scrollTo(0,0);
    if (uploaderIdle) {
        saveLog();
    }
}

function replaceAll(str, find, replace) {
  return str.replace(new RegExp(find, 'g'), replace);
}

function numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

function textAreaAdjust() {
    $('#summit').height(1);
    var scrollHeight = document.getElementById("summit").scrollHeight;
    $('#summit').height(25+scrollHeight);
}

function reset_textareas(selector){
    var value = $(selector).val();
    if (value=='full details of your trip...' || value=='write a caption...'){$(selector).val("")}
    $(selector).removeClass("blur");
}

function jailai(selector){
    $("textarea#"+selector).effect("highlight", {}, 3000);
}

function reset_spinner(selector){
    $(selector).find(selector+'-spinner').remove();
    $(selector).find('div.qq-upload-button').removeClass('loading');
    $(selector).find('div.qq-upload-button').children('div').hide();
    $(selector).siblings('.caption').hide();
    $(selector).find('div.qq-uploader').show();
}

function reset_gpx_spinner(selector){
    $(selector).find('div.qq-upload-button').removeClass('qq-upload-loading');
    $(selector).find('div.qq-upload-button').children('div').hide();
};

function cancelEdit() {
    if ($('#add_peak2').prop('disabled') == true) {
        window.location.href = '{{ request.META.HTTP_REFERER }}';
    } else {
        $('#confirm-modal-label').html('Discard your changes?');
        $('#confirm-modal-body').html('<div style="margin-top: 20px; text-align: center;"><a href="{% if IS_MOBILE_APP_ACCESS == 'True' %}javascript:Android.goBack();{% else %}{{ request.META.HTTP_REFERER }}{% endif %}" class="btn btn-primary" style="width: 100px;">Discard</a><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></div>');
        $('#confirm-modal').modal('show');
    }
}

function cancelLocationSearch() {
    $('#location-search-title').hide();
    $('#cancel-location-search-div').hide();
    $('#log-a-climb-title').show();
    $('#save-changes-button-div').show();
    $('#cancel-x-button-div').show();
    $('#peaks-climbed-map-container').hide();
    $('#peaks-climbed-search-container').show();
}

function confirmRemovePhoto(where) {
    $('#confirm-modal-label').html('Are you sure?');
    $('#confirm-modal-body').html('<div style="margin-top: 20px; text-align: center;"><button onclick="removePhoto(\''+where+'\'); return false;" class="btn btn-primary" style="width: 100px;">Discard</button><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></div>');
    $('#confirm-modal').modal('show');
}

function removePhoto(where) {
    $('#confirm-modal').modal('hide');
    var step_number = where.split("-")[1];
    step_number--;
    $('.qq-file-id-'+step_number).remove();
}

function geoCode() {
    geocoding = true;
    var geocoder = new google.maps.Geocoder();
    var resultsMap = map;
    var address = $('#peak-location-search').val();
    geocoder.geocode({'address': address, 'region': 'US'}, function(results, status) {
      if (status === 'OK') {
        resultsMap.setCenter([results[0].geometry.location.lng(), results[0].geometry.location.lat()]);
        if (typeof results[0].geometry.bounds != 'undefined') {
            resultsMap.fitBounds([[results[0].geometry.bounds.getSouthWest().lng(),results[0].geometry.bounds.getSouthWest().lat()],[results[0].geometry.bounds.getNorthEast().lng(),results[0].geometry.bounds.getNorthEast().lat()]]);
            var $mapDiv = $('#map-canvas');
            var mapDim = { height: $mapDiv.height(), width: $mapDiv.width() };
            var newZoom = getBoundsZoomLevel(results[0].geometry.bounds, mapDim);
            resultsMap.setZoom(newZoom - 2);
            var southWest = results[0].geometry.bounds.getSouthWest();
            var northEast = results[0].geometry.bounds.getNorthEast();
            var sw = new mapboxgl.LngLat(southWest.lng() - 0.25, southWest.lat() - 0.25);
            var ne = new mapboxgl.LngLat(northEast.lng() + 0.25, northEast.lat() + 0.25);
            var llb = new mapboxgl.LngLatBounds(sw, ne);
            loadPeaksFromMapSearch(llb);
        } else {
            //show message
            var near = $('#peak-location-search').val();
            $('#message-modal-label').html('Location Error');
            $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>No matching locations found for</p><p><strong>' + near + '</strong></p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
            $('#message-modal').modal('show');
        }
      } else {
        //show message
        var keyword = $('#peak-name-search').val();
        var near = $('#peak-location-search').val();
        var peakErrors = '';
        if (keyword != '') {
            peakErrors = 'Keyword: <strong>"'+keyword+'"</strong> ';
        }
        if (near != '') {
            peakErrors = peakErrors + 'Near: <strong>"'+near+'"</strong> ';
        }
        $('#message-modal-label').html('No peaks found');
        $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>No matching peaks found for</p><p>' + peakErrors + '</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
        $('#message-modal').modal('show');
      }
    });
}

function getBoundsZoomLevel(bounds, mapDim) {
    var WORLD_DIM = { height: 256, width: 256 };
    var ZOOM_MAX = 21;

    function latRad(lat) {
        var sin = Math.sin(lat * Math.PI / 180);
        var radX2 = Math.log((1 + sin) / (1 - sin)) / 2;
        return Math.max(Math.min(radX2, Math.PI), -Math.PI) / 2;
    }

    function zoom(mapPx, worldPx, fraction) {
        return Math.floor(Math.log(mapPx / worldPx / fraction) / Math.LN2);
    }

    var latFraction = (latRad(bounds.getNorthEast().lat()) - latRad(bounds.getSouthWest().lat())) / Math.PI;

    var lngDiff = bounds.getNorthEast().lng() - bounds.getSouthWest().lng();
    var lngFraction = ((lngDiff < 0) ? (lngDiff + 360) : lngDiff) / 360;

    var latZoom = zoom(mapDim.height, WORLD_DIM.height, latFraction);
    var lngZoom = zoom(mapDim.width, WORLD_DIM.width, lngFraction);

    return Math.min(latZoom, lngZoom, ZOOM_MAX);
}

function set_gpx(responseJSON){

    var valid_file = responseJSON.valid_file;

    if (valid_file == 'true') {

        gpx_url = responseJSON.gpx_file;

        var new_center = null;
        $.ajax({
            type: "GET",
            url: gpx_url,
            dataType: "xml",
            success: function (xml) {
                bounds = new mapboxgl.LngLatBounds();
                $(xml).find("trkpt").each(function () {

                    if ($("#gpx-start-lat").val() == '') {
                        $("#gpx-start-lat").val($(this).attr("lat"));
                    }
                    if ($("#gpx-start-lon").val() == '') {
                        $("#gpx-start-lon").val($(this).attr("lon"));
                    }
                    $("#gpx-end-lat").val($(this).attr("lat"));
                    $("#gpx-end-lon").val($(this).attr("lon"));

                    var lat = $(this).attr("lat");
                    var lon = $(this).attr("lon");
                    var p = new mapboxgl.LngLat(lon, lat);
                    points.push(p);
                    linePoints.push([p.lng, p.lat]);
                    if (new_center == null) {
                        new_center = p;
                    }
                    bounds.extend(p);
                });
                $(xml).find("rtept").each(function () {

                    if ($("#gpx-start-lat").val() == '') {
                        $("#gpx-start-lat").val($(this).attr("lat"));
                    }
                    if ($("#gpx-start-lon").val() == '') {
                        $("#gpx-start-lon").val($(this).attr("lon"));
                    }
                    $("#gpx-end-lat").val($(this).attr("lat"));
                    $("#gpx-end-lon").val($(this).attr("lon"));

                    var lat = $(this).attr("lat");
                    var lon = $(this).attr("lon");
                    var p = new mapboxgl.LngLat(lon, lat);
                    points.push(p);
                    linePoints.push([p.lng, p.lat]);
                    if (new_center == null) {
                        new_center = p;
                    }
                    bounds.extend(p);
                });

                lineData = {
                    "type": "Feature",
                    "properties": {},
                    "geometry": {
                        "type": "LineString",
                        "coordinates": linePoints
                    }
                }
                map.addSource('route-data', {
                    type: 'geojson',
                    data: lineData
                });
                map.addLayer({
                    "id": "route-layer",
                    "type": "line",
                    "source": "route-data",
                    "layout": {
                        "line-join": "round",
                        "line-cap": "round"
                    },
                    "paint": {
                        "line-color": "#fc202e",
                        "line-width": 4
                    }
                });

                //create an HTML element for the marker
                var el = document.createElement('div');
                el.className = 'route_marker_fc202e';

                startmarker = new mapboxgl.Marker(el)
                    .setLngLat(points[0])
                    .setOffset([-5, -10])
                    .setDraggable(false)
                    .addTo(map);

                // fit bounds to track
                map_init = false;
                map.fitBounds(bounds, {padding: 50, duration: 0});

                setMapControls();

                $('.peaksYouClimbed .field-title').show();

                $('#peaks-climbed-map-instructions-text').find('.header-help').html('Missing a peak you climbed or attempted? Select it from the map');

            }
        });
        $('#peak-location-search-container').hide();
        $('#peaks-climbed-search-container').hide();
        $('#peaks-climbed-map-container').show();
        $('#map-canvas').show();

        $('#peaks-climbed-map-instructions').show();

        {% if init_gpx_file %}
        {% else %}
        $('#log-a-climb-title').hide();
        $('#save-changes-button-div').hide();
        $('#cancel-x-button-div').hide();
        $('#location-search-title').show();
        $('#cancel-location-search-div').show();
        {% endif %}

        map.resize();

    }
}

$(function(){
    $('textarea.addACaption').placeholder();
});

    function gmapsCallback() {
        // Empty function needed for the google maps callback
    }

</script>

    {% load item_tags %}{% get_gmaps_lib %}
    {% block gmaps_lib %}{% endblock %}

{% endblock %}

{% block end_full_height_form %}
</form>
{% endblock %}

{% load widget_tweaks %}
{% load thumbnail %}

{% block content %}
<script type="text/javascript">
  $(document).ready(function(){
    $("#id_countries").change(function () {
        var country_id = $(this).val();
        $("#id_regions").html("<option>Loading...</option>");
        $.getJSON("{% url "get_regions_by_country" %}",{'country_id':country_id},function(data) {
            $("#id_regions").html("");
            $.each(data, function(k,v){
                 $('#id_regions').append( new Option(v.label,v.id) );
            });
        });
    });


  $("#form_resolve_region").submit(function() {
                $(this).ajaxSubmit({
                    target: '#output_resolve',
                    success:    function(e) {
                       var o = jQuery.parseJSON(e);
                       //
                      // {'status':False , 'id':-1, 'step2': False}

                            $('#thanks').facebox();
                            $("a#thanks").trigger('click');
                        }
                    })
                return false;
            });
  });
</script>

<a id="thanks" href="{% url "item_add_thanks" %}" style="display: none"></a>
<div id="output_resolve" style="display:none"></div>
<div style="width:400px">
    <div class="title">
        <h1>Select peak location</h1>
        <h3>for {{ name }}</h3>
    </div>
  <form id="form_resolve_region" action="{% url "item_add_resolve_region_with_id" id %}" method="POST" >
  {{ form.tempitem }}
  
    <div class="fieldset">
        <h3>Country: </h3>
        <div class="clearfix">
            {{ form.countries }}
        </div>
    </div><!-- end .fieldset -->

    <div class="fieldset">
        <h3>Sub-region: </h3>
        <div class="clearfix">
            {{ form.regions }}
        </div>
    </div><!-- end .fieldset -->
    
    <style type="text/css">
        .title h1 {
            margin-bottom: 2px;
            font-size: 27px;
        }
        .title h3 {
            font-size: 17px;
            color: #666;
        }
        form#form_resolve_region {
            margin-bottom: 0px;
        }
        .fieldset {
            width: 100%;
            display: inline-block;
            clear: both;
            padding: 5px;
        }
        .fieldset:last-child {
            margin-bottom: 20px;
        }
        .fieldset h3 {
            float: left;
            margin: 0;
            width: 120px;
            font-size: 18px;
            line-height: 29px;
        }
        .fieldset .clearfix {
            width: 200px;
            float: right;
            margin-right: 20px;
        }
        .fieldset .clearfix select {
            width: 200px;
        }
    </style>
  
    <ul class="horz righted">
        <li><input class="btn set2 input" id="add_peak" type="submit" value="ADD PEAK!" /></li>
    </ul>
   </form>
</div>


{% endblock %}

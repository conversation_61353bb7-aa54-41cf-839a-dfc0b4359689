from peakery.items.models import <PERSON><PERSON>, <PERSON>Route, SummitLog
from django.core.management.base import BaseCommand
from django.db import connection
import sys
from peakery.utils.utils import dictfetchall
from django.template.defaultfilters import slugify

class Command(BaseCommand):
    help = 'Processes GPX files for summit logs.'

    def add_arguments(self, parser):
        parser.add_argument('summitlog_id_digit')

    def handle(self, *args, **options):
        summitlog_id_digit = options['summitlog_id_digit']
        from django.core.files.storage import default_storage
        import gpxpy
        import gpxpy.gpx
        import polyline

        self.stdout.write('Finding summit logs...\n')

        sql = "select a.id, a.item_id, a.gpx_file " + \
              "from items_summitlog a, items_item b " + \
              "where right(cast(a.id as text),1) = %s and length(a.gpx_file) > 0 and a.encoded_polyline is null and a.item_id = b.id "

        summitlogs = SummitLog.objects.raw(sql, [summitlog_id_digit])

        for s in summitlogs:
            gpx_path = s.gpx_file

            self.stdout.write('Processing: %s' % gpx_path)

            try:

                # create polyline from gpx

                f = default_storage.open(s.gpx_file, 'r')
                gpx = gpxpy.parse(f)

                # determine reduction factor
                total_points = 0
                reduction_factor = 1
                if gpx.tracks:
                    for track in gpx.tracks:
                        for segment in track.segments:
                            total_points += len(segment.points)
                elif gpx.routes:
                    for route in gpx.routes:
                        total_points += len(route.points)

                if total_points <= 7200:
                    reduction_factor = 10
                elif total_points <= 14400:
                    reduction_factor = 20
                elif total_points <= 28800:
                    reduction_factor = 40
                else:
                    reduction_factor = 80

                gpx.reduce_points(None, reduction_factor)

                points = []
                if gpx.tracks:
                    for track in gpx.tracks:
                        for segment in track.segments:
                            for point in segment.points:
                                points.append([point.latitude, point.longitude])
                elif gpx.routes:
                    for route in gpx.routes:
                        for point in route.points:
                            points.append([point.latitude, point.longitude])

                gpx_polyline = polyline.encode(points, 5)

                sql = "update items_summitlog " + \
                      "set encoded_polyline = %s " + \
                      "where id = %s "

                cursor = connection.cursor()
                cursor.execute(sql, [gpx_polyline, s.id])

            except:
                self.stdout.write('Unable to parse GPX file.\n')

        self.stdout.write('END\n')

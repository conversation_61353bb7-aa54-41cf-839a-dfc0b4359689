from django.core.management.base import BaseCommand
from django.db.models import Count

from peakery.items.models import Item


class Command(BaseCommand):
    args = ''
    help = 'Fix South Korea peak names'

    def handle(self, *args, **options):
        items_in_kr = Item.objects.filter(country__code="KR")

        dups = (
            items_in_kr
            .values('slug_new_text')
            .annotate(slug_count=Count('id'))
            .filter(slug_count__gt=1)
        )

        dup_slugs = [item['slug_new_text'] for item in dups]

        dup_items_in_kr = items_in_kr.filter(slug_new_text__in=dup_slugs).order_by('slug_new_text', 'id')

        from collections import defaultdict

        grouped = defaultdict(list)
        for item in dup_items_in_kr:
            grouped[item.slug_new_text].append(item)

        for slug_text, items in grouped.items():
            for index, item in enumerate(items):
                if index == 0:
                    continue  # Skip the first one
                item.slug_new_text = f"{slug_text}{index + 1}"
                item.name = f"{item.name}{index + 1}"
                item.slug = f"{item.slug}{index + 1}"
                item.save()

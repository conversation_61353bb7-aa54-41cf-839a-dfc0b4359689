from peakery.items.models import Item
from peakery.cities.models import Country, Region
from django.core.management.base import BaseCommand
from django.db import connection
from peakery.utils.utils import dictfetchall
import boto3
from django.conf import settings

class Command(BaseCommand):
    help = 'Rebuilds all peak photos flagged as missing.'
    args = ''

    def handle(self, *args, **options):

        self.stdout.write('Rebuilding all peak photos flagged as missing...\n')

        from PIL import Image, ImageFilter, ExifTags
        from io import BytesIO
        import os.path
        from django.core.files.storage import default_storage

        self.stdout.write('Finding user photos to rebuild...\n')

        path_string = 'items/users/%'

        sql = "select a.id, a.image, replace(a.image,'items/users/','') as basefile " + \
            "from items_itemphoto a " + \
            "where a.image like %s and a.missing = true "

        cursor = connection.cursor()
        cursor.execute(sql, [path_string])
        photos = dictfetchall(cursor)

        for p in photos:

            file_extension = os.path.splitext(p['basefile'])[1][1:].lower()
            if file_extension == 'png':
                extension = 'PNG'
            else:
                extension = 'JPEG'

            upload_path = p['image']
            test_path = 'images/items/users/cache/%s.910x680_q95.jpg' % (p['basefile'])
            thumb_file_path = 'images/items/users/cache/%s' % (p['basefile'])

            s3_client = boto3.client('s3', aws_access_key_id=settings.AWS_ACCESS_KEY_ID, aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)

            try:
                response = s3_client.list_objects_v2(Bucket='peakery-media', Prefix=test_path)
                if response['KeyCount'] > 0:
                    self.stdout.write('Skipping: %s' % (p['image']))
                    continue
            except Exception as e:
                # Thumbnails exist
                self.stdout.write('Skipping: %s' % (p['image']))
                continue

            self.stdout.write('Processing: %s' % (p['image']))

            try:
                f = default_storage.open(upload_path, 'rb')
                image = Image.open(f)

                try:
                    for orientation in ExifTags.TAGS.keys():
                        if ExifTags.TAGS[orientation] == 'Orientation':
                            break
                    exif = dict(image._getexif().items())

                    if exif[orientation] == 3:
                        image = image.rotate(180, expand=True)
                    elif exif[orientation] == 6:
                        image = image.rotate(270, expand=True)
                    elif exif[orientation] == 8:
                        image = image.rotate(90, expand=True)

                except (AttributeError, KeyError, IndexError):
                    # cases: image don't have getexif
                    pass

                width, height = image.size

                # 1920x1440 thumbnail
                if width >= height:
                    # landscape orientation photo
                    if float(width) / float(height) > 1.333333:
                        # possible panoramic photo, let's keep aspect ratio intact
                        left = 0
                        upper = 0
                        right = width
                        lower = height
                    else:
                        delta = height - (width / 1.333333)
                        left = 0
                        upper = int(delta / 2)
                        right = width
                        lower = height - upper
                else:
                    # portrait orientation photo
                    if float(height) / float(width) > 1.333333:
                        delta = height - (1.333333 * width)
                        left = 0
                        upper = int(delta / 2)
                        right = width
                        lower = height - upper
                    else:
                        delta = width - (height / 1.333333)
                        left = int(delta / 2)
                        upper = 0
                        right = width - left
                        lower = height

                tmp_image = image.crop((left, upper, right, lower))
                if width >= height:
                    new_width = 1920
                    if float(width) / float(height) > 1.333333:
                        # possible panoramic photo, let's keep aspect ratio intact
                        aspect_ratio = float(width) / float(height)
                        new_height = int(1920 / aspect_ratio)
                    else:
                        new_height = 1440
                else:
                    new_width = 1440
                    new_height = 1920
                tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
                f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '1920x1440_q95_crop.jpg'), "w")
                # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                out_img = BytesIO()
                # You MUST specify the file type because there is no file name to discern it from
                tmp_image.save(out_img, format='JPEG', subsampling=0, quality=95)
                f_thumb.write(out_img.getvalue())
                f_thumb.close()

                # 480x360 thumbnail
                if width >= height:
                    # landscape orientation photo
                    if float(width) / float(height) > 1.333333:
                        delta = width - (1.333333 * height)
                        left = int(delta / 2)
                        upper = 0
                        right = width - left
                        lower = height
                    else:
                        delta = height - (width / 1.333333)
                        left = 0
                        upper = int(delta / 2)
                        right = width
                        lower = height - upper
                else:
                    # portrait orientation photo
                    if float(height) / float(width) > 1.333333:
                        delta = height - (1.333333 * width)
                        left = 0
                        upper = int(delta / 2)
                        right = width
                        lower = height - upper
                    else:
                        delta = width - (height / 1.333333)
                        left = int(delta / 2)
                        upper = 0
                        right = width - left
                        lower = height

                tmp_image = image.crop((left, upper, right, lower))
                if width >= height:
                    new_width = 480
                    new_height = 360
                else:
                    new_width = 360
                    new_height = 480
                tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
                f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '480x360_q95.jpg'), "w")
                # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                out_img = BytesIO()
                # You MUST specify the file type because there is no file name to discern it from
                tmp_image.save(out_img, format=extension, subsampling=0, quality=95)
                f_thumb.write(out_img.getvalue())
                f_thumb.close()

                # 320x240 thumbnail
                if width >= height:
                    # landscape orientation photo
                    if float(width) / float(height) > 1.333333:
                        delta = width - (1.333333 * height)
                        left = int(delta / 2)
                        upper = 0
                        right = width - left
                        lower = height
                    else:
                        delta = height - (width / 1.333333)
                        left = 0
                        upper = int(delta / 2)
                        right = width
                        lower = height - upper
                else:
                    # portrait orientation photo
                    if float(height) / float(width) > 1.333333:
                        delta = height - (1.333333 * width)
                        left = 0
                        upper = int(delta / 2)
                        right = width
                        lower = height - upper
                    else:
                        delta = width - (height / 1.333333)
                        left = int(delta / 2)
                        upper = 0
                        right = width - left
                        lower = height

                tmp_image = image.crop((left, upper, right, lower))
                if width >= height:
                    new_width = 320
                    new_height = 240
                else:
                    new_width = 240
                    new_height = 320
                tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
                f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '320x240_q95.jpg'), "w")
                # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                out_img = BytesIO()
                # You MUST specify the file type because there is no file name to discern it from
                tmp_image.save(out_img, format=extension, subsampling=0, quality=95)
                f_thumb.write(out_img.getvalue())
                f_thumb.close()

                # 120x90 thumbnail
                if width >= height:
                    # landscape orientation photo
                    if float(width) / float(height) > 1.333333:
                        delta = width - (1.333333 * height)
                        left = int(delta / 2)
                        upper = 0
                        right = width - left
                        lower = height
                    else:
                        delta = height - (width / 1.333333)
                        left = 0
                        upper = int(delta / 2)
                        right = width
                        lower = height - upper
                else:
                    # portrait orientation photo
                    if float(height) / float(width) > 1.333333:
                        delta = height - (1.333333 * width)
                        left = 0
                        upper = int(delta / 2)
                        right = width
                        lower = height - upper
                    else:
                        delta = width - (height / 1.333333)
                        left = int(delta / 2)
                        upper = 0
                        right = width - left
                        lower = height

                tmp_image = image.crop((left, upper, right, lower))
                if width >= height:
                    new_width = 120
                    new_height = 90
                else:
                    new_width = 90
                    new_height = 120
                tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '120x90_q95_crop.jpg'), "w")
                # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                out_img = BytesIO()
                # You MUST specify the file type because there is no file name to discern it from
                tmp_image.save(out_img, extension)
                f_thumb.write(out_img.getvalue())
                f_thumb.close()

                # 70x50 thumbnail
                if width >= height:
                    # landscape orientation photo
                    if float(width) / float(height) > 1.4:
                        delta = width - (1.4 * height)
                        left = int(delta / 2)
                        upper = 0
                        right = width - left
                        lower = height
                    else:
                        delta = height - (width / 1.4)
                        left = 0
                        upper = int(delta / 2)
                        right = width
                        lower = height - upper
                else:
                    # portrait orientation photo
                    if float(height) / float(width) > 1.4:
                        delta = height - (1.4 * width)
                        left = 0
                        upper = int(delta / 2)
                        right = width
                        lower = height - upper
                    else:
                        delta = width - (height / 1.4)
                        left = int(delta / 2)
                        upper = 0
                        right = width - left
                        lower = height

                tmp_image = image.crop((left, upper, right, lower))
                if width >= height:
                    new_width = 70
                    new_height = 50
                else:
                    new_width = 50
                    new_height = 70
                tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '70x50_q95_crop.jpg'), "w")
                # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                out_img = BytesIO()
                # You MUST specify the file type because there is no file name to discern it from
                tmp_image.save(out_img, extension)
                f_thumb.write(out_img.getvalue())
                f_thumb.close()

                # 910x680 thumbnail
                if width >= height:
                    # landscape orientation photo
                    if float(width) / float(height) > 1.338235:
                        # possible panoramic photo, let's keep aspect ratio intact
                        left = 0
                        upper = 0
                        right = width
                        lower = height
                    else:
                        delta = height - (width / 1.338235)
                        left = 0
                        upper = int(delta / 2)
                        right = width
                        lower = height - upper
                else:
                    # portrait orientation photo
                    if float(height) / float(width) > 1.338235:
                        delta = height - (1.338235 * width)
                        left = 0
                        upper = int(delta / 2)
                        right = width
                        lower = height - upper
                    else:
                        delta = width - (height / 1.338235)
                        left = int(delta / 2)
                        upper = 0
                        right = width - left
                        lower = height

                tmp_image = image.crop((left, upper, right, lower))
                if width >= height:
                    new_width = 910
                    if float(width) / float(height) > 1.338235:
                        # possible panoramic photo, let's keep aspect ratio intact
                        aspect_ratio = float(width) / float(height)
                        new_height = int(910 / aspect_ratio)
                    else:
                        new_height = 680
                else:
                    new_width = 680
                    new_height = 910
                tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
                f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '910x680_q95.jpg'), "w")
                # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                out_img = BytesIO()
                # You MUST specify the file type because there is no file name to discern it from
                tmp_image.save(out_img, format=extension, subsampling=0, quality=95)
                f_thumb.write(out_img.getvalue())
                f_thumb.close()

                # 745x500 thumbnail
                if width >= height:
                    # landscape orientation photo
                    if float(width) / float(height) > 1.49:
                        delta = width - (1.49 * height)
                        left = int(delta / 2)
                        upper = 0
                        right = width - left
                        lower = height
                    else:
                        delta = height - (width / 1.49)
                        left = 0
                        upper = int(delta / 2)
                        right = width
                        lower = height - upper
                else:
                    # portrait orientation photo
                    if float(height) / float(width) > 1.49:
                        delta = height - (1.49 * width)
                        left = 0
                        upper = int(delta / 2)
                        right = width
                        lower = height - upper
                    else:
                        delta = width - (height / 1.49)
                        left = int(delta / 2)
                        upper = 0
                        right = width - left
                        lower = height

                tmp_image = image.crop((left, upper, right, lower))
                if width >= height:
                    new_width = 745
                    new_height = 500
                else:
                    new_width = 500
                    new_height = 745
                tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)

                f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '745x500_q95_crop-top.jpg'), "w")
                # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                out_img = BytesIO()
                # You MUST specify the file type because there is no file name to discern it from
                tmp_image.save(out_img, extension)
                f_thumb.write(out_img.getvalue())
                f_thumb.close()

                sql = "update items_itemphoto set missing = false where id = %s"
                cursor = connection.cursor()
                cursor.execute(sql, [p['id']])


            except Exception as e:
                width,height = [-1,-1]

        self.stdout.write('END\n')

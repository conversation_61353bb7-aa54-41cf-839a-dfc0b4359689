#update items_item set description=NULL, saved_autodescription=NULL where "description" LIKE '%Contribute a summit log entry%'

from django.core.management.base import BaseCommand
from django.db.models import Q
from items.models import ItemsItemauditlogentry, Item

class Command(BaseCommand):
    args = ''
    help = ''

    def handle(self, *args, **options):
        for i in ItemsItemauditlogentry.objects.all():
            try:
                location = Item.objects.get(pk=i.id).location
                if i.location == location:
                    i.delete()
                else:
                    print("not equal")
            except:
                print("failed")
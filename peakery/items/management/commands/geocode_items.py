from items.models import Item
from cities.models import Country, Region
from django.core.management.base import BaseCommand
from django.db import connection
import sys

class Command(BaseCommand):
    help = 'Geocodes items.'
    args = '<country_code ...>'

    def dictfetchall(cursor):
        "Return all rows from a cursor as a dict"
        columns = [col[0] for col in cursor.description]
        return [
            dict(zip(columns, row))
            for row in cursor.fetchall()
        ]

    def handle(self, *args, **options):
        reload(sys)
        sys.setdefaultencoding('utf8')
        for country_code in args:
            self.stdout.write('Geocoding Items for...\n')

            import googlemaps
            import json
            from django.contrib.gis.geos import Point

            gmaps = googlemaps.Client(key="AIzaSyAjV7dkmG1WYHeHE0EnQvOV6YnPWpRxIzg")

            country_code = country_code.upper()

            sql = "select c.id, c.name, count(a.id) as peak_count " + \
                "from items_item a " + \
                "join items_item_country b on b.item_id = a.id " + \
                "join cities_country c on c.id = b.country_id " + \
                "where c.code = %s group by c.id, c.name"

            #custom query for unknown region peaks
            sql = "select " + \
                "c.id, " + \
                "c.name, " + \
                "count(a.id) as peak_count " + \
                "from items_item a " + \
                "join items_item_country b on b.item_id = a.id " + \
                "join cities_country c on c.id = b.country_id " + \
                "where not exists (select 1 from items_item_region x, cities_region y where x.item_id = a.id and y.id = x.region_id and y.country_id = c.id) " + \
                "and lower(c.code) = 'ca' " + \
                "group by c.id, c.name"

            country = Country.objects.raw(sql, [country_code])
            for c in country:
                total_peak_count = c.peak_count
                country_id = c.id

            sql = "select a.id, a.name, a.lat, a.long " + \
                "from items_item a " + \
                "join items_item_country b on b.item_id = a.id " + \
                "join cities_country c on c.id = b.country_id " + \
                "where c.code = %s "

            #custom query for unknown region peaks
            sql = "select " + \
                "a.id, " + \
                "a.name, " + \
                "a.lat, " + \
                "a.long, " + \
                "a.slug_new_text as slug, " + \
                "a.summitlog_count, " + \
                "a.elevation, " + \
                "a.prominence, " + \
                "string_agg(f.highlight, '~~' order by f.id) as peak_highlights, " + \
                "get_thumb(a.thumbnail, 745) as thumbnail_url " + \
                "from items_item a " + \
                "join items_item_country b on b.item_id = a.id " + \
                "join cities_country c on c.id = b.country_id " + \
                "left join items_itemhighlight f on f.item_id = a.id " + \
                "where not exists (select 1 from items_item_region x, cities_region y where x.item_id = a.id and y.id = x.region_id and y.country_id = c.id) " + \
                "and lower(c.code) = 'ca' " + \
                "group by a.id, a.name, a.slug_new_text, a.summitlog_count, a.elevation, a.prominence, thumbnail_url"

            peaks = Item.objects.raw(sql, [country_code])

            processed = 1
            for p in peaks:
                self.stdout.write('Processing: %s (%s of %s)' % (p, processed, total_peak_count))
                point = Point(float(p.long),float(p.lat))
                result = gmaps.reverse_geocode(("%s, %s" % (point.y, point.x)))
                for r in result:
                    region_name = None
                    country_name = None
                    region_match = False
                    if r['geometry']['location_type'] == "GEOMETRIC_CENTER" or r['geometry']['location_type'] == "APPROXIMATE":
                        for a in r['address_components']:
                            for t in a['types']:
                                if t == "country":
                                    if country_name != a['short_name']:
                                        country_name = a['short_name']
                                if t == "administrative_area_level_1":
                                    if region_name != a['long_name']:
                                        region_name = a['long_name']
                                        regions = Region.objects.filter(name=region_name, country_id=country_id)
                                        if regions:
                                            region_match = True
                                            #delete existing regions
                                            sql = "delete from items_item_region where item_id = %s "
                                            cursor = connection.cursor()
                                            cursor.execute(sql, [p.id])
                                            for region in regions:
                                                #add new region
                                                sql = "insert into items_item_region (item_id, region_id, rank, prominence_rank, summits_rank, geocoded) values (%s, %s, 0, 0, 0, true)"
                                                cursor = connection.cursor()
                                                cursor.execute(sql, [p.id, region.id])
                                        #log geocoder response
                                        sql = "delete from items_item_geocode where item_id = %s"
                                        cursor = connection.cursor()
                                        cursor.execute(sql, [p.id])
                                        sql = "insert into items_item_geocode (item_id, region_name, region_match, geocoder_response, country_name) values (%s, %s, %s, %s, %s)"
                                        cursor = connection.cursor()
                                        cursor.execute(sql, [p.id, region_name, region_match, json.dumps(result), country_name])
                processed += 1

        self.stdout.write('END\n')
